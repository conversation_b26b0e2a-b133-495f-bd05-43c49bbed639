{"ast": null, "code": "import React,{useEffect}from'react';import{BrowserRouter as Router,Routes,Route}from'react-router-dom';import LandingPage from'./components/LandingPage';import ZodiacPage from'./components/ZodiacPage';import AnalyticsDebugger from'./components/AnalyticsDebugger';import{TranslationProvider}from'./contexts/TranslationContext';import{useAnalytics}from'./hooks/useAnalytics';import{initGA,setUserProperties}from'./services/analytics';import'./App.css';// Cache busting utility\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const cacheBuster=Date.now();window.CACHE_VERSION=cacheBuster;const zodiacSigns=[{id:'aries',sinhala:'මේෂ',english:'Aries'},{id:'taurus',sinhala:'වෘෂභ',english:'Taurus'},{id:'gemini',sinhala:'මිථුන',english:'Gemini'},{id:'cancer',sinhala:'කටක',english:'Cancer'},{id:'leo',sinhala:'සිංහ',english:'Leo'},{id:'virgo',sinhala:'කන්‍යා',english:'Virgo'},{id:'libra',sinhala:'තුලා',english:'Libra'},{id:'scorpio',sinhala:'වෘශ්චික',english:'Scorpio'},{id:'sagittarius',sinhala:'ධනු',english:'Sagittarius'},{id:'capricorn',sinhala:'මකර',english:'Capricorn'},{id:'aquarius',sinhala:'කුම්භ',english:'Aquarius'},{id:'pisces',sinhala:'මීන',english:'Pisces'}];function App(){useEffect(()=>{// Initialize Google Analytics\nconst gaInitialized=initGA();if(gaInitialized){// Set initial user properties\nsetUserProperties({website_language:'sinhala',website_type:'astrology',content_category:'horoscope'});}// Disable right-click context menu\nconst handleContextMenu=e=>{e.preventDefault();return false;};// Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts\nconst handleKeyDown=e=>{// F12\nif(e.keyCode===123){e.preventDefault();return false;}// Ctrl+Shift+I (Developer Tools)\nif(e.ctrlKey&&e.shiftKey&&e.keyCode===73){e.preventDefault();return false;}// Ctrl+U (View Source)\nif(e.ctrlKey&&e.keyCode===85){e.preventDefault();return false;}// Ctrl+S (Save Page)\nif(e.ctrlKey&&e.keyCode===83){e.preventDefault();return false;}// Ctrl+A (Select All)\nif(e.ctrlKey&&e.keyCode===65){e.preventDefault();return false;}// Ctrl+C (Copy)\nif(e.ctrlKey&&e.keyCode===67){e.preventDefault();return false;}};// Disable drag and drop\nconst handleDragStart=e=>{e.preventDefault();return false;};// Add event listeners\ndocument.addEventListener('contextmenu',handleContextMenu);document.addEventListener('keydown',handleKeyDown);document.addEventListener('dragstart',handleDragStart);// Cleanup event listeners\nreturn()=>{document.removeEventListener('contextmenu',handleContextMenu);document.removeEventListener('keydown',handleKeyDown);document.removeEventListener('dragstart',handleDragStart);};},[]);return/*#__PURE__*/_jsx(TranslationProvider,{children:/*#__PURE__*/_jsx(Router,{future:{v7_startTransition:true,v7_relativeSplatPath:true},children:/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(LandingPage,{zodiacSigns:zodiacSigns})}),zodiacSigns.map(sign=>/*#__PURE__*/_jsx(Route,{path:\"/\".concat(sign.id),element:/*#__PURE__*/_jsx(ZodiacPage,{sign:sign})},sign.id))]}),/*#__PURE__*/_jsx(AnalyticsDebugger,{})]})})});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LandingPage", "ZodiacPage", "AnalyticsDebugger", "TranslationProvider", "useAnalytics", "initGA", "setUserProperties", "jsx", "_jsx", "jsxs", "_jsxs", "cacheBuster", "Date", "now", "window", "CACHE_VERSION", "zodiacSigns", "id", "sinhala", "english", "App", "gaInitialized", "website_language", "website_type", "content_category", "handleContextMenu", "e", "preventDefault", "handleKeyDown", "keyCode", "ctrl<PERSON>ey", "shift<PERSON>ey", "handleDragStart", "document", "addEventListener", "removeEventListener", "children", "future", "v7_startTransition", "v7_relativeSplatPath", "className", "path", "element", "map", "sign", "concat"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './components/LandingPage';\nimport ZodiacPage from './components/ZodiacPage';\nimport AnalyticsDebugger from './components/AnalyticsDebugger';\nimport { TranslationProvider } from './contexts/TranslationContext';\nimport { useAnalytics } from './hooks/useAnalytics';\nimport { initGA, setUserProperties } from './services/analytics';\nimport './App.css';\n\n// Cache busting utility\nconst cacheBuster = Date.now();\nwindow.CACHE_VERSION = cacheBuster;\n\nconst zodiacSigns = [\n  { id: 'aries', sinhala: 'මේෂ', english: 'Aries' },\n  { id: 'taurus', sinhala: 'වෘෂභ', english: 'Taurus' },\n  { id: 'gemini', sinhala: 'මිථුන', english: 'Gemini' },\n  { id: 'cancer', sinhala: 'කටක', english: 'Cancer' },\n  { id: 'leo', sinhala: 'සිංහ', english: 'Leo' },\n  { id: 'virgo', sinhala: 'කන්‍යා', english: 'Virgo' },\n  { id: 'libra', sinhala: 'තුලා', english: 'Libra' },\n  { id: 'scorpio', sinhala: 'වෘශ්චික', english: 'Scorpio' },\n  { id: 'sagittarius', sinhala: 'ධනු', english: 'Sagittarius' },\n  { id: 'capricorn', sinhala: 'මකර', english: 'Capricorn' },\n  { id: 'aquarius', sinhala: 'කුම්භ', english: 'Aquarius' },\n  { id: 'pisces', sinhala: 'මීන', english: 'Pisces' }\n];\n\nfunction App() {\n  useEffect(() => {\n    // Initialize Google Analytics\n    const gaInitialized = initGA();\n\n    if (gaInitialized) {\n      // Set initial user properties\n      setUserProperties({\n        website_language: 'sinhala',\n        website_type: 'astrology',\n        content_category: 'horoscope'\n      });\n    }\n\n    // Disable right-click context menu\n    const handleContextMenu = (e) => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts\n    const handleKeyDown = (e) => {\n      // F12\n      if (e.keyCode === 123) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+Shift+I (Developer Tools)\n      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+U (View Source)\n      if (e.ctrlKey && e.keyCode === 85) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+S (Save Page)\n      if (e.ctrlKey && e.keyCode === 83) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+A (Select All)\n      if (e.ctrlKey && e.keyCode === 65) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+C (Copy)\n      if (e.ctrlKey && e.keyCode === 67) {\n        e.preventDefault();\n        return false;\n      }\n    };\n\n    // Disable drag and drop\n    const handleDragStart = (e) => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Add event listeners\n    document.addEventListener('contextmenu', handleContextMenu);\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('dragstart', handleDragStart);\n\n    // Cleanup event listeners\n    return () => {\n      document.removeEventListener('contextmenu', handleContextMenu);\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('dragstart', handleDragStart);\n    };\n  }, []);\n\n  return (\n    <TranslationProvider>\n      <Router future={{\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      }}>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<LandingPage zodiacSigns={zodiacSigns} />} />\n            {zodiacSigns.map(sign => (\n              <Route\n                key={sign.id}\n                path={`/${sign.id}`}\n                element={<ZodiacPage sign={sign} />}\n              />\n            ))}\n          </Routes>\n\n          {/* Analytics Debugger - only shows in development */}\n          <AnalyticsDebugger />\n        </div>\n      </Router>\n    </TranslationProvider>\n  );\n}\n\nexport default App;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,KAAQ,kBAAkB,CACzE,MAAO,CAAAC,WAAW,KAAM,0BAA0B,CAClD,MAAO,CAAAC,UAAU,KAAM,yBAAyB,CAChD,MAAO,CAAAC,iBAAiB,KAAM,gCAAgC,CAC9D,OAASC,mBAAmB,KAAQ,+BAA+B,CACnE,OAASC,YAAY,KAAQ,sBAAsB,CACnD,OAASC,MAAM,CAAEC,iBAAiB,KAAQ,sBAAsB,CAChE,MAAO,WAAW,CAElB;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,WAAW,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CAC9BC,MAAM,CAACC,aAAa,CAAGJ,WAAW,CAElC,KAAM,CAAAK,WAAW,CAAG,CAClB,CAAEC,EAAE,CAAE,OAAO,CAAEC,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE,OAAQ,CAAC,CACjD,CAAEF,EAAE,CAAE,QAAQ,CAAEC,OAAO,CAAE,MAAM,CAAEC,OAAO,CAAE,QAAS,CAAC,CACpD,CAAEF,EAAE,CAAE,QAAQ,CAAEC,OAAO,CAAE,OAAO,CAAEC,OAAO,CAAE,QAAS,CAAC,CACrD,CAAEF,EAAE,CAAE,QAAQ,CAAEC,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAS,CAAC,CACnD,CAAEF,EAAE,CAAE,KAAK,CAAEC,OAAO,CAAE,MAAM,CAAEC,OAAO,CAAE,KAAM,CAAC,CAC9C,CAAEF,EAAE,CAAE,OAAO,CAAEC,OAAO,CAAE,QAAQ,CAAEC,OAAO,CAAE,OAAQ,CAAC,CACpD,CAAEF,EAAE,CAAE,OAAO,CAAEC,OAAO,CAAE,MAAM,CAAEC,OAAO,CAAE,OAAQ,CAAC,CAClD,CAAEF,EAAE,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAS,CAAEC,OAAO,CAAE,SAAU,CAAC,CACzD,CAAEF,EAAE,CAAE,aAAa,CAAEC,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE,aAAc,CAAC,CAC7D,CAAEF,EAAE,CAAE,WAAW,CAAEC,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE,WAAY,CAAC,CACzD,CAAEF,EAAE,CAAE,UAAU,CAAEC,OAAO,CAAE,OAAO,CAAEC,OAAO,CAAE,UAAW,CAAC,CACzD,CAAEF,EAAE,CAAE,QAAQ,CAAEC,OAAO,CAAE,KAAK,CAAEC,OAAO,CAAE,QAAS,CAAC,CACpD,CAED,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACbzB,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA0B,aAAa,CAAGhB,MAAM,CAAC,CAAC,CAE9B,GAAIgB,aAAa,CAAE,CACjB;AACAf,iBAAiB,CAAC,CAChBgB,gBAAgB,CAAE,SAAS,CAC3BC,YAAY,CAAE,WAAW,CACzBC,gBAAgB,CAAE,WACpB,CAAC,CAAC,CACJ,CAEA;AACA,KAAM,CAAAC,iBAAiB,CAAIC,CAAC,EAAK,CAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAIF,CAAC,EAAK,CAC3B;AACA,GAAIA,CAAC,CAACG,OAAO,GAAK,GAAG,CAAE,CACrBH,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CACA;AACA,GAAID,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACK,QAAQ,EAAIL,CAAC,CAACG,OAAO,GAAK,EAAE,CAAE,CAC/CH,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CACA;AACA,GAAID,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACG,OAAO,GAAK,EAAE,CAAE,CACjCH,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CACA;AACA,GAAID,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACG,OAAO,GAAK,EAAE,CAAE,CACjCH,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CACA;AACA,GAAID,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACG,OAAO,GAAK,EAAE,CAAE,CACjCH,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CACA;AACA,GAAID,CAAC,CAACI,OAAO,EAAIJ,CAAC,CAACG,OAAO,GAAK,EAAE,CAAE,CACjCH,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CACF,CAAC,CAED;AACA,KAAM,CAAAK,eAAe,CAAIN,CAAC,EAAK,CAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC,CAClB,MAAO,MAAK,CACd,CAAC,CAED;AACAM,QAAQ,CAACC,gBAAgB,CAAC,aAAa,CAAET,iBAAiB,CAAC,CAC3DQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,CAAEN,aAAa,CAAC,CACnDK,QAAQ,CAACC,gBAAgB,CAAC,WAAW,CAAEF,eAAe,CAAC,CAEvD;AACA,MAAO,IAAM,CACXC,QAAQ,CAACE,mBAAmB,CAAC,aAAa,CAAEV,iBAAiB,CAAC,CAC9DQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,CAAEP,aAAa,CAAC,CACtDK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,CAAEH,eAAe,CAAC,CAC5D,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN,mBACExB,IAAA,CAACL,mBAAmB,EAAAiC,QAAA,cAClB5B,IAAA,CAACX,MAAM,EAACwC,MAAM,CAAE,CACdC,kBAAkB,CAAE,IAAI,CACxBC,oBAAoB,CAAE,IACxB,CAAE,CAAAH,QAAA,cACA1B,KAAA,QAAK8B,SAAS,CAAC,KAAK,CAAAJ,QAAA,eAClB1B,KAAA,CAACZ,MAAM,EAAAsC,QAAA,eACL5B,IAAA,CAACT,KAAK,EAAC0C,IAAI,CAAC,GAAG,CAACC,OAAO,cAAElC,IAAA,CAACR,WAAW,EAACgB,WAAW,CAAEA,WAAY,CAAE,CAAE,CAAE,CAAC,CACrEA,WAAW,CAAC2B,GAAG,CAACC,IAAI,eACnBpC,IAAA,CAACT,KAAK,EAEJ0C,IAAI,KAAAI,MAAA,CAAMD,IAAI,CAAC3B,EAAE,CAAG,CACpByB,OAAO,cAAElC,IAAA,CAACP,UAAU,EAAC2C,IAAI,CAAEA,IAAK,CAAE,CAAE,EAF/BA,IAAI,CAAC3B,EAGX,CACF,CAAC,EACI,CAAC,cAGTT,IAAA,CAACN,iBAAiB,GAAE,CAAC,EAClB,CAAC,CACA,CAAC,CACU,CAAC,CAE1B,CAEA,cAAe,CAAAkB,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}