{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\TranslationTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationTest = () => {\n  _s();\n  const {\n    currentLanguage,\n    translateText,\n    changeLanguage,\n    isTranslating\n  } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n\n      // Test direct API call first\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API URL:', apiUrl);\n      console.log('NODE_ENV:', process.env.NODE_ENV);\n      console.log('REACT_APP_API_URL:', process.env.REACT_APP_API_URL);\n      try {\n        console.log('Making fetch request to:', `${apiUrl}/translate`);\n        const requestBody = {\n          text: 'ආදරය',\n          targetLanguage: currentLanguage,\n          targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',\n          context: 'test'\n        };\n        console.log('Request body:', requestBody);\n        const response = await fetch(`${apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify(requestBody)\n        });\n        console.log('Direct API response status:', response.status);\n        console.log('Direct API response headers:', response.headers);\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n        const data = await response.json();\n        console.log('Direct API response data:', data);\n        if (data.success) {\n          setTestResult(`Direct API: ${data.translatedText}`);\n        } else {\n          setError(`Direct API Error: ${data.error}`);\n        }\n      } catch (apiError) {\n        console.error('Direct API error:', apiError);\n        setError(`Direct API Error: ${apiError.message}`);\n      }\n\n      // Also test through translation context\n      try {\n        console.log('Testing through translation context...');\n        const result = await translateText('ආදරය', currentLanguage, 'test');\n        console.log('Context translation result:', result);\n        setTestResult(prev => prev + ` | Context: ${result}`);\n      } catch (contextError) {\n        console.error('Context translation error:', contextError);\n        setError(prev => prev + ` | Context Error: ${contextError.message}`);\n      }\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Translation Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 101,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Current Language: \", currentLanguage]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 102,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Is Translating: \", isTranslating ? 'Yes' : 'No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('sinhala'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Sinhala\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('english'),\n        style: {\n          margin: '5px'\n        },\n        children: \"English\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('tamil'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Tamil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: testTranslation,\n      disabled: isTranslating,\n      children: \"Test Translation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'lightgreen'\n      },\n      children: testResult\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'red'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 128,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 90,\n    columnNumber: 5\n  }, this);\n};\n_s(TranslationTest, \"ofjRKXIuHGl7R9hpsA1r9bfRQW4=\", false, function () {\n  return [useTranslation];\n});\n_c = TranslationTest;\nexport default TranslationTest;\nvar _c;\n$RefreshReg$(_c, \"TranslationTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "jsxDEV", "_jsxDEV", "TranslationTest", "_s", "currentLanguage", "translateText", "changeLanguage", "isTranslating", "testResult", "setTestResult", "error", "setError", "testTranslation", "console", "log", "apiUrl", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "requestBody", "text", "targetLanguage", "targetLanguageName", "context", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "status", "ok", "Error", "data", "json", "success", "translatedText", "apiError", "message", "result", "prev", "contextError", "err", "style", "position", "top", "left", "background", "color", "padding", "borderRadius", "zIndex", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "margin", "disabled", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/TranslationTest.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\nconst TranslationTest = () => {\n  const { currentLanguage, translateText, changeLanguage, isTranslating } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n\n      // Test direct API call first\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API URL:', apiUrl);\n      console.log('NODE_ENV:', process.env.NODE_ENV);\n      console.log('REACT_APP_API_URL:', process.env.REACT_APP_API_URL);\n\n      try {\n        console.log('Making fetch request to:', `${apiUrl}/translate`);\n        const requestBody = {\n          text: 'ආදරය',\n          targetLanguage: currentLanguage,\n          targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',\n          context: 'test'\n        };\n        console.log('Request body:', requestBody);\n\n        const response = await fetch(`${apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify(requestBody)\n        });\n\n        console.log('Direct API response status:', response.status);\n        console.log('Direct API response headers:', response.headers);\n\n        if (!response.ok) {\n          throw new Error(`HTTP error! status: ${response.status}`);\n        }\n\n        const data = await response.json();\n        console.log('Direct API response data:', data);\n\n        if (data.success) {\n          setTestResult(`Direct API: ${data.translatedText}`);\n        } else {\n          setError(`Direct API Error: ${data.error}`);\n        }\n      } catch (apiError) {\n        console.error('Direct API error:', apiError);\n        setError(`Direct API Error: ${apiError.message}`);\n      }\n\n      // Also test through translation context\n      try {\n        console.log('Testing through translation context...');\n        const result = await translateText('ආදරය', currentLanguage, 'test');\n        console.log('Context translation result:', result);\n        setTestResult(prev => prev + ` | Context: ${result}`);\n      } catch (contextError) {\n        console.error('Context translation error:', contextError);\n        setError(prev => prev + ` | Context Error: ${contextError.message}`);\n      }\n\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    }}>\n      <h3>Translation Test</h3>\n      <p>Current Language: {currentLanguage}</p>\n      <p>Is Translating: {isTranslating ? 'Yes' : 'No'}</p>\n      \n      <div style={{ marginBottom: '10px' }}>\n        <button onClick={() => changeLanguage('sinhala')} style={{ margin: '5px' }}>\n          Sinhala\n        </button>\n        <button onClick={() => changeLanguage('english')} style={{ margin: '5px' }}>\n          English\n        </button>\n        <button onClick={() => changeLanguage('tamil')} style={{ margin: '5px' }}>\n          Tamil\n        </button>\n      </div>\n      \n      <button onClick={testTranslation} disabled={isTranslating}>\n        Test Translation\n      </button>\n      \n      {testResult && (\n        <div style={{ marginTop: '10px', color: 'lightgreen' }}>\n          {testResult}\n        </div>\n      )}\n      \n      {error && (\n        <div style={{ marginTop: '10px', color: 'red' }}>\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TranslationTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,eAAe;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC1F,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFD,QAAQ,CAAC,EAAE,CAAC;MACZF,aAAa,CAAC,YAAY,CAAC;MAE3BI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;MACjDS,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,OAAOT,aAAa,CAAC;MAEpE,IAAID,eAAe,KAAK,SAAS,EAAE;QACjCK,aAAa,CAAC,oDAAoD,CAAC;QACnE;MACF;;MAEA;MACA,MAAMM,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MAC3EL,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,MAAM,CAAC;MAC/BF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEE,OAAO,CAACC,GAAG,CAACE,QAAQ,CAAC;MAC9CN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;MAEhE,IAAI;QACFL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAE,GAAGC,MAAM,YAAY,CAAC;QAC9D,MAAMK,WAAW,GAAG;UAClBC,IAAI,EAAE,MAAM;UACZC,cAAc,EAAElB,eAAe;UAC/BmB,kBAAkB,EAAEnB,eAAe,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;UACvEoB,OAAO,EAAE;QACX,CAAC;QACDX,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEM,WAAW,CAAC;QAEzC,MAAMK,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGX,MAAM,YAAY,EAAE;UAClDY,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACX,WAAW;QAClC,CAAC,CAAC;QAEFP,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEW,QAAQ,CAACO,MAAM,CAAC;QAC3DnB,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAEW,QAAQ,CAACG,OAAO,CAAC;QAE7D,IAAI,CAACH,QAAQ,CAACQ,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,uBAAuBT,QAAQ,CAACO,MAAM,EAAE,CAAC;QAC3D;QAEA,MAAMG,IAAI,GAAG,MAAMV,QAAQ,CAACW,IAAI,CAAC,CAAC;QAClCvB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEqB,IAAI,CAAC;QAE9C,IAAIA,IAAI,CAACE,OAAO,EAAE;UAChB5B,aAAa,CAAC,eAAe0B,IAAI,CAACG,cAAc,EAAE,CAAC;QACrD,CAAC,MAAM;UACL3B,QAAQ,CAAC,qBAAqBwB,IAAI,CAACzB,KAAK,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC,OAAO6B,QAAQ,EAAE;QACjB1B,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAE6B,QAAQ,CAAC;QAC5C5B,QAAQ,CAAC,qBAAqB4B,QAAQ,CAACC,OAAO,EAAE,CAAC;MACnD;;MAEA;MACA,IAAI;QACF3B,OAAO,CAACC,GAAG,CAAC,wCAAwC,CAAC;QACrD,MAAM2B,MAAM,GAAG,MAAMpC,aAAa,CAAC,MAAM,EAAED,eAAe,EAAE,MAAM,CAAC;QACnES,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAE2B,MAAM,CAAC;QAClDhC,aAAa,CAACiC,IAAI,IAAIA,IAAI,GAAG,eAAeD,MAAM,EAAE,CAAC;MACvD,CAAC,CAAC,OAAOE,YAAY,EAAE;QACrB9B,OAAO,CAACH,KAAK,CAAC,4BAA4B,EAAEiC,YAAY,CAAC;QACzDhC,QAAQ,CAAC+B,IAAI,IAAIA,IAAI,GAAG,qBAAqBC,YAAY,CAACH,OAAO,EAAE,CAAC;MACtE;IAEF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ/B,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAEkC,GAAG,CAAC;MAC7CjC,QAAQ,CAAC,UAAUiC,GAAG,CAACJ,OAAO,EAAE,CAAC;IACnC;EACF,CAAC;EAED1C,SAAS,CAAC,MAAM;IACde,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;IACjDS,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,aAAa,CAAC;EAC/C,CAAC,EAAE,CAACH,eAAe,EAAEG,aAAa,CAAC,CAAC;EAEpC,oBACEN,OAAA;IAAK4C,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,iBAAiB;MAC7BC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACAtD,OAAA;MAAAsD,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzB1D,OAAA;MAAAsD,QAAA,GAAG,oBAAkB,EAACnD,eAAe;IAAA;MAAAoD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1C1D,OAAA;MAAAsD,QAAA,GAAG,kBAAgB,EAAChD,aAAa,GAAG,KAAK,GAAG,IAAI;IAAA;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErD1D,OAAA;MAAK4C,KAAK,EAAE;QAAEe,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnCtD,OAAA;QAAQ4D,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAAC,SAAS,CAAE;QAACuC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1D,OAAA;QAAQ4D,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAAC,SAAS,CAAE;QAACuC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1D,OAAA;QAAQ4D,OAAO,EAAEA,CAAA,KAAMvD,cAAc,CAAC,OAAO,CAAE;QAACuC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE1E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN1D,OAAA;MAAQ4D,OAAO,EAAEjD,eAAgB;MAACmD,QAAQ,EAAExD,aAAc;MAAAgD,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAERnD,UAAU,iBACTP,OAAA;MAAK4C,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAa,CAAE;MAAAK,QAAA,EACpD/C;IAAU;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,EAEAjD,KAAK,iBACJT,OAAA;MAAK4C,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAM,CAAE;MAAAK,QAAA,EAC7C7C;IAAK;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACxD,EAAA,CAlIID,eAAe;EAAA,QACuDH,cAAc;AAAA;AAAAkE,EAAA,GADpF/D,eAAe;AAoIrB,eAAeA,eAAe;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}