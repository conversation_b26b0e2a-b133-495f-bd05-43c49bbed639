{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\BackgroundMusic.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst BackgroundMusic = () => {\n  _s();\n  const [isMuted, setIsMuted] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [hasUserInteracted, setHasUserInteracted] = useState(false);\n  const audioRef = useRef(null);\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    // Set up audio properties\n    audio.loop = true;\n    audio.volume = 0.3; // Set to 30% volume\n    audio.preload = 'auto';\n    const handleCanPlayThrough = () => {\n      setIsLoaded(true);\n    };\n    const handleLoadedData = () => {\n      setIsLoaded(true);\n    };\n\n    // Add event listeners\n    audio.addEventListener('canplaythrough', handleCanPlayThrough);\n    audio.addEventListener('loadeddata', handleLoadedData);\n\n    // Cleanup\n    return () => {\n      audio.removeEventListener('canplaythrough', handleCanPlayThrough);\n      audio.removeEventListener('loadeddata', handleLoadedData);\n    };\n  }, []);\n\n  // Handle user interaction to enable autoplay\n  useEffect(() => {\n    const handleUserInteraction = () => {\n      setHasUserInteracted(true);\n\n      // Try to play the audio after user interaction\n      if (audioRef.current && isLoaded && !isMuted) {\n        audioRef.current.play().catch(error => {\n          console.log('Audio autoplay failed:', error);\n        });\n      }\n    };\n\n    // Listen for any user interaction\n    const events = ['click', 'touchstart', 'keydown'];\n    events.forEach(event => {\n      document.addEventListener(event, handleUserInteraction, {\n        once: true\n      });\n    });\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleUserInteraction);\n      });\n    };\n  }, [isLoaded, isMuted]);\n\n  // Auto-play when loaded and user has interacted\n  useEffect(() => {\n    if (audioRef.current && isLoaded && hasUserInteracted && !isMuted) {\n      audioRef.current.play().catch(error => {\n        console.log('Audio play failed:', error);\n      });\n    }\n  }, [isLoaded, hasUserInteracted, isMuted]);\n  const toggleMute = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n    if (isMuted) {\n      // Unmute\n      audio.muted = false;\n      setIsMuted(false);\n      if (hasUserInteracted) {\n        audio.play().catch(error => {\n          console.log('Audio play failed:', error);\n        });\n      }\n    } else {\n      // Mute\n      audio.muted = true;\n      audio.pause();\n      setIsMuted(true);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"audio\", {\n      ref: audioRef,\n      src: \"/music.mp3\",\n      preload: \"auto\",\n      style: {\n        display: 'none'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 96,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: toggleMute,\n      style: {\n        position: 'fixed',\n        bottom: '20px',\n        right: '20px',\n        width: '50px',\n        height: '50px',\n        borderRadius: '50%',\n        border: 'none',\n        background: 'rgba(244, 208, 63, 0.9)',\n        color: '#1a1a1a',\n        fontSize: '20px',\n        cursor: 'pointer',\n        zIndex: 1000,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center',\n        boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',\n        transition: 'all 0.3s ease',\n        backdropFilter: 'blur(10px)',\n        WebkitBackdropFilter: 'blur(10px)'\n      },\n      onMouseEnter: e => {\n        e.target.style.transform = 'scale(1.1)';\n        e.target.style.background = 'rgba(244, 208, 63, 1)';\n      },\n      onMouseLeave: e => {\n        e.target.style.transform = 'scale(1)';\n        e.target.style.background = 'rgba(244, 208, 63, 0.9)';\n      },\n      title: isMuted ? 'Unmute Music' : 'Mute Music',\n      children: isMuted ? '🔇' : '🎵'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @media (max-width: 768px) {\n          button {\n            bottom: 15px !important;\n            right: 15px !important;\n            width: 45px !important;\n            height: 45px !important;\n            fontSize: 18px !important;\n          }\n        }\n\n        @media (max-width: 480px) {\n          button {\n            bottom: 10px !important;\n            right: 10px !important;\n            width: 40px !important;\n            height: 40px !important;\n            fontSize: 16px !important;\n          }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(BackgroundMusic, \"YxRZfoaFsIFeklPMy84Ax5LxIOk=\");\n_c = BackgroundMusic;\nexport default BackgroundMusic;\nvar _c;\n$RefreshReg$(_c, \"BackgroundMusic\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "BackgroundMusic", "_s", "isMuted", "setIsMuted", "isLoaded", "setIsLoaded", "hasUserInteracted", "setHasUserInteracted", "audioRef", "audio", "current", "loop", "volume", "preload", "handleCanPlayThrough", "handleLoadedData", "addEventListener", "removeEventListener", "handleUserInteraction", "play", "catch", "error", "console", "log", "events", "for<PERSON>ach", "event", "document", "once", "toggleMute", "muted", "pause", "children", "ref", "src", "style", "display", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "position", "bottom", "right", "width", "height", "borderRadius", "border", "background", "color", "fontSize", "cursor", "zIndex", "alignItems", "justifyContent", "boxShadow", "transition", "<PERSON><PERSON>ilter", "WebkitBackdropFilter", "onMouseEnter", "e", "target", "transform", "onMouseLeave", "title", "jsx", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/BackgroundMusic.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\n\nconst BackgroundMusic = () => {\n  const [isMuted, setIsMuted] = useState(false);\n  const [isLoaded, setIsLoaded] = useState(false);\n  const [hasUserInteracted, setHasUserInteracted] = useState(false);\n  const audioRef = useRef(null);\n\n  useEffect(() => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    // Set up audio properties\n    audio.loop = true;\n    audio.volume = 0.3; // Set to 30% volume\n    audio.preload = 'auto';\n\n    const handleCanPlayThrough = () => {\n      setIsLoaded(true);\n    };\n\n    const handleLoadedData = () => {\n      setIsLoaded(true);\n    };\n\n    // Add event listeners\n    audio.addEventListener('canplaythrough', handleCanPlayThrough);\n    audio.addEventListener('loadeddata', handleLoadedData);\n\n    // Cleanup\n    return () => {\n      audio.removeEventListener('canplaythrough', handleCanPlayThrough);\n      audio.removeEventListener('loadeddata', handleLoadedData);\n    };\n  }, []);\n\n  // Handle user interaction to enable autoplay\n  useEffect(() => {\n    const handleUserInteraction = () => {\n      setHasUserInteracted(true);\n      \n      // Try to play the audio after user interaction\n      if (audioRef.current && isLoaded && !isMuted) {\n        audioRef.current.play().catch(error => {\n          console.log('Audio autoplay failed:', error);\n        });\n      }\n    };\n\n    // Listen for any user interaction\n    const events = ['click', 'touchstart', 'keydown'];\n    events.forEach(event => {\n      document.addEventListener(event, handleUserInteraction, { once: true });\n    });\n\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleUserInteraction);\n      });\n    };\n  }, [isLoaded, isMuted]);\n\n  // Auto-play when loaded and user has interacted\n  useEffect(() => {\n    if (audioRef.current && isLoaded && hasUserInteracted && !isMuted) {\n      audioRef.current.play().catch(error => {\n        console.log('Audio play failed:', error);\n      });\n    }\n  }, [isLoaded, hasUserInteracted, isMuted]);\n\n  const toggleMute = () => {\n    const audio = audioRef.current;\n    if (!audio) return;\n\n    if (isMuted) {\n      // Unmute\n      audio.muted = false;\n      setIsMuted(false);\n      if (hasUserInteracted) {\n        audio.play().catch(error => {\n          console.log('Audio play failed:', error);\n        });\n      }\n    } else {\n      // Mute\n      audio.muted = true;\n      audio.pause();\n      setIsMuted(true);\n    }\n  };\n\n  return (\n    <>\n      {/* Audio element */}\n      <audio\n        ref={audioRef}\n        src=\"/music.mp3\"\n        preload=\"auto\"\n        style={{ display: 'none' }}\n      />\n      \n      {/* Mute/Unmute Button */}\n      <button\n        onClick={toggleMute}\n        style={{\n          position: 'fixed',\n          bottom: '20px',\n          right: '20px',\n          width: '50px',\n          height: '50px',\n          borderRadius: '50%',\n          border: 'none',\n          background: 'rgba(244, 208, 63, 0.9)',\n          color: '#1a1a1a',\n          fontSize: '20px',\n          cursor: 'pointer',\n          zIndex: 1000,\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          boxShadow: '0 4px 15px rgba(0, 0, 0, 0.3)',\n          transition: 'all 0.3s ease',\n          backdropFilter: 'blur(10px)',\n          WebkitBackdropFilter: 'blur(10px)'\n        }}\n        onMouseEnter={(e) => {\n          e.target.style.transform = 'scale(1.1)';\n          e.target.style.background = 'rgba(244, 208, 63, 1)';\n        }}\n        onMouseLeave={(e) => {\n          e.target.style.transform = 'scale(1)';\n          e.target.style.background = 'rgba(244, 208, 63, 0.9)';\n        }}\n        title={isMuted ? 'Unmute Music' : 'Mute Music'}\n      >\n        {isMuted ? '🔇' : '🎵'}\n      </button>\n\n      {/* Mobile responsive styles */}\n      <style jsx>{`\n        @media (max-width: 768px) {\n          button {\n            bottom: 15px !important;\n            right: 15px !important;\n            width: 45px !important;\n            height: 45px !important;\n            fontSize: 18px !important;\n          }\n        }\n\n        @media (max-width: 480px) {\n          button {\n            bottom: 10px !important;\n            right: 10px !important;\n            width: 40px !important;\n            height: 40px !important;\n            fontSize: 16px !important;\n          }\n        }\n      `}</style>\n    </>\n  );\n};\n\nexport default BackgroundMusic;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE3D,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACW,QAAQ,EAAEC,WAAW,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM,CAACa,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAMe,QAAQ,GAAGb,MAAM,CAAC,IAAI,CAAC;EAE7BD,SAAS,CAAC,MAAM;IACd,MAAMe,KAAK,GAAGD,QAAQ,CAACE,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;;IAEZ;IACAA,KAAK,CAACE,IAAI,GAAG,IAAI;IACjBF,KAAK,CAACG,MAAM,GAAG,GAAG,CAAC,CAAC;IACpBH,KAAK,CAACI,OAAO,GAAG,MAAM;IAEtB,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;MACjCT,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;IAED,MAAMU,gBAAgB,GAAGA,CAAA,KAAM;MAC7BV,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC;;IAED;IACAI,KAAK,CAACO,gBAAgB,CAAC,gBAAgB,EAAEF,oBAAoB,CAAC;IAC9DL,KAAK,CAACO,gBAAgB,CAAC,YAAY,EAAED,gBAAgB,CAAC;;IAEtD;IACA,OAAO,MAAM;MACXN,KAAK,CAACQ,mBAAmB,CAAC,gBAAgB,EAAEH,oBAAoB,CAAC;MACjEL,KAAK,CAACQ,mBAAmB,CAAC,YAAY,EAAEF,gBAAgB,CAAC;IAC3D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACArB,SAAS,CAAC,MAAM;IACd,MAAMwB,qBAAqB,GAAGA,CAAA,KAAM;MAClCX,oBAAoB,CAAC,IAAI,CAAC;;MAE1B;MACA,IAAIC,QAAQ,CAACE,OAAO,IAAIN,QAAQ,IAAI,CAACF,OAAO,EAAE;QAC5CM,QAAQ,CAACE,OAAO,CAACS,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;UACrCC,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEF,KAAK,CAAC;QAC9C,CAAC,CAAC;MACJ;IACF,CAAC;;IAED;IACA,MAAMG,MAAM,GAAG,CAAC,OAAO,EAAE,YAAY,EAAE,SAAS,CAAC;IACjDA,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;MACtBC,QAAQ,CAACX,gBAAgB,CAACU,KAAK,EAAER,qBAAqB,EAAE;QAAEU,IAAI,EAAE;MAAK,CAAC,CAAC;IACzE,CAAC,CAAC;IAEF,OAAO,MAAM;MACXJ,MAAM,CAACC,OAAO,CAACC,KAAK,IAAI;QACtBC,QAAQ,CAACV,mBAAmB,CAACS,KAAK,EAAER,qBAAqB,CAAC;MAC5D,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACd,QAAQ,EAAEF,OAAO,CAAC,CAAC;;EAEvB;EACAR,SAAS,CAAC,MAAM;IACd,IAAIc,QAAQ,CAACE,OAAO,IAAIN,QAAQ,IAAIE,iBAAiB,IAAI,CAACJ,OAAO,EAAE;MACjEM,QAAQ,CAACE,OAAO,CAACS,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;QACrCC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,KAAK,CAAC;MAC1C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACjB,QAAQ,EAAEE,iBAAiB,EAAEJ,OAAO,CAAC,CAAC;EAE1C,MAAM2B,UAAU,GAAGA,CAAA,KAAM;IACvB,MAAMpB,KAAK,GAAGD,QAAQ,CAACE,OAAO;IAC9B,IAAI,CAACD,KAAK,EAAE;IAEZ,IAAIP,OAAO,EAAE;MACX;MACAO,KAAK,CAACqB,KAAK,GAAG,KAAK;MACnB3B,UAAU,CAAC,KAAK,CAAC;MACjB,IAAIG,iBAAiB,EAAE;QACrBG,KAAK,CAACU,IAAI,CAAC,CAAC,CAACC,KAAK,CAACC,KAAK,IAAI;UAC1BC,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEF,KAAK,CAAC;QAC1C,CAAC,CAAC;MACJ;IACF,CAAC,MAAM;MACL;MACAZ,KAAK,CAACqB,KAAK,GAAG,IAAI;MAClBrB,KAAK,CAACsB,KAAK,CAAC,CAAC;MACb5B,UAAU,CAAC,IAAI,CAAC;IAClB;EACF,CAAC;EAED,oBACEN,OAAA,CAAAE,SAAA;IAAAiC,QAAA,gBAEEnC,OAAA;MACEoC,GAAG,EAAEzB,QAAS;MACd0B,GAAG,EAAC,YAAY;MAChBrB,OAAO,EAAC,MAAM;MACdsB,KAAK,EAAE;QAAEC,OAAO,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5B,CAAC,eAGF3C,OAAA;MACE4C,OAAO,EAAEZ,UAAW;MACpBM,KAAK,EAAE;QACLO,QAAQ,EAAE,OAAO;QACjBC,MAAM,EAAE,MAAM;QACdC,KAAK,EAAE,MAAM;QACbC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdC,YAAY,EAAE,KAAK;QACnBC,MAAM,EAAE,MAAM;QACdC,UAAU,EAAE,yBAAyB;QACrCC,KAAK,EAAE,SAAS;QAChBC,QAAQ,EAAE,MAAM;QAChBC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE,IAAI;QACZjB,OAAO,EAAE,MAAM;QACfkB,UAAU,EAAE,QAAQ;QACpBC,cAAc,EAAE,QAAQ;QACxBC,SAAS,EAAE,+BAA+B;QAC1CC,UAAU,EAAE,eAAe;QAC3BC,cAAc,EAAE,YAAY;QAC5BC,oBAAoB,EAAE;MACxB,CAAE;MACFC,YAAY,EAAGC,CAAC,IAAK;QACnBA,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC4B,SAAS,GAAG,YAAY;QACvCF,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAACc,UAAU,GAAG,uBAAuB;MACrD,CAAE;MACFe,YAAY,EAAGH,CAAC,IAAK;QACnBA,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAAC4B,SAAS,GAAG,UAAU;QACrCF,CAAC,CAACC,MAAM,CAAC3B,KAAK,CAACc,UAAU,GAAG,yBAAyB;MACvD,CAAE;MACFgB,KAAK,EAAE/D,OAAO,GAAG,cAAc,GAAG,YAAa;MAAA8B,QAAA,EAE9C9B,OAAO,GAAG,IAAI,GAAG;IAAI;MAAAmC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC,eAGT3C,OAAA;MAAOqE,GAAG;MAAAlC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IAAO;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA,eACV,CAAC;AAEP,CAAC;AAACvC,EAAA,CAjKID,eAAe;AAAAmE,EAAA,GAAfnE,eAAe;AAmKrB,eAAeA,eAAe;AAAC,IAAAmE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}