{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport KuberaCardSection from './KuberaCardSection';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const analytics = useAnalytics();\n  const {\n    currentLanguage,\n    getUITexts,\n    translateContent,\n    isTranslating\n  } = useLanguage();\n\n  // State for translated content\n  const [translatedContent, setTranslatedContent] = useState({});\n\n  // Get UI texts for current language\n  const uiTexts = getUITexts();\n\n  // Track component mounting\n  useComponentTracking('LandingPage');\n  useEffect(() => {\n    // Track landing page view with additional context\n    analytics.trackEvent('landing_page_loaded', {\n      event_category: 'page_interaction',\n      page_type: 'landing',\n      content_type: 'kubera_guide'\n    });\n\n    // Add floating animation to content cards with staggered delay\n    const cards = document.querySelectorAll('.kubera-content-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.2}s`;\n      card.classList.add('floating');\n\n      // Track card visibility\n      const observer = new IntersectionObserver(entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            analytics.trackEvent('content_card_viewed', {\n              event_category: 'content_engagement',\n              card_index: index,\n              card_delay: index * 0.2\n            });\n            observer.unobserve(entry.target);\n          }\n        });\n      }, {\n        threshold: 0.5\n      });\n      observer.observe(card);\n    });\n  }, [analytics]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"landing-page kubera-guide-page\",\n    children: [/*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 55,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(KuberaAnimation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"landing-header\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"main-title\",\n        children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DDA \\u0DB6\\u0DBD\\u0DBA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"subtitle\",\n        children: \"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DDA \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DD6\\u0DBB\\u0DCA\\u0DAB \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C\\u0DDD\\u0DB4\\u0DAF\\u0DDA\\u0DC1\\u0DBA\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divine-blessing\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"blessing-text\",\n          children: \"\\uD83D\\uDE4F \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\uD83D\\uDE4F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 59,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(KuberaCardSection, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-content-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-card dark-glass-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"content-title\",\n            children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0DDA \\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA\\u0D9A\\u0DB8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0DC3\\u0DD1\\u0DB8 \\u0DB4\\u0DD4\\u0DAF\\u0DCA\\u0D9C\\u0DBD\\u0DBA\\u0D9A\\u0DD4\\u0D9C\\u0DDA\\u0DB8 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DC0\\u0DBB\\u0DAD\\u0DCA\\u0DC0\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DDE\\u0DB7\\u0DCF\\u0D9C\\u0DCA\\u200D\\u0DBA\\u0DBA \\u0DBA\\u0DB1\\u0DD4 \\u0D89\\u0DAD\\u0DCF \\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA \\u0D85\\u0D82\\u0D9C\\u0DBA\\u0D9A\\u0DD2. \\u0DC0\\u0DDB\\u0DAF\\u0DD2\\u0D9A \\u0DC3\\u0DC4 \\u0DC4\\u0DD2\\u0DB1\\u0DCA\\u0DAF\\u0DD4 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DB1\\u0DCA\\u0DA7 \\u0D85\\u0DB1\\u0DD4\\u0DC0, \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0\\u0DDA \\u0D87\\u0DAD\\u0DD2 \\u0DB0\\u0DB1\\u0DBA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DC3\\u0DC4 \\u0D91\\u0DC4\\u0DD2 \\u0DB7\\u0DCF\\u0DBB\\u0D9A\\u0DBB\\u0DD4 \\u0DC0\\u0DB1\\u0DCA\\u0DB1\\u0DDA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0DBA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DB0\\u0DB1 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DAD\\u0DCA, \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DB6\\u0DC0\\u0DA7 \\u0D9C\\u0DD0\\u0DB9\\u0DD4\\u0DBB\\u0DD4 \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0\\u0DCF\\u0DC3\\u0DBA\\u0D9A\\u0DCA \\u0DB4\\u0DC0\\u0DAD\\u0DD3.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 88,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 73,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-content-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-card dark-glass-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 99,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"content-title\",\n            children: \"\\u0D9A\\u0DC0\\u0DD4\\u0DAF \\u0DB8\\u0DDA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-body\",\n          children: [/*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0DC4\\u0DD2\\u0DB1\\u0DCA\\u0DAF\\u0DD4 \\u0DAF\\u0DDA\\u0DC0 \\u0DB4\\u0DD4\\u0DBB\\u0DCF\\u0DAB\\u0DBA\\u0DA7 \\u0D85\\u0DB1\\u0DD4\\u0DC0, \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DBA\\u0DB1\\u0DCA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2, \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DB7\\u0DCF\\u0DAB\\u0DCA\\u0DA9\\u0DCF\\u0D9C\\u0DCF\\u0DBB\\u0DD2\\u0D9A \\u0DC3\\u0DC4 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DDA \\u0D86\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0D9A\\u0DBA\\u0DCF (\\u0DAF\\u0DD2\\u0D9A\\u0DCA\\u0DB4\\u0DCF\\u0DBD) \\u0DBD\\u0DD9\\u0DC3 \\u0DC3\\u0DD0\\u0DBD\\u0D9A\\u0DDA.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DD3 \\u0DBD\\u0DCF\\u0D82\\u0D9A\\u0DD2\\u0D9A \\u0D85\\u0DB4\\u0DA7 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0DC0\\u0DA9\\u0DCF\\u0DAD\\u0DCA \\u0DC3\\u0DB8\\u0DD3\\u0DB4 \\u0DA0\\u0DBB\\u0DD2\\u0DAD\\u0DBA\\u0D9A\\u0DD2, \\u0DB8\\u0DB1\\u0DCA\\u0DAF \\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA \\u0DBD\\u0D82\\u0D9A\\u0DCF\\u0DB4\\u0DD4\\u0DBB\\u0DDA \\u0DBB\\u0DCF\\u0DC0\\u0DAB \\u0DBB\\u0DA2\\u0DD4\\u0D9C\\u0DDA \\u0D85\\u0DBB\\u0DCA\\u0DB0 \\u0DC3\\u0DC4\\u0DDD\\u0DAF\\u0DBB\\u0DBA\\u0DCF \\u0DBD\\u0DD9\\u0DC3\\u0DAF \\u0DC3\\u0DD0\\u0DBD\\u0D9A\\u0DD9\\u0DB1 \\u0DB6\\u0DD0\\u0DC0\\u0DD2\\u0DB1\\u0DD2. \\u0DB6\\u0DDE\\u0DAF\\u0DCA\\u0DB0 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DDA\\u0DAF\\u0DD3 \\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA \\\"\\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB\\\" (\\u0DC0\\u0DD9\\u0DC3\\u0DB8\\u0DD4\\u0DAB\\u0DD2) \\u0DBD\\u0DD9\\u0DC3 \\u0DC4\\u0DB3\\u0DD4\\u0DB1\\u0DCA\\u0DC0\\u0DB1\\u0DD4 \\u0DBD\\u0DB6\\u0DB1 \\u0D85\\u0DAD\\u0DBB, \\u0DC3\\u0DAD\\u0DBB\\u0DC0\\u0DBB\\u0DB8\\u0DCA \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DC0\\u0DBB\\u0DD4\\u0DB1\\u0DCA\\u0D9C\\u0DD9\\u0DB1\\u0DCA \\u0D9A\\u0DD9\\u0DB1\\u0DD9\\u0D9A\\u0DD4 \\u0DBD\\u0DD9\\u0DC3 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2\\u0DAD\\u0DCA\\u0DC0\\u0DBA \\u0DAF\\u0DBB\\u0DBA\\u0DD2.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 111,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0D9A\\u0DD1\\u0DAF\\u0DBB \\u0DBD\\u0DD9\\u0DC3 \\u0DB0\\u0DB1\\u0DBA \\u0DBB\\u0DD0\\u0DC3\\u0DCA \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\\u0DD9\\u0D9A\\u0DD4 \\u0DB1\\u0DDC\\u0DC0, \\u0DB0\\u0DCF\\u0DBB\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DC0 \\u0D8B\\u0DB4\\u0DBA\\u0DB1 \\u0DBD\\u0DAF \\u0DB0\\u0DB1\\u0DBA \\u0DBD\\u0DDD\\u0D9A\\u0DBA\\u0DA7 \\u0DB6\\u0DD9\\u0DAF\\u0DCF\\u0DC4\\u0DBB\\u0DD2\\u0DB1 \\u0DB4\\u0DCF\\u0DBD\\u0D9A\\u0DBA\\u0DD9\\u0D9A\\u0DD2.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-content-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-card dark-glass-card mantra-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 128,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 129,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"content-title\",\n            children: \"\\u0DB6\\u0DBD\\u0D9C\\u0DAD\\u0DD4 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DC3\\u0DC4 \\u0D91\\u0DC4\\u0DD2 \\u0DAD\\u0DDA\\u0DBB\\u0DD4\\u0DB8\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mantra-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mantra-subtitle\",\n              children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA (\\u0DC3\\u0D82\\u0DC3\\u0DCA\\u0D9A\\u0DD8\\u0DAD):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sanskrit-mantra\",\n              children: [\"\\u0950 \\u092F\\u0915\\u094D\\u0937\\u093E\\u092F \\u0915\\u0941\\u092C\\u0947\\u0930\\u093E\\u092F \\u0935\\u0948\\u0936\\u094D\\u0930\\u0935\\u0923\\u093E\\u092F \\u0927\\u0928\\u0927\\u093E\\u0928\\u094D\\u092F\\u093E\\u0927\\u093F\\u092A\\u0924\\u092F\\u0947\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 58\n              }, this), \"\\u0927\\u0928\\u0927\\u093E\\u0928\\u094D\\u092F\\u0938\\u092E\\u0943\\u0926\\u094D\\u0927\\u093F\\u0902 \\u092E\\u0947 \\u0926\\u0947\\u0939\\u093F \\u0926\\u093E\\u092A\\u092F \\u0938\\u094D\\u0935\\u093E\\u0939\\u093E \\u0965\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mantra-subtitle\",\n              children: \"\\u0D8B\\u0DA0\\u0DCA\\u0DA0\\u0DCF\\u0DBB\\u0DAB\\u0DBA \\u0DC3\\u0DB3\\u0DC4\\u0DCF (\\u0DC3\\u0DD2\\u0D82\\u0DC4\\u0DBD):\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 143,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"sinhala-pronunciation\",\n              children: [\"\\u0D95\\u0DB8\\u0DCA \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DCF\\u0DBA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB\\u0DCF\\u0DBA \\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB\\u0DCF\\u0DBA \\u0DB0\\u0DB1\\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DBA\\u0DDA,\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 63\n              }, this), \"\\u0DB0\\u0DB1\\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DB8\\u0DCA \\u0DB8\\u0DDA \\u0DAF\\u0DDA\\u0DC4\\u0DD2 \\u0DAF\\u0DCF\\u0DB4\\u0DBA \\u0DC3\\u0DCA\\u0DC0\\u0DCF\\u0DC4\\u0DCF \\u0965\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"mantra-subtitle\",\n              children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DDA \\u0DC3\\u0DBB\\u0DBD \\u0D85\\u0DBB\\u0DCA\\u0DAE\\u0DBA:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mantra-meaning\",\n              children: \"\\\"\\u0D95\\u0DB8\\u0DCA, \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DBB\\u0DA2\\u0DD4 \\u0DC0\\u0DD6\\u0DAD\\u0DCA, \\u0DB0\\u0DB1\\u0DBA\\u0DA7 \\u0DC3\\u0DC4 \\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DBA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DC0\\u0DD6\\u0DAD\\u0DCA, \\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB \\u0DBD\\u0DD9\\u0DC3\\u0DAF \\u0DC4\\u0DD0\\u0DB3\\u0DD2\\u0DB1\\u0DCA\\u0DC0\\u0DD9\\u0DB1 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DD2, \\u0D94\\u0DB6 \\u0DC0\\u0DC4\\u0DB1\\u0DCA\\u0DC3\\u0DDA\\u0DA7 \\u0DB8\\u0DB8 \\u0DB1\\u0DB8\\u0DC3\\u0DCA\\u0D9A\\u0DCF\\u0DBB \\u0D9A\\u0DBB\\u0DB8\\u0DD2. \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DB8\\u0DA7 \\u0DB0\\u0DB1\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DC4 \\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0DBD\\u0DB6\\u0DCF \\u0DAF\\u0DD9\\u0DB1\\u0DD4 \\u0DB8\\u0DD0\\u0DB1\\u0DC0.\\\"\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 150,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 135,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 127,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 126,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-content-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-card dark-glass-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 163,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"content-title\",\n            children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DB1\\u0DD2\\u0DC0\\u0DD0\\u0DBB\\u0DAF\\u0DD2\\u0DC0 \\u0DB7\\u0DCF\\u0DC0\\u0DD2\\u0DAD \\u0D9A\\u0DBB\\u0DB1 \\u0D86\\u0D9A\\u0DCF\\u0DBB\\u0DBA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"usage-guidelines\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"guideline-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"guideline-title\",\n                children: \"1. \\u0DC3\\u0DD4\\u0DAF\\u0DD4\\u0DC3\\u0DD4\\u0DB8 \\u0DC0\\u0DDA\\u0DBD\\u0DCF\\u0DC0:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 173,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0DAF\\u0DD2\\u0DB1\\u0DB4\\u0DAD\\u0DCF \\u0D8B\\u0DAF\\u0DD1\\u0DC3\\u0DB1 \\u0DC3\\u0DCA\\u0DB1\\u0DCF\\u0DB1\\u0DBA \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D85\\u0DB1\\u0DAD\\u0DD4\\u0DBB\\u0DD4\\u0DC0 \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0DC0 \\u0DC4\\u0DDD \\u0DC3\\u0DB1\\u0DCA\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF \\u0D9A\\u0DCF\\u0DBD\\u0DDA\\u0DAF\\u0DD3 \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC0\\u0DA9\\u0DCF\\u0DAD\\u0DCA \\u0DC3\\u0DD4\\u0DAF\\u0DD4\\u0DC3\\u0DD4\\u0DBA. \\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DB6\\u0DCA\\u200D\\u0DBB\\u0DC4\\u0DCA\\u0DB8 \\u0DB8\\u0DD4\\u0DC4\\u0DD4\\u0DBB\\u0DCA\\u0DAD\\u0DBA (\\u0D85\\u0DBD\\u0DD4\\u0DBA\\u0DB8 4:30 - 5:30 \\u0DB4\\u0DB8\\u0DAB) \\u0D89\\u0DAD\\u0DCF \\u0DB6\\u0DBD\\u0D9C\\u0DAD\\u0DD4\\u0DBA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 174,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 172,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"guideline-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"guideline-title\",\n                children: \"2. \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DB1\\u0DBA:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 182,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0DB1\\u0DD2\\u0DC0\\u0DC3\\u0DDA \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4, \\u0DB1\\u0DD2\\u0DC3\\u0DCA\\u0D9A\\u0DBD\\u0D82\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DB1\\u0DBA\\u0D9A\\u0DCA \\u0DAD\\u0DDD\\u0DBB\\u0DCF\\u0D9C\\u0DB1\\u0DCA\\u0DB1. \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DB1\\u0DB8\\u0DCA \\u0DB4\\u0DD6\\u0DA2\\u0DCF\\u0DC3\\u0DB1\\u0DBA\\u0D9A\\u0DCA \\u0DC3\\u0D9A\\u0DC3\\u0DCF \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DBB\\u0DD6\\u0DB4\\u0DBA\\u0D9A\\u0DCA \\u0DC4\\u0DDD \\u0DBA\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0D9A\\u0DCA \\u0DAD\\u0DB6\\u0DCF \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DB8\\u0DB1\\u0DC3 \\u0D92\\u0D9A\\u0DCF\\u0D9C\\u0DCA\\u200D\\u0DBB \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8 \\u0DB4\\u0DC4\\u0DC3\\u0DD4\\u0DBA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"guideline-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"guideline-title\",\n                children: \"3. \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0DC1\\u0DCF\\u0DBB\\u0DD3\\u0DBB\\u0DD2\\u0D9A \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8 \\u0DB8\\u0DD9\\u0DB1\\u0DCA\\u0DB8 \\u0DB8\\u0DCF\\u0DB1\\u0DC3\\u0DD2\\u0D9A \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8\\u0DAF \\u0D85\\u0DAD\\u0DD2\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA\\u0DBA. \\u0D9A\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4 \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DDD\\u0DB0\\u0DBA\\u0D9A\\u0DCA, \\u0DC0\\u0DDB\\u0DBB\\u0DBA\\u0D9A\\u0DCA \\u0DC4\\u0DDD \\u0DB1\\u0DD2\\u0DC2\\u0DDA\\u0DB0\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0D9A \\u0DC3\\u0DD2\\u0DAD\\u0DD4\\u0DC0\\u0DD2\\u0DBD\\u0DCA\\u0DBD\\u0D9A\\u0DCA \\u0DC3\\u0DD2\\u0DAD\\u0DDA \\u0DAD\\u0DB6\\u0DCF \\u0DB1\\u0DDC\\u0D9C\\u0DD9\\u0DB1, \\u0DC3\\u0DD0\\u0DC4\\u0DD0\\u0DBD\\u0DCA\\u0DBD\\u0DD4 \\u0DB8\\u0DB1\\u0DC3\\u0D9A\\u0DD2\\u0DB1\\u0DCA \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0D86\\u0DBB\\u0DB8\\u0DCA\\u0DB7 \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 192,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"guideline-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"guideline-title\",\n                children: \"4. \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DB1\\u0DD2\\u0DC3\\u0DCF, \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DBB\\u0DB1 \\u0DC0\\u0DD2\\u0DA7 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0DB8\\u0DD4\\u0DC4\\u0DD4\\u0DAB\\u0DBD\\u0DCF \\u0DC0\\u0DCF\\u0DA9\\u0DD2 \\u0DC0\\u0DD3\\u0DB8 \\u0D89\\u0DAD\\u0DCF \\u0DBA\\u0DDD\\u0D9C\\u0DCA\\u200D\\u0DBA\\u0DBA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"guideline-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"guideline-title\",\n                children: \"5. \\u0DA2\\u0DB4 \\u0D9A\\u0DBB\\u0DB1 \\u0DC0\\u0DCF\\u0DBB \\u0D9C\\u0DAB\\u0DB1:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 208,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0DC3\\u0DCA\\u0DB5\\u0DA7\\u0DD2\\u0D9A, \\u0DBB\\u0DD4\\u0DAF\\u0DCA\\u200D\\u0DBB\\u0DCF\\u0D9A\\u0DCA\\u0DC2 \\u0DC4\\u0DDD \\u0DAD\\u0DD4\\u0DBD\\u0DCA\\u0DC3\\u0DD2 (\\u0DB8\\u0DAF\\u0DD4\\u0DBB\\u0DD4\\u0DAD\\u0DBD\\u0DCF) \\u0D87\\u0DA7\\u0DC0\\u0DBD\\u0DD2\\u0DB1\\u0DCA \\u0DC3\\u0DD0\\u0DAF\\u0DD6 \\u0DA2\\u0DB4\\u0DB8\\u0DCF\\u0DBD\\u0DBA\\u0D9A\\u0DCA \\u0DB7\\u0DCF\\u0DC0\\u0DD2\\u0DAD \\u0D9A\\u0DBB 108 \\u0DC0\\u0DAD\\u0DCF\\u0DC0\\u0D9A\\u0DCA \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DBA\\u0DD2. \\u0D86\\u0DBB\\u0DB8\\u0DCA\\u0DB7\\u0DDA\\u0DAF\\u0DD3 \\u0D94\\u0DB6\\u0DA7 \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DC0\\u0DCF\\u0DBB \\u0D9C\\u0DAB\\u0DB1\\u0D9A\\u0DCA (\\u0D8B\\u0DAF\\u0DCF: 9, 27, 54) \\u0DA2\\u0DB4 \\u0D9A\\u0DBB \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0DD9\\u0DB1\\u0DCA 108 \\u0DAF\\u0D9A\\u0DCA\\u0DC0\\u0DCF \\u0DC0\\u0DD0\\u0DA9\\u0DD2 \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"guideline-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"guideline-title\",\n                children: \"6. \\u0DB4\\u0DD6\\u0DA2\\u0DCF\\u0DC0:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DA7 \\u0DB4\\u0DD9\\u0DBB \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA \\u0D8B\\u0DAF\\u0DD9\\u0DC3\\u0DCF \\u0DB4\\u0DC4\\u0DB1\\u0D9A\\u0DCA \\u0DAF\\u0DD0\\u0DBD\\u0DCA\\u0DC0\\u0DD3\\u0DB8, \\u0DC3\\u0DD4\\u0DC0\\u0DB3 \\u0DC4\\u0DB3\\u0DD4\\u0DB1\\u0DCA\\u0D9A\\u0DD6\\u0DBB\\u0D9A\\u0DCA \\u0DB4\\u0DAD\\u0DCA\\u0DAD\\u0DD4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC3\\u0DC4 \\u0DB1\\u0DD0\\u0DC0\\u0DD4\\u0DB8\\u0DCA \\u0DB8\\u0DBD\\u0DCA \\u0D9A\\u0DD2\\u0DC4\\u0DD2\\u0DB4\\u0DBA\\u0D9A\\u0DCA \\u0DB4\\u0DD6\\u0DA2\\u0DCF \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DB7\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCA\\u0DB0\\u0DCF\\u0DC0 \\u0DB4\\u0DCA\\u200D\\u0DBB\\u0D9A\\u0DCF\\u0DC1 \\u0D9A\\u0DC5 \\u0DC4\\u0DD0\\u0D9A\\u0DD2\\u0DBA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 219,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 217,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 161,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-content-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-card dark-glass-card benefits-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 233,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 234,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"content-title\",\n            children: \"\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAD\\u0DD2\\u0DBD\\u0DCF\\u0DB7 \\u0DC3\\u0DC4 \\u0DB1\\u0DD2\\u0DC0\\u0DD0\\u0DBB\\u0DAF\\u0DD2 \\u0DB8\\u0DCF\\u0DB1\\u0DC3\\u0DD2\\u0D9A \\u0D86\\u0D9A\\u0DBD\\u0DCA\\u0DB4\\u0DBA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 236,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"benefits-list\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83D\\uDCB0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0DC0\\u0DD3\\u0DB8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DBB\\u0DD0\\u0D9A\\u0DD2\\u0DBA\\u0DCF\\u0DC0\\u0DDA, \\u0DC0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DB4\\u0DCF\\u0DBB\\u0DDA \\u0DC4\\u0DDD \\u0DC0\\u0DD9\\u0DB1\\u0DAD\\u0DCA \\u0D86\\u0DAF\\u0DCF\\u0DBA\\u0DB8\\u0DCA \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C\\u0DC0\\u0DBD \\u0D87\\u0DAD\\u0DD2 \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0DC0\\u0DD3 \\u0DBA\\u0DC4\\u0DB4\\u0DAD \\u0D8B\\u0DAF\\u0DCF\\u0DC0\\u0DDA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 242,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83C\\uDF1F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 254,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\u0DB1\\u0DC0 \\u0D86\\u0DAF\\u0DCF\\u0DBA\\u0DB8\\u0DCA \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C \\u0DC0\\u0DD2\\u0DC0\\u0DD8\\u0DAD \\u0DC0\\u0DD3\\u0DB8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DB0\\u0DB1\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0DC0\\u0DD3\\u0DB8\\u0DA7 \\u0DB1\\u0DC0 \\u0D85\\u0DC0\\u0DC3\\u0DCA\\u0DAE\\u0DCF \\u0DC3\\u0DC4 \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C \\u0DC0\\u0DD2\\u0DC0\\u0DD8\\u0DAD\\u0DC0\\u0DDA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83C\\uDFE6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 264,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\u0DAB\\u0DBA \\u0DB6\\u0DBB\\u0DD2\\u0DB1\\u0DCA \\u0DB8\\u0DD2\\u0DAF\\u0DD3\\u0DB8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 266,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DC0\\u0DBB\\u0DAD\\u0DCA\\u0DC0\\u0DBA\\u0D9A\\u0DCA \\u0D87\\u0DAD\\u0DD2\\u0DC0\\u0DD3\\u0DB8 \\u0DB1\\u0DD2\\u0DC3\\u0DCF \\u0DAB\\u0DBA\\u0DAD\\u0DD4\\u0DBB\\u0DD4\\u0DC3\\u0DCA \\u0DC0\\u0DBD\\u0DD2\\u0DB1\\u0DCA \\u0DB1\\u0DD2\\u0DAF\\u0DC4\\u0DC3\\u0DCA \\u0DC0\\u0DD3\\u0DB8\\u0DA7 \\u0DB8\\u0D9C \\u0DB4\\u0DD1\\u0DAF\\u0DDA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 265,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 263,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefit-item\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-icon\",\n                children: \"\\uD83D\\uDEE1\\uFE0F\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 274,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-content\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  children: \"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DD4\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0DD2\\u0DAD \\u0DC0\\u0DD3\\u0DB8\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 276,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0D8B\\u0DB4\\u0DBA\\u0DB1 \\u0DBD\\u0DAF \\u0DB0\\u0DB1\\u0DBA \\u0D85\\u0DB1\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA \\u0DBD\\u0DD9\\u0DC3 \\u0DC0\\u0DD2\\u0DBA\\u0DAF\\u0DB8\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DD3 \\u0D89\\u0DAD\\u0DD2\\u0DBB\\u0DD2 \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DA7 \\u0DC3\\u0DC4 \\u0DC0\\u0DBB\\u0DCA\\u0DB0\\u0DB1\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DA7 \\u0D85\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DBD\\u0DD0\\u0DB6\\u0DDA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 241,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 240,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 232,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-content-section\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-card dark-glass-card important-notes-card\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-glow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"card-shine\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-header\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"content-title\",\n            children: \"\\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA\\u0DB8 \\u0DAF\\u0DDA\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"content-body\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"important-note\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DBA\\u0DB1\\u0DD4 \\u0DB8\\u0DD0\\u0DA2\\u0DD2\\u0D9A\\u0DCA \\u0DBA\\u0DC2\\u0DCA\\u0DA7\\u0DD2\\u0DBA\\u0D9A\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DB1 \\u0DB6\\u0DC0 \\u0DAD\\u0DDA\\u0DBB\\u0DD4\\u0DB8\\u0DCA \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DBA\\u0DD2. \\u0D91\\u0DBA \\u0D9A\\u0DCA\\u0DC2\\u0DAB\\u0DD2\\u0D9A\\u0DC0 \\u0DB8\\u0DD4\\u0DAF\\u0DBD\\u0DCA \\u0DB8\\u0DC0\\u0DB1 \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0D9A\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DDA.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DD2\\u0DAF\\u0DD4 \\u0DC0\\u0DB1\\u0DCA\\u0DB1\\u0DDA \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DC3\\u0DD2\\u0DAD\\u0DD4\\u0DC0\\u0DD2\\u0DBD\\u0DD2 \\u0DC3\\u0DC4 \\u0D9A\\u0DB8\\u0DCA\\u0DB4\\u0DB1 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA, \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0D9C\\u0DDA \\u0DC3\\u0DC4 \\u0DB0\\u0DB1\\u0D9C\\u0DDA \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC4\\u0DCF \\u0D85\\u0DB1\\u0DD4\\u0D9C\\u0DAD \\u0DC0\\u0DD3\\u0DB8\\u0DBA\\u0DD2.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 304,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0D91\\u0DB6\\u0DD0\\u0DC0\\u0DD2\\u0DB1\\u0DCA, \\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCA\\u0DB0\\u0DCF\\u0DC0, \\u0DB7\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0D9A\\u0DD0\\u0DB4\\u0DC0\\u0DD3\\u0DB8 \\u0DBA\\u0DB1 \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DD4 \\u0DAD\\u0DD4\\u0DB1 \\u0DB8\\u0DAD \\u0DB4\\u0DAF\\u0DB1\\u0DB8\\u0DCA\\u0DC0, \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DC0\\u0DD9\\u0DC4\\u0DD9\\u0DC3 \\u0DB8\\u0DC4\\u0DB1\\u0DCA\\u0DC3\\u0DD2 \\u0DC0\\u0DD3 \\u0D9A\\u0DBB\\u0DB1 \\u0D8B\\u0DAD\\u0DCA\\u0DC3\\u0DCF\\u0DC4\\u0DBA\\u0DA7 \\u0DB8\\u0DD9\\u0DB8 \\u0D85\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DB4\\u0DD4\\u0DC4\\u0DD4\\u0DAB\\u0DD4\\u0DC0\\u0DAF \\u0D91\\u0D9A\\u0DCA \\u0D9A\\u0DBB \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D94\\u0DB6\\u0DA7 \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0D89\\u0DBD\\u0D9A\\u0DCA\\u0D9A \\u0DC3\\u0DB5\\u0DBD \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DDA \\u0DB8\\u0DCF\\u0DC0\\u0DAD \\u0DC0\\u0DD2\\u0DC0\\u0DBB \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A\\u0DD2\\u0DBA.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 298,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 290,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 289,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-footer\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"divine-blessing\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"blessing-text\",\n          children: \"\\uD83D\\uDE4F \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0D94\\u0DB6 \\u0DC3\\u0DB8\\u0D9F \\u0DC0\\u0DDA\\u0DC0\\u0DCF \\uD83D\\uDE4F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 322,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"VHi/oTf7OnD2ZwBGmzHvdk0roEs=\", false, function () {\n  return [useAnalytics, useLanguage, useComponentTracking];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "ParticleBackground", "KuberaAnimation", "KuberaCardSection", "useLanguage", "useAnalytics", "useComponentTracking", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "analytics", "currentLanguage", "getUITexts", "translate<PERSON>ontent", "isTranslating", "<PERSON><PERSON><PERSON><PERSON>", "setTranslatedContent", "uiTexts", "trackEvent", "event_category", "page_type", "content_type", "cards", "document", "querySelectorAll", "for<PERSON>ach", "card", "index", "style", "animationDelay", "classList", "add", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "card_index", "card_delay", "unobserve", "target", "threshold", "observe", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport KuberaCardSection from './KuberaCardSection';\nimport { useLanguage } from '../contexts/LanguageContext';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\n\nconst LandingPage = () => {\n  const analytics = useAnalytics();\n  const { currentLanguage, getUITexts, translateContent, isTranslating } = useLanguage();\n\n  // State for translated content\n  const [translatedContent, setTranslatedContent] = useState({});\n\n  // Get UI texts for current language\n  const uiTexts = getUITexts();\n\n  // Track component mounting\n  useComponentTracking('LandingPage');\n\n  useEffect(() => {\n    // Track landing page view with additional context\n    analytics.trackEvent('landing_page_loaded', {\n      event_category: 'page_interaction',\n      page_type: 'landing',\n      content_type: 'kubera_guide'\n    });\n\n    // Add floating animation to content cards with staggered delay\n    const cards = document.querySelectorAll('.kubera-content-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.2}s`;\n      card.classList.add('floating');\n\n      // Track card visibility\n      const observer = new IntersectionObserver((entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            analytics.trackEvent('content_card_viewed', {\n              event_category: 'content_engagement',\n              card_index: index,\n              card_delay: index * 0.2\n            });\n            observer.unobserve(entry.target);\n          }\n        });\n      }, { threshold: 0.5 });\n\n      observer.observe(card);\n    });\n  }, [analytics]);\n\n  return (\n    <div className=\"landing-page kubera-guide-page\">\n      <ParticleBackground />\n      <KuberaAnimation />\n\n      {/* Header Section */}\n      <div className=\"landing-header\">\n        <h1 className=\"main-title\">කුබේර මන්ත්‍රේ බලය</h1>\n        <h2 className=\"subtitle\">ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීමේ සම්පූර්ණ මාර්ගෝපදේශය</h2>\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ 🙏</span>\n        </div>\n      </div>\n\n      {/* Kubera Card Product Section */}\n      <KuberaCardSection />\n\n\n\n      {/* Introduction Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">කුබේර මන්ත්‍රයේ වැදගත්කම</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <p>\n              සෑම පුද්ගලයකුගේම ජීවිතේ මූලික ස්ථාවරත්වය සහ සෞභාග්‍යය යනු ඉතා වැදගත්\n              අංගයකි. වෛදික සහ හින්දු සම්ප්‍රදායන්ට අනුව, විශ්වේ ඇති ධනයට අධිපති සහ එහි\n              භාරකරු වන්නේ කුබේර දෙවියන්ය.\n            </p>\n            <p>\n              එතුමන්ගේ ආශීර්වාදය ලබා ගැනීමෙන් ධන සම්පත්, සමෘද්ධිය සහ ජීවිතේ මූලික\n              බාධක ඉවත් කරගත හැකි බවට ගැඹුරු විශ්වාසයක් පවතී.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Who is Kubera Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">කවුද මේ කුබේර දෙවියන්?</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <p>\n              හින්දු දේව පුරාණයට අනුව, කුබේර යනු යක්ෂයන්ට අධිපති, දෙවියන්ගේ භාණ්ඩාගාරික\n              සහ උතුරු දිශාවේ ආරක්ෂකයා (දික්පාල) ලෙස සැලකේ.\n            </p>\n            <p>\n              ශ්‍රී ලාංකික අපට කුබේර යනු වඩාත් සමීප චරිතයකි, මන්ද එතුමන් ලංකාපුරේ රාවණ\n              රජුගේ අර්ධ සහෝදරයා ලෙසද සැලකෙන බැවිනි. බෞද්ධ සම්ප්‍රදායේදී එතුමන්\n              \"වෛශ්‍රවණ\" (වෙසමුණි) ලෙස හඳුන්වනු ලබන අතර, සතරවරම් දෙවිවරුන්ගෙන් කෙනෙකු\n              ලෙස උතුරු දිශාවට අධිපතිත්වය දරයි.\n            </p>\n            <p>\n              කුබේර යනු කෑදර ලෙස ධනය රැස් කරන්නෙකු නොව, ධාර්මික ව උපයන ලද ධනය\n              ලෝකයට බෙදාහරින පාලකයෙකි.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* The Mantra Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card mantra-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">බලගතු කුබේර මන්ත්‍රය සහ එහි තේරුම</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"mantra-section\">\n              <h4 className=\"mantra-subtitle\">මන්ත්‍රය (සංස්කෘත):</h4>\n              <div className=\"sanskrit-mantra\">\n                ॐ यक्षाय कुबेराय वैश्रवणाय धनधान्याधिपतये<br/>\n                धनधान्यसमृद्धिं मे देहि दापय स्वाहा ॥\n              </div>\n\n              <h4 className=\"mantra-subtitle\">උච්චාරණය සඳහා (සිංහල):</h4>\n              <div className=\"sinhala-pronunciation\">\n                ඕම් යක්ෂාය කුබේරාය වෛශ්‍රවණාය ධනධාන්‍යාධිපතයේ,<br/>\n                ධනධාන්‍ය සමෘද්ධිම් මේ දේහි දාපය ස්වාහා ॥\n              </div>\n\n              <h4 className=\"mantra-subtitle\">මන්ත්‍රේ සරල අර්ථය:</h4>\n              <div className=\"mantra-meaning\">\n                \"ඕම්, යක්ෂයන්ගේ රජු වූත්, ධනයට සහ ධාන්‍යයට අධිපති වූත්, වෛශ්‍රවණ ලෙසද\n                හැඳින්වෙන කුබේර දෙවියනි, ඔබ වහන්සේට මම නමස්කාර කරමි. කරුණාකර මට\n                ධනයෙන් සහ ධාන්‍යයෙන් සමෘද්ධිය ලබා දෙනු මැනව.\"\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* How to Use Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">මන්ත්‍රය නිවැරදිව භාවිත කරන ආකාරය</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"usage-guidelines\">\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">1. සුදුසුම වේලාව:</h4>\n                <p>\n                  දිනපතා උදෑසන ස්නානය කිරීමෙන් අනතුරුව පිරිසිදුව හෝ සන්ධ්‍යා කාලේදී\n                  මන්ත්‍රය ජප කිරීම වඩාත් සුදුසුය. විශේෂයෙන් බ්‍රහ්ම මුහුර්තය\n                  (අලුයම 4:30 - 5:30 පමණ) ඉතා බලගතුය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">2. ස්ථානය:</h4>\n                <p>\n                  නිවසේ පිරිසිදු, නිස්කලංක ස්ථානයක් තෝරාගන්න. හැකි නම් පූජාසනයක්\n                  සකසා කුබේර දෙවියන්ගේ රූපයක් හෝ යන්ත්‍රයක් තබා ගැනීමෙන් මනස\n                  ඒකාග්‍ර කරගැනීම පහසුය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">3. පිරිසිදුකම:</h4>\n                <p>\n                  ශාරීරික පිරිසිදුකම මෙන්ම මානසික පිරිසිදුකමද අතිවශ්‍යය. කිසිදු\n                  ක්‍රෝධයක්, වෛරයක් හෝ නිෂේධාත්මක සිතුවිල්ලක් සිතේ තබා නොගෙන,\n                  සැහැල්ලු මනසකින් මන්ත්‍ර ජප කිරීම ආරම්භ කරන්න.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">4. දිශාව:</h4>\n                <p>\n                  කුබේර දෙවියන් උතුරු දිශාවට අධිපති නිසා, මන්ත්‍රය ජප කරන විට උතුරු\n                  දිශාවට මුහුණලා වාඩි වීම ඉතා යෝග්‍යය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">5. ජප කරන වාර ගණන:</h4>\n                <p>\n                  ස්ඵටික, රුද්‍රාක්ෂ හෝ තුල්සි (මදුරුතලා) ඇටවලින් සැදූ ජපමාලයක්\n                  භාවිත කර 108 වතාවක් මන්ත්‍රය ජප කිරීම සම්ප්‍රදායයි. ආරම්භේදී\n                  ඔබට හැකි වාර ගණනක් (උදා: 9, 27, 54) ජප කර ක්‍රමයෙන් 108 දක්වා\n                  වැඩි කරගත හැක.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">6. පූජාව:</h4>\n                <p>\n                  මන්ත්‍රය ජප කිරීමට පෙර කුබේර දෙවියන් උදෙසා පහනක් දැල්වීම, සුවඳ\n                  හඳුන්කූරක් පත්තු කිරීම සහ නැවුම් මල් කිහිපයක් පූජා කිරීමෙන් ඔබගේ\n                  භක්තිය සහ ශ්‍රද්ධාව ප්‍රකාශ කළ හැකිය.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Benefits Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card benefits-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">ප්‍රතිලාභ සහ නිවැරදි මානසික ආකල්පය</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"benefits-list\">\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">💰</div>\n                <div className=\"benefit-content\">\n                  <h4>මූලික බාධක ඉවත් වීම</h4>\n                  <p>\n                    රැකියාවේ, ව්‍යාපාරේ හෝ වෙනත් ආදායම් මාර්ගවල ඇති බාධක ක්‍රමයෙන්\n                    ඉවත් වී යහපත උදාවේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🌟</div>\n                <div className=\"benefit-content\">\n                  <h4>නව ආදායම් මාර්ග විවෘත වීම</h4>\n                  <p>\n                    ධනය ආකර්ෂණය වීමට නව අවස්ථා සහ මාර්ග විවෘතවේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🏦</div>\n                <div className=\"benefit-content\">\n                  <h4>ණය බරින් මිදීම</h4>\n                  <p>\n                    මූලික ස්ථාවරත්වයක් ඇතිවීම නිසා ණයතුරුස් වලින් නිදහස් වීමට මග පෑදේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🛡️</div>\n                <div className=\"benefit-content\">\n                  <h4>ධනය සුරක්ෂිත වීම</h4>\n                  <p>\n                    උපයන ලද ධනය අනවශ්‍ය ලෙස වියදම් නොවී ඉතිරි කරගැනීමට සහ\n                    වර්ධනය කරගැනීමට අවශ්‍ය ශක්තිය ලැබේ.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Important Notes Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card important-notes-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">වැදගත්ම දේ</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"important-note\">\n              <p>\n                කුබේර මන්ත්‍රය යනු මැජික් යෂ්ටියක් නොවන බව තේරුම් ගැනීමයි.\n                එය ක්ෂණිකව මුදල් මවන ක්‍රමයක් නොවේ.\n              </p>\n              <p>\n                මන්ත්‍ර ජප කිරීමෙන් සිදු වන්නේ ඔබගේ සිතුවිලි සහ කම්පන ශක්තිය,\n                සමෘද්ධිගේ සහ ධනගේ විශ්ව ශක්තිය හා අනුගත වීමයි.\n              </p>\n              <p>\n                එබැවින්, ශ්‍රද්ධාව, භක්තිය සහ කැපවීම යන කරුණු තුන මත පදනම්ව,\n                ඔබගේ වෙහෙස මහන්සි වී කරන උත්සාහයට මෙම අධ්‍යාත්මික පුහුණුවද\n                එක් කර ගැනීමෙන් ඔබට ඔබගේ මූලික ඉලක්ක සඵල කරගැනීමේ මාවත\n                විවර කරගත හැකිය.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer with Blessing */}\n      <div className=\"kubera-footer\">\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,WAAW,QAAQ,6BAA6B;AACzD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,SAAS,GAAGN,YAAY,CAAC,CAAC;EAChC,MAAM;IAAEO,eAAe;IAAEC,UAAU;IAAEC,gBAAgB;IAAEC;EAAc,CAAC,GAAGX,WAAW,CAAC,CAAC;;EAEtF;EACA,MAAM,CAACY,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjB,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAE9D;EACA,MAAMkB,OAAO,GAAGL,UAAU,CAAC,CAAC;;EAE5B;EACAP,oBAAoB,CAAC,aAAa,CAAC;EAEnCP,SAAS,CAAC,MAAM;IACd;IACAY,SAAS,CAACQ,UAAU,CAAC,qBAAqB,EAAE;MAC1CC,cAAc,EAAE,kBAAkB;MAClCC,SAAS,EAAE,SAAS;MACpBC,YAAY,EAAE;IAChB,CAAC,CAAC;;IAEF;IACA,MAAMC,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,sBAAsB,CAAC;IAC/DF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC7BD,IAAI,CAACE,KAAK,CAACC,cAAc,GAAG,GAAGF,KAAK,GAAG,GAAG,GAAG;MAC7CD,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;MAE9B;MACA,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;QACrDA,OAAO,CAACT,OAAO,CAACU,KAAK,IAAI;UACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;YACxB1B,SAAS,CAACQ,UAAU,CAAC,qBAAqB,EAAE;cAC1CC,cAAc,EAAE,oBAAoB;cACpCkB,UAAU,EAAEV,KAAK;cACjBW,UAAU,EAAEX,KAAK,GAAG;YACtB,CAAC,CAAC;YACFK,QAAQ,CAACO,SAAS,CAACJ,KAAK,CAACK,MAAM,CAAC;UAClC;QACF,CAAC,CAAC;MACJ,CAAC,EAAE;QAAEC,SAAS,EAAE;MAAI,CAAC,CAAC;MAEtBT,QAAQ,CAACU,OAAO,CAAChB,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChB,SAAS,CAAC,CAAC;EAEf,oBACEH,OAAA;IAAKoC,SAAS,EAAC,gCAAgC;IAAAC,QAAA,gBAC7CrC,OAAA,CAACP,kBAAkB;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACtBzC,OAAA,CAACN,eAAe;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGnBzC,OAAA;MAAKoC,SAAS,EAAC,gBAAgB;MAAAC,QAAA,gBAC7BrC,OAAA;QAAIoC,SAAS,EAAC,YAAY;QAAAC,QAAA,EAAC;MAAkB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAClDzC,OAAA;QAAIoC,SAAS,EAAC,UAAU;QAAAC,QAAA,EAAC;MAAqD;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnFzC,OAAA;QAAKoC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BrC,OAAA;UAAMoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA,CAACL,iBAAiB;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAKrBzC,OAAA;MAAKoC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCrC,OAAA;QAAKoC,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClDrC,OAAA;UAAKoC,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCzC,OAAA;UAAKoC,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElCzC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrC,OAAA;YAAIoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxD,CAAC,eAENzC,OAAA;UAAKoC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrC,OAAA;YAAAqC,QAAA,EAAG;UAIH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzC,OAAA;YAAAqC,QAAA,EAAG;UAGH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCrC,OAAA;QAAKoC,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClDrC,OAAA;UAAKoC,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCzC,OAAA;UAAKoC,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElCzC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrC,OAAA;YAAIoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAsB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC,eAENzC,OAAA;UAAKoC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BrC,OAAA;YAAAqC,QAAA,EAAG;UAGH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzC,OAAA;YAAAqC,QAAA,EAAG;UAKH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJzC,OAAA;YAAAqC,QAAA,EAAG;UAGH;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCrC,OAAA;QAAKoC,SAAS,EAAC,iDAAiD;QAAAC,QAAA,gBAC9DrC,OAAA;UAAKoC,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCzC,OAAA;UAAKoC,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElCzC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrC,OAAA;YAAIoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAENzC,OAAA;UAAKoC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrC,OAAA;YAAKoC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrC,OAAA;cAAIoC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDzC,OAAA;cAAKoC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,GAAC,oOACU,eAAArC,OAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,yMAEhD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENzC,OAAA;cAAIoC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC3DzC,OAAA;cAAKoC,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,6PACS,eAAArC,OAAA;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,sNAErD;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAENzC,OAAA;cAAIoC,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAmB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACxDzC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAIhC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCrC,OAAA;QAAKoC,SAAS,EAAC,qCAAqC;QAAAC,QAAA,gBAClDrC,OAAA;UAAKoC,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCzC,OAAA;UAAKoC,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElCzC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrC,OAAA;YAAIoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjE,CAAC,eAENzC,OAAA;UAAKoC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrC,OAAA;YAAKoC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/BrC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrC,OAAA;gBAAIoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACtDzC,OAAA;gBAAAqC,QAAA,EAAG;cAIH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrC,OAAA;gBAAIoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/CzC,OAAA;gBAAAqC,QAAA,EAAG;cAIH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrC,OAAA;gBAAIoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnDzC,OAAA;gBAAAqC,QAAA,EAAG;cAIH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrC,OAAA;gBAAIoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CzC,OAAA;gBAAAqC,QAAA,EAAG;cAGH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrC,OAAA;gBAAIoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACvDzC,OAAA;gBAAAqC,QAAA,EAAG;cAKH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,gBAC7BrC,OAAA;gBAAIoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC9CzC,OAAA;gBAAAqC,QAAA,EAAG;cAIH;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCrC,OAAA;QAAKoC,SAAS,EAAC,mDAAmD;QAAAC,QAAA,gBAChErC,OAAA;UAAKoC,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCzC,OAAA;UAAKoC,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElCzC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrC,OAAA;YAAIoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAkC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eAENzC,OAAA;UAAKoC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrC,OAAA;YAAKoC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BrC,OAAA;cAAKoC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrC,OAAA;gBAAKoC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCzC,OAAA;gBAAKoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrC,OAAA;kBAAAqC,QAAA,EAAI;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC5BzC,OAAA;kBAAAqC,QAAA,EAAG;gBAGH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrC,OAAA;gBAAKoC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCzC,OAAA;gBAAKoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrC,OAAA;kBAAAqC,QAAA,EAAI;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClCzC,OAAA;kBAAAqC,QAAA,EAAG;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrC,OAAA;gBAAKoC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACtCzC,OAAA;gBAAKoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrC,OAAA;kBAAAqC,QAAA,EAAI;gBAAc;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvBzC,OAAA;kBAAAqC,QAAA,EAAG;gBAEH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENzC,OAAA;cAAKoC,SAAS,EAAC,cAAc;cAAAC,QAAA,gBAC3BrC,OAAA;gBAAKoC,SAAS,EAAC,cAAc;gBAAAC,QAAA,EAAC;cAAG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvCzC,OAAA;gBAAKoC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,gBAC9BrC,OAAA;kBAAAqC,QAAA,EAAI;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACzBzC,OAAA;kBAAAqC,QAAA,EAAG;gBAGH;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,wBAAwB;MAAAC,QAAA,eACrCrC,OAAA;QAAKoC,SAAS,EAAC,0DAA0D;QAAAC,QAAA,gBACvErC,OAAA;UAAKoC,SAAS,EAAC;QAAW;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjCzC,OAAA;UAAKoC,SAAS,EAAC;QAAY;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAElCzC,OAAA;UAAKoC,SAAS,EAAC,gBAAgB;UAAAC,QAAA,eAC7BrC,OAAA;YAAIoC,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAU;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eAENzC,OAAA;UAAKoC,SAAS,EAAC,cAAc;UAAAC,QAAA,eAC3BrC,OAAA;YAAKoC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BrC,OAAA;cAAAqC,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzC,OAAA;cAAAqC,QAAA,EAAG;YAGH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJzC,OAAA;cAAAqC,QAAA,EAAG;YAKH;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,eAAe;MAAAC,QAAA,eAC5BrC,OAAA;QAAKoC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9BrC,OAAA;UAAMoC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAA2C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA/TID,WAAW;EAAA,QACGJ,YAAY,EAC2CD,WAAW,EASpFE,oBAAoB;AAAA;AAAA4C,EAAA,GAXhBzC,WAAW;AAiUjB,eAAeA,WAAW;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}