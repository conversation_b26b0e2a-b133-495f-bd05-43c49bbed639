{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\TranslationTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationTest = () => {\n  _s();\n  const {\n    currentLanguage,\n    translateText,\n    changeLanguage,\n    isTranslating\n  } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n      const result = await translateText('ආදරය', currentLanguage, 'test');\n      console.log('Translation result:', result);\n      setTestResult(`Translation: ${result}`);\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Translation Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Current Language: \", currentLanguage]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Is Translating: \", isTranslating ? 'Yes' : 'No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('sinhala'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Sinhala\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('english'),\n        style: {\n          margin: '5px'\n        },\n        children: \"English\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('tamil'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Tamil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: testTranslation,\n      disabled: isTranslating,\n      children: \"Test Translation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 66,\n      columnNumber: 7\n    }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'lightgreen'\n      },\n      children: testResult\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 71,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'red'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 39,\n    columnNumber: 5\n  }, this);\n};\n_s(TranslationTest, \"ofjRKXIuHGl7R9hpsA1r9bfRQW4=\", false, function () {\n  return [useTranslation];\n});\n_c = TranslationTest;\nexport default TranslationTest;\nvar _c;\n$RefreshReg$(_c, \"TranslationTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "jsxDEV", "_jsxDEV", "TranslationTest", "_s", "currentLanguage", "translateText", "changeLanguage", "isTranslating", "testResult", "setTestResult", "error", "setError", "testTranslation", "console", "log", "result", "err", "message", "style", "position", "top", "left", "background", "color", "padding", "borderRadius", "zIndex", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "margin", "disabled", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/TranslationTest.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\nconst TranslationTest = () => {\n  const { currentLanguage, translateText, changeLanguage, isTranslating } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n      \n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n      \n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n      \n      const result = await translateText('ආදරය', currentLanguage, 'test');\n      console.log('Translation result:', result);\n      setTestResult(`Translation: ${result}`);\n      \n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    }}>\n      <h3>Translation Test</h3>\n      <p>Current Language: {currentLanguage}</p>\n      <p>Is Translating: {isTranslating ? 'Yes' : 'No'}</p>\n      \n      <div style={{ marginBottom: '10px' }}>\n        <button onClick={() => changeLanguage('sinhala')} style={{ margin: '5px' }}>\n          Sinhala\n        </button>\n        <button onClick={() => changeLanguage('english')} style={{ margin: '5px' }}>\n          English\n        </button>\n        <button onClick={() => changeLanguage('tamil')} style={{ margin: '5px' }}>\n          Tamil\n        </button>\n      </div>\n      \n      <button onClick={testTranslation} disabled={isTranslating}>\n        Test Translation\n      </button>\n      \n      {testResult && (\n        <div style={{ marginTop: '10px', color: 'lightgreen' }}>\n          {testResult}\n        </div>\n      )}\n      \n      {error && (\n        <div style={{ marginTop: '10px', color: 'red' }}>\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TranslationTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,eAAe;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC1F,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFD,QAAQ,CAAC,EAAE,CAAC;MACZF,aAAa,CAAC,YAAY,CAAC;MAE3BI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;MACjDS,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,OAAOT,aAAa,CAAC;MAEpE,IAAID,eAAe,KAAK,SAAS,EAAE;QACjCK,aAAa,CAAC,oDAAoD,CAAC;QACnE;MACF;MAEA,MAAMM,MAAM,GAAG,MAAMV,aAAa,CAAC,MAAM,EAAED,eAAe,EAAE,MAAM,CAAC;MACnES,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEC,MAAM,CAAC;MAC1CN,aAAa,CAAC,gBAAgBM,MAAM,EAAE,CAAC;IAEzC,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZH,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAEM,GAAG,CAAC;MAC7CL,QAAQ,CAAC,UAAUK,GAAG,CAACC,OAAO,EAAE,CAAC;IACnC;EACF,CAAC;EAEDnB,SAAS,CAAC,MAAM;IACde,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;IACjDS,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,aAAa,CAAC;EAC/C,CAAC,EAAE,CAACH,eAAe,EAAEG,aAAa,CAAC,CAAC;EAEpC,oBACEN,OAAA;IAAKiB,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,iBAAiB;MAC7BC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACA3B,OAAA;MAAA2B,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzB/B,OAAA;MAAA2B,QAAA,GAAG,oBAAkB,EAACxB,eAAe;IAAA;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1C/B,OAAA;MAAA2B,QAAA,GAAG,kBAAgB,EAACrB,aAAa,GAAG,KAAK,GAAG,IAAI;IAAA;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErD/B,OAAA;MAAKiB,KAAK,EAAE;QAAEe,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnC3B,OAAA;QAAQiC,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,SAAS,CAAE;QAACY,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/B,OAAA;QAAQiC,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,SAAS,CAAE;QAACY,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/B,OAAA;QAAQiC,OAAO,EAAEA,CAAA,KAAM5B,cAAc,CAAC,OAAO,CAAE;QAACY,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE1E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAEN/B,OAAA;MAAQiC,OAAO,EAAEtB,eAAgB;MAACwB,QAAQ,EAAE7B,aAAc;MAAAqB,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAERxB,UAAU,iBACTP,OAAA;MAAKiB,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAa,CAAE;MAAAK,QAAA,EACpDpB;IAAU;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,EAEAtB,KAAK,iBACJT,OAAA;MAAKiB,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAM,CAAE;MAAAK,QAAA,EAC7ClB;IAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA/EID,eAAe;EAAA,QACuDH,cAAc;AAAA;AAAAuC,EAAA,GADpFpC,eAAe;AAiFrB,eAAeA,eAAe;AAAC,IAAAoC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}