class TranslationService {
  constructor() {
    // Use direct API URL for both development and production
    this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';
    this.cache = new Map();
    this.requestQueue = new Map();
    console.log('TranslationService initialized with API URL:', this.apiUrl);
    console.log('Environment:', process.env.NODE_ENV);
  }

  // Language mappings for better context
  getLanguageInfo(languageKey) {
    const languages = {
      sinhala: {
        name: 'Sinhala',
        nativeName: 'සිංහල',
        code: 'si'
      },
      english: {
        name: 'English',
        nativeName: 'English',
        code: 'en'
      },
      tamil: {
        name: 'Tamil',
        nativeName: 'தமிழ்',
        code: 'ta'
      }
    };
    return languages[languageKey] || languages.english;
  }

  // Generate cache key
  getCacheKey(text, targetLanguage, context) {
    return `${text.substring(0, 50)}_${targetLanguage}_${context}`.replace(/\s+/g, '_');
  }

  // Translate text using backend Gemini API
  async translateText(text, targetLanguage, context = '') {
    if (!text || text.trim() === '') {
      return text;
    }

    // Return original text if target is Sinhala (default language)
    if (targetLanguage === 'sinhala') {
      return text;
    }

    const cacheKey = this.getCacheKey(text, targetLanguage, context);
    
    // Check cache first
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Check if there's already a pending request for this translation
    if (this.requestQueue.has(cacheKey)) {
      return await this.requestQueue.get(cacheKey);
    }

    // Create the translation request
    const translationPromise = this.performTranslation(text, targetLanguage, context);
    this.requestQueue.set(cacheKey, translationPromise);

    try {
      const result = await translationPromise;
      this.cache.set(cacheKey, result);
      return result;
    } finally {
      this.requestQueue.delete(cacheKey);
    }
  }

  // Perform the actual translation via backend API
  async performTranslation(text, targetLanguage, context = '') {
    const maxRetries = 2;
    let lastError = null;

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        const targetLangInfo = this.getLanguageInfo(targetLanguage);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout

        const response = await fetch(`${this.apiUrl}/translate`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            text: text,
            targetLanguage: targetLanguage,
            targetLanguageName: targetLangInfo.name,
            context: context || 'horoscope_content'
          }),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw new Error(`Translation API error: ${response.status} - ${response.statusText}`);
        }

        const data = await response.json();

        if (data.success && data.translatedText) {
          return data.translatedText;
        } else {
          throw new Error(data.error || 'Translation failed - no translated text received');
        }
      } catch (error) {
        lastError = error;
        console.warn(`Translation attempt ${attempt + 1} failed:`, error.message);

        // If it's the last attempt or a non-retryable error, break
        if (attempt === maxRetries || error.name === 'AbortError') {
          break;
        }

        // Wait before retrying (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
      }
    }

    console.error('Translation service error after all retries:', lastError);

    // Fallback: return original text if all translation attempts fail
    return text;
  }

  // Translate multiple texts efficiently
  async translateMultiple(textArray, targetLanguage, context = '') {
    if (targetLanguage === 'sinhala') {
      return textArray;
    }

    try {
      const translations = await Promise.all(
        textArray.map(text => this.translateText(text, targetLanguage, context))
      );
      return translations;
    } catch (error) {
      console.error('Batch translation error:', error);
      return textArray; // Return original texts if batch translation fails
    }
  }

  // Translate horoscope categories
  async translateHoroscopeCategories(categories, targetLanguage) {
    if (targetLanguage === 'sinhala' || !categories || categories.length === 0) {
      return categories;
    }

    try {
      const translatedCategories = await Promise.all(
        categories.map(async (category) => {
          const translatedTitle = await this.translateText(
            category.title, 
            targetLanguage, 
            'horoscope_category_title'
          );
          
          const translatedContent = await this.translateText(
            category.content, 
            targetLanguage, 
            'horoscope_content'
          );

          return {
            ...category,
            title: translatedTitle,
            content: translatedContent
          };
        })
      );

      return translatedCategories;
    } catch (error) {
      console.error('Horoscope categories translation error:', error);
      return categories; // Return original categories if translation fails
    }
  }

  // Clear cache (useful for memory management)
  clearCache() {
    this.cache.clear();
  }

  // Get cache size for debugging
  getCacheSize() {
    return this.cache.size;
  }
}

const translationService = new TranslationService();
export default translationService;
