{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\LandingPage.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport KuberaCardSection from './KuberaCardSection';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LandingPage = () => {\n  _s();\n  const analytics = useAnalytics();\n\n  // Track component mounting\n  useComponentTracking('LandingPage');\n  useEffect(() => {\n    // Track landing page view with additional context\n    analytics.trackEvent('landing_page_loaded', {\n      event_category: 'page_interaction',\n      page_type: 'landing',\n      content_type: 'kubera_guide'\n    });\n\n    // Add floating animation to content cards with staggered delay\n    const cards = document.querySelectorAll('.kubera-content-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.2}s`;\n      card.classList.add('floating');\n\n      // Track card visibility\n      const observer = new IntersectionObserver(entries => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            analytics.trackEvent('content_card_viewed', {\n              event_category: 'content_engagement',\n              card_index: index,\n              card_delay: index * 0.2\n            });\n            observer.unobserve(entry.target);\n          }\n        });\n      }, {\n        threshold: 0.5\n      });\n      observer.observe(card);\n    });\n  }, [analytics]);\n  return /*#__PURE__*/_jsxDEV(TranslationLoader, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"landing-page kubera-guide-page\",\n      children: [/*#__PURE__*/_jsxDEV(ParticleBackground, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(KuberaAnimation, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          zIndex: 1001\n        },\n        children: /*#__PURE__*/_jsxDEV(LanguageSelector, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"landing-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"main-title\",\n          children: translatedContent.mainTitle || 'කුබේර මන්ත්‍රේ බලය'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"subtitle\",\n          children: translatedContent.subtitle || 'ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීමේ සම්පූර්ණ මාර්ගෝපදේශය'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divine-blessing\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"blessing-text\",\n            children: translatedContent.blessing || '🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ 🙏'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(KuberaCardSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kubera-content-card dark-glass-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-shine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"content-title\",\n              children: translatedContent.importanceTitle || 'කුබේර මන්ත්‍රයේ වැදගත්කම'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 81,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0DC3\\u0DD1\\u0DB8 \\u0DB4\\u0DD4\\u0DAF\\u0DCA\\u0D9C\\u0DBD\\u0DBA\\u0D9A\\u0DD4\\u0D9C\\u0DDA\\u0DB8 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DC0\\u0DBB\\u0DAD\\u0DCA\\u0DC0\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DDE\\u0DB7\\u0DCF\\u0D9C\\u0DCA\\u200D\\u0DBA\\u0DBA \\u0DBA\\u0DB1\\u0DD4 \\u0D89\\u0DAD\\u0DCF \\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA \\u0D85\\u0D82\\u0D9C\\u0DBA\\u0D9A\\u0DD2. \\u0DC0\\u0DDB\\u0DAF\\u0DD2\\u0D9A \\u0DC3\\u0DC4 \\u0DC4\\u0DD2\\u0DB1\\u0DCA\\u0DAF\\u0DD4 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DB1\\u0DCA\\u0DA7 \\u0D85\\u0DB1\\u0DD4\\u0DC0, \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0\\u0DDA \\u0D87\\u0DAD\\u0DD2 \\u0DB0\\u0DB1\\u0DBA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DC3\\u0DC4 \\u0D91\\u0DC4\\u0DD2 \\u0DB7\\u0DCF\\u0DBB\\u0D9A\\u0DBB\\u0DD4 \\u0DC0\\u0DB1\\u0DCA\\u0DB1\\u0DDA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0DBA.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 86,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DB0\\u0DB1 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DAD\\u0DCA, \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DB6\\u0DC0\\u0DA7 \\u0D9C\\u0DD0\\u0DB9\\u0DD4\\u0DBB\\u0DD4 \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0\\u0DCF\\u0DC3\\u0DBA\\u0D9A\\u0DCA \\u0DB4\\u0DC0\\u0DAD\\u0DD3.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kubera-content-card dark-glass-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-shine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"content-title\",\n              children: translatedContent.whoIsKuberaTitle || 'කවුද මේ කුබේර දෙවියන්?'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 106,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 105,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-body\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0DC4\\u0DD2\\u0DB1\\u0DCA\\u0DAF\\u0DD4 \\u0DAF\\u0DDA\\u0DC0 \\u0DB4\\u0DD4\\u0DBB\\u0DCF\\u0DAB\\u0DBA\\u0DA7 \\u0D85\\u0DB1\\u0DD4\\u0DC0, \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DBA\\u0DB1\\u0DCA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2, \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DB7\\u0DCF\\u0DAB\\u0DCA\\u0DA9\\u0DCF\\u0D9C\\u0DCF\\u0DBB\\u0DD2\\u0D9A \\u0DC3\\u0DC4 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DDA \\u0D86\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0D9A\\u0DBA\\u0DCF (\\u0DAF\\u0DD2\\u0D9A\\u0DCA\\u0DB4\\u0DCF\\u0DBD) \\u0DBD\\u0DD9\\u0DC3 \\u0DC3\\u0DD0\\u0DBD\\u0D9A\\u0DDA.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DD3 \\u0DBD\\u0DCF\\u0D82\\u0D9A\\u0DD2\\u0D9A \\u0D85\\u0DB4\\u0DA7 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0DC0\\u0DA9\\u0DCF\\u0DAD\\u0DCA \\u0DC3\\u0DB8\\u0DD3\\u0DB4 \\u0DA0\\u0DBB\\u0DD2\\u0DAD\\u0DBA\\u0D9A\\u0DD2, \\u0DB8\\u0DB1\\u0DCA\\u0DAF \\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA \\u0DBD\\u0D82\\u0D9A\\u0DCF\\u0DB4\\u0DD4\\u0DBB\\u0DDA \\u0DBB\\u0DCF\\u0DC0\\u0DAB \\u0DBB\\u0DA2\\u0DD4\\u0D9C\\u0DDA \\u0D85\\u0DBB\\u0DCA\\u0DB0 \\u0DC3\\u0DC4\\u0DDD\\u0DAF\\u0DBB\\u0DBA\\u0DCF \\u0DBD\\u0DD9\\u0DC3\\u0DAF \\u0DC3\\u0DD0\\u0DBD\\u0D9A\\u0DD9\\u0DB1 \\u0DB6\\u0DD0\\u0DC0\\u0DD2\\u0DB1\\u0DD2. \\u0DB6\\u0DDE\\u0DAF\\u0DCA\\u0DB0 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DDA\\u0DAF\\u0DD3 \\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA \\\"\\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB\\\" (\\u0DC0\\u0DD9\\u0DC3\\u0DB8\\u0DD4\\u0DAB\\u0DD2) \\u0DBD\\u0DD9\\u0DC3 \\u0DC4\\u0DB3\\u0DD4\\u0DB1\\u0DCA\\u0DC0\\u0DB1\\u0DD4 \\u0DBD\\u0DB6\\u0DB1 \\u0D85\\u0DAD\\u0DBB, \\u0DC3\\u0DAD\\u0DBB\\u0DC0\\u0DBB\\u0DB8\\u0DCA \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DC0\\u0DBB\\u0DD4\\u0DB1\\u0DCA\\u0D9C\\u0DD9\\u0DB1\\u0DCA \\u0D9A\\u0DD9\\u0DB1\\u0DD9\\u0D9A\\u0DD4 \\u0DBD\\u0DD9\\u0DC3 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2\\u0DAD\\u0DCA\\u0DC0\\u0DBA \\u0DAF\\u0DBB\\u0DBA\\u0DD2.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0D9A\\u0DD1\\u0DAF\\u0DBB \\u0DBD\\u0DD9\\u0DC3 \\u0DB0\\u0DB1\\u0DBA \\u0DBB\\u0DD0\\u0DC3\\u0DCA \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\\u0DD9\\u0D9A\\u0DD4 \\u0DB1\\u0DDC\\u0DC0, \\u0DB0\\u0DCF\\u0DBB\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DC0 \\u0D8B\\u0DB4\\u0DBA\\u0DB1 \\u0DBD\\u0DAF \\u0DB0\\u0DB1\\u0DBA \\u0DBD\\u0DDD\\u0D9A\\u0DBA\\u0DA7 \\u0DB6\\u0DD9\\u0DAF\\u0DCF\\u0DC4\\u0DBB\\u0DD2\\u0DB1 \\u0DB4\\u0DCF\\u0DBD\\u0D9A\\u0DBA\\u0DD9\\u0D9A\\u0DD2.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kubera-content-card dark-glass-card mantra-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-shine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"content-title\",\n              children: \"\\u0DB6\\u0DBD\\u0D9C\\u0DAD\\u0DD4 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DC3\\u0DC4 \\u0D91\\u0DC4\\u0DD2 \\u0DAD\\u0DDA\\u0DBB\\u0DD4\\u0DB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 134,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mantra-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mantra-subtitle\",\n                children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA (\\u0DC3\\u0D82\\u0DC3\\u0DCA\\u0D9A\\u0DD8\\u0DAD):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sanskrit-mantra\",\n                children: [\"\\u0950 \\u092F\\u0915\\u094D\\u0937\\u093E\\u092F \\u0915\\u0941\\u092C\\u0947\\u0930\\u093E\\u092F \\u0935\\u0948\\u0936\\u094D\\u0930\\u0935\\u0923\\u093E\\u092F \\u0927\\u0928\\u0927\\u093E\\u0928\\u094D\\u092F\\u093E\\u0927\\u093F\\u092A\\u0924\\u092F\\u0947\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 142,\n                  columnNumber: 58\n                }, this), \"\\u0927\\u0928\\u0927\\u093E\\u0928\\u094D\\u092F\\u0938\\u092E\\u0943\\u0926\\u094D\\u0927\\u093F\\u0902 \\u092E\\u0947 \\u0926\\u0947\\u0939\\u093F \\u0926\\u093E\\u092A\\u092F \\u0938\\u094D\\u0935\\u093E\\u0939\\u093E \\u0965\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 141,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mantra-subtitle\",\n                children: \"\\u0D8B\\u0DA0\\u0DCA\\u0DA0\\u0DCF\\u0DBB\\u0DAB\\u0DBA \\u0DC3\\u0DB3\\u0DC4\\u0DCF (\\u0DC3\\u0DD2\\u0D82\\u0DC4\\u0DBD):\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"sinhala-pronunciation\",\n                children: [\"\\u0D95\\u0DB8\\u0DCA \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DCF\\u0DBA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB\\u0DCF\\u0DBA \\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB\\u0DCF\\u0DBA \\u0DB0\\u0DB1\\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DBA\\u0DDA,\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 63\n                }, this), \"\\u0DB0\\u0DB1\\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DB8\\u0DCA \\u0DB8\\u0DDA \\u0DAF\\u0DDA\\u0DC4\\u0DD2 \\u0DAF\\u0DCF\\u0DB4\\u0DBA \\u0DC3\\u0DCA\\u0DC0\\u0DCF\\u0DC4\\u0DCF \\u0965\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 147,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"mantra-subtitle\",\n                children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DDA \\u0DC3\\u0DBB\\u0DBD \\u0D85\\u0DBB\\u0DCA\\u0DAE\\u0DBA:\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mantra-meaning\",\n                children: \"\\\"\\u0D95\\u0DB8\\u0DCA, \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DBB\\u0DA2\\u0DD4 \\u0DC0\\u0DD6\\u0DAD\\u0DCA, \\u0DB0\\u0DB1\\u0DBA\\u0DA7 \\u0DC3\\u0DC4 \\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DBA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DC0\\u0DD6\\u0DAD\\u0DCA, \\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB \\u0DBD\\u0DD9\\u0DC3\\u0DAF \\u0DC4\\u0DD0\\u0DB3\\u0DD2\\u0DB1\\u0DCA\\u0DC0\\u0DD9\\u0DB1 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DD2, \\u0D94\\u0DB6 \\u0DC0\\u0DC4\\u0DB1\\u0DCA\\u0DC3\\u0DDA\\u0DA7 \\u0DB8\\u0DB8 \\u0DB1\\u0DB8\\u0DC3\\u0DCA\\u0D9A\\u0DCF\\u0DBB \\u0D9A\\u0DBB\\u0DB8\\u0DD2. \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DB8\\u0DA7 \\u0DB0\\u0DB1\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DC4 \\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0DBD\\u0DB6\\u0DCF \\u0DAF\\u0DD9\\u0DB1\\u0DD4 \\u0DB8\\u0DD0\\u0DB1\\u0DC0.\\\"\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kubera-content-card dark-glass-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-shine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 167,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"content-title\",\n              children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DB1\\u0DD2\\u0DC0\\u0DD0\\u0DBB\\u0DAF\\u0DD2\\u0DC0 \\u0DB7\\u0DCF\\u0DC0\\u0DD2\\u0DAD \\u0D9A\\u0DBB\\u0DB1 \\u0D86\\u0D9A\\u0DCF\\u0DBB\\u0DBA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"usage-guidelines\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"guideline-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"guideline-title\",\n                  children: \"1. \\u0DC3\\u0DD4\\u0DAF\\u0DD4\\u0DC3\\u0DD4\\u0DB8 \\u0DC0\\u0DDA\\u0DBD\\u0DCF\\u0DC0:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 176,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DAF\\u0DD2\\u0DB1\\u0DB4\\u0DAD\\u0DCF \\u0D8B\\u0DAF\\u0DD1\\u0DC3\\u0DB1 \\u0DC3\\u0DCA\\u0DB1\\u0DCF\\u0DB1\\u0DBA \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D85\\u0DB1\\u0DAD\\u0DD4\\u0DBB\\u0DD4\\u0DC0 \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0DC0 \\u0DC4\\u0DDD \\u0DC3\\u0DB1\\u0DCA\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF \\u0D9A\\u0DCF\\u0DBD\\u0DDA\\u0DAF\\u0DD3 \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC0\\u0DA9\\u0DCF\\u0DAD\\u0DCA \\u0DC3\\u0DD4\\u0DAF\\u0DD4\\u0DC3\\u0DD4\\u0DBA. \\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DB6\\u0DCA\\u200D\\u0DBB\\u0DC4\\u0DCA\\u0DB8 \\u0DB8\\u0DD4\\u0DC4\\u0DD4\\u0DBB\\u0DCA\\u0DAD\\u0DBA (\\u0D85\\u0DBD\\u0DD4\\u0DBA\\u0DB8 4:30 - 5:30 \\u0DB4\\u0DB8\\u0DAB) \\u0D89\\u0DAD\\u0DCF \\u0DB6\\u0DBD\\u0D9C\\u0DAD\\u0DD4\\u0DBA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 175,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"guideline-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"guideline-title\",\n                  children: \"2. \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DB1\\u0DBA:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DB1\\u0DD2\\u0DC0\\u0DC3\\u0DDA \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4, \\u0DB1\\u0DD2\\u0DC3\\u0DCA\\u0D9A\\u0DBD\\u0D82\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DB1\\u0DBA\\u0D9A\\u0DCA \\u0DAD\\u0DDD\\u0DBB\\u0DCF\\u0D9C\\u0DB1\\u0DCA\\u0DB1. \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DB1\\u0DB8\\u0DCA \\u0DB4\\u0DD6\\u0DA2\\u0DCF\\u0DC3\\u0DB1\\u0DBA\\u0D9A\\u0DCA \\u0DC3\\u0D9A\\u0DC3\\u0DCF \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DBB\\u0DD6\\u0DB4\\u0DBA\\u0D9A\\u0DCA \\u0DC4\\u0DDD \\u0DBA\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0D9A\\u0DCA \\u0DAD\\u0DB6\\u0DCF \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DB8\\u0DB1\\u0DC3 \\u0D92\\u0D9A\\u0DCF\\u0D9C\\u0DCA\\u200D\\u0DBB \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8 \\u0DB4\\u0DC4\\u0DC3\\u0DD4\\u0DBA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 186,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"guideline-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"guideline-title\",\n                  children: \"3. \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 194,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DC1\\u0DCF\\u0DBB\\u0DD3\\u0DBB\\u0DD2\\u0D9A \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8 \\u0DB8\\u0DD9\\u0DB1\\u0DCA\\u0DB8 \\u0DB8\\u0DCF\\u0DB1\\u0DC3\\u0DD2\\u0D9A \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8\\u0DAF \\u0D85\\u0DAD\\u0DD2\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA\\u0DBA. \\u0D9A\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4 \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DDD\\u0DB0\\u0DBA\\u0D9A\\u0DCA, \\u0DC0\\u0DDB\\u0DBB\\u0DBA\\u0D9A\\u0DCA \\u0DC4\\u0DDD \\u0DB1\\u0DD2\\u0DC2\\u0DDA\\u0DB0\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0D9A \\u0DC3\\u0DD2\\u0DAD\\u0DD4\\u0DC0\\u0DD2\\u0DBD\\u0DCA\\u0DBD\\u0D9A\\u0DCA \\u0DC3\\u0DD2\\u0DAD\\u0DDA \\u0DAD\\u0DB6\\u0DCF \\u0DB1\\u0DDC\\u0D9C\\u0DD9\\u0DB1, \\u0DC3\\u0DD0\\u0DC4\\u0DD0\\u0DBD\\u0DCA\\u0DBD\\u0DD4 \\u0DB8\\u0DB1\\u0DC3\\u0D9A\\u0DD2\\u0DB1\\u0DCA \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0D86\\u0DBB\\u0DB8\\u0DCA\\u0DB7 \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"guideline-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"guideline-title\",\n                  children: \"4. \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 203,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DB1\\u0DD2\\u0DC3\\u0DCF, \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DBB\\u0DB1 \\u0DC0\\u0DD2\\u0DA7 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0DB8\\u0DD4\\u0DC4\\u0DD4\\u0DAB\\u0DBD\\u0DCF \\u0DC0\\u0DCF\\u0DA9\\u0DD2 \\u0DC0\\u0DD3\\u0DB8 \\u0D89\\u0DAD\\u0DCF \\u0DBA\\u0DDD\\u0D9C\\u0DCA\\u200D\\u0DBA\\u0DBA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 204,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 202,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"guideline-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"guideline-title\",\n                  children: \"5. \\u0DA2\\u0DB4 \\u0D9A\\u0DBB\\u0DB1 \\u0DC0\\u0DCF\\u0DBB \\u0D9C\\u0DAB\\u0DB1:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DC3\\u0DCA\\u0DB5\\u0DA7\\u0DD2\\u0D9A, \\u0DBB\\u0DD4\\u0DAF\\u0DCA\\u200D\\u0DBB\\u0DCF\\u0D9A\\u0DCA\\u0DC2 \\u0DC4\\u0DDD \\u0DAD\\u0DD4\\u0DBD\\u0DCA\\u0DC3\\u0DD2 (\\u0DB8\\u0DAF\\u0DD4\\u0DBB\\u0DD4\\u0DAD\\u0DBD\\u0DCF) \\u0D87\\u0DA7\\u0DC0\\u0DBD\\u0DD2\\u0DB1\\u0DCA \\u0DC3\\u0DD0\\u0DAF\\u0DD6 \\u0DA2\\u0DB4\\u0DB8\\u0DCF\\u0DBD\\u0DBA\\u0D9A\\u0DCA \\u0DB7\\u0DCF\\u0DC0\\u0DD2\\u0DAD \\u0D9A\\u0DBB 108 \\u0DC0\\u0DAD\\u0DCF\\u0DC0\\u0D9A\\u0DCA \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DBA\\u0DD2. \\u0D86\\u0DBB\\u0DB8\\u0DCA\\u0DB7\\u0DDA\\u0DAF\\u0DD3 \\u0D94\\u0DB6\\u0DA7 \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DC0\\u0DCF\\u0DBB \\u0D9C\\u0DAB\\u0DB1\\u0D9A\\u0DCA (\\u0D8B\\u0DAF\\u0DCF: 9, 27, 54) \\u0DA2\\u0DB4 \\u0D9A\\u0DBB \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0DD9\\u0DB1\\u0DCA 108 \\u0DAF\\u0D9A\\u0DCA\\u0DC0\\u0DCF \\u0DC0\\u0DD0\\u0DA9\\u0DD2 \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"guideline-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"guideline-title\",\n                  children: \"6. \\u0DB4\\u0DD6\\u0DA2\\u0DCF\\u0DC0:\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 221,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DA7 \\u0DB4\\u0DD9\\u0DBB \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA \\u0D8B\\u0DAF\\u0DD9\\u0DC3\\u0DCF \\u0DB4\\u0DC4\\u0DB1\\u0D9A\\u0DCA \\u0DAF\\u0DD0\\u0DBD\\u0DCA\\u0DC0\\u0DD3\\u0DB8, \\u0DC3\\u0DD4\\u0DC0\\u0DB3 \\u0DC4\\u0DB3\\u0DD4\\u0DB1\\u0DCA\\u0D9A\\u0DD6\\u0DBB\\u0D9A\\u0DCA \\u0DB4\\u0DAD\\u0DCA\\u0DAD\\u0DD4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC3\\u0DC4 \\u0DB1\\u0DD0\\u0DC0\\u0DD4\\u0DB8\\u0DCA \\u0DB8\\u0DBD\\u0DCA \\u0D9A\\u0DD2\\u0DC4\\u0DD2\\u0DB4\\u0DBA\\u0D9A\\u0DCA \\u0DB4\\u0DD6\\u0DA2\\u0DCF \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DB7\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCA\\u0DB0\\u0DCF\\u0DC0 \\u0DB4\\u0DCA\\u200D\\u0DBB\\u0D9A\\u0DCF\\u0DC1 \\u0D9A\\u0DC5 \\u0DC4\\u0DD0\\u0D9A\\u0DD2\\u0DBA.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 164,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kubera-content-card dark-glass-card benefits-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-shine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 237,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"content-title\",\n              children: \"\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAD\\u0DD2\\u0DBD\\u0DCF\\u0DB7 \\u0DC3\\u0DC4 \\u0DB1\\u0DD2\\u0DC0\\u0DD0\\u0DBB\\u0DAF\\u0DD2 \\u0DB8\\u0DCF\\u0DB1\\u0DC3\\u0DD2\\u0D9A \\u0D86\\u0D9A\\u0DBD\\u0DCA\\u0DB4\\u0DBA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 240,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 239,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"benefits-list\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-icon\",\n                  children: \"\\uD83D\\uDCB0\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0DC0\\u0DD3\\u0DB8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 248,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"\\u0DBB\\u0DD0\\u0D9A\\u0DD2\\u0DBA\\u0DCF\\u0DC0\\u0DDA, \\u0DC0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DB4\\u0DCF\\u0DBB\\u0DDA \\u0DC4\\u0DDD \\u0DC0\\u0DD9\\u0DB1\\u0DAD\\u0DCA \\u0D86\\u0DAF\\u0DCF\\u0DBA\\u0DB8\\u0DCA \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C\\u0DC0\\u0DBD \\u0D87\\u0DAD\\u0DD2 \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0DC0\\u0DD3 \\u0DBA\\u0DC4\\u0DB4\\u0DAD \\u0D8B\\u0DAF\\u0DCF\\u0DC0\\u0DDA.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 249,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 247,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-icon\",\n                  children: \"\\uD83C\\uDF1F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u0DB1\\u0DC0 \\u0D86\\u0DAF\\u0DCF\\u0DBA\\u0DB8\\u0DCA \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C \\u0DC0\\u0DD2\\u0DC0\\u0DD8\\u0DAD \\u0DC0\\u0DD3\\u0DB8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"\\u0DB0\\u0DB1\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0DC0\\u0DD3\\u0DB8\\u0DA7 \\u0DB1\\u0DC0 \\u0D85\\u0DC0\\u0DC3\\u0DCA\\u0DAE\\u0DCF \\u0DC3\\u0DC4 \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C \\u0DC0\\u0DD2\\u0DC0\\u0DD8\\u0DAD\\u0DC0\\u0DDA.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 260,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 258,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-icon\",\n                  children: \"\\uD83C\\uDFE6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 267,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u0DAB\\u0DBA \\u0DB6\\u0DBB\\u0DD2\\u0DB1\\u0DCA \\u0DB8\\u0DD2\\u0DAF\\u0DD3\\u0DB8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 269,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"\\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DC0\\u0DBB\\u0DAD\\u0DCA\\u0DC0\\u0DBA\\u0D9A\\u0DCA \\u0D87\\u0DAD\\u0DD2\\u0DC0\\u0DD3\\u0DB8 \\u0DB1\\u0DD2\\u0DC3\\u0DCF \\u0DAB\\u0DBA\\u0DAD\\u0DD4\\u0DBB\\u0DD4\\u0DC3\\u0DCA \\u0DC0\\u0DBD\\u0DD2\\u0DB1\\u0DCA \\u0DB1\\u0DD2\\u0DAF\\u0DC4\\u0DC3\\u0DCA \\u0DC0\\u0DD3\\u0DB8\\u0DA7 \\u0DB8\\u0D9C \\u0DB4\\u0DD1\\u0DAF\\u0DDA.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 270,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"benefit-item\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-icon\",\n                  children: \"\\uD83D\\uDEE1\\uFE0F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"benefit-content\",\n                  children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                    children: \"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DD4\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0DD2\\u0DAD \\u0DC0\\u0DD3\\u0DB8\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    children: \"\\u0D8B\\u0DB4\\u0DBA\\u0DB1 \\u0DBD\\u0DAF \\u0DB0\\u0DB1\\u0DBA \\u0D85\\u0DB1\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA \\u0DBD\\u0DD9\\u0DC3 \\u0DC0\\u0DD2\\u0DBA\\u0DAF\\u0DB8\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DD3 \\u0D89\\u0DAD\\u0DD2\\u0DBB\\u0DD2 \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DA7 \\u0DC3\\u0DC4 \\u0DC0\\u0DBB\\u0DCA\\u0DB0\\u0DB1\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DA7 \\u0D85\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DBD\\u0DD0\\u0DB6\\u0DDA.\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 280,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 278,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 276,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-content-section\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"kubera-content-card dark-glass-card important-notes-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-glow\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 294,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"card-shine\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-header\",\n            children: /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"content-title\",\n              children: \"\\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA\\u0DB8 \\u0DAF\\u0DDA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-body\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"important-note\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DBA\\u0DB1\\u0DD4 \\u0DB8\\u0DD0\\u0DA2\\u0DD2\\u0D9A\\u0DCA \\u0DBA\\u0DC2\\u0DCA\\u0DA7\\u0DD2\\u0DBA\\u0D9A\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DB1 \\u0DB6\\u0DC0 \\u0DAD\\u0DDA\\u0DBB\\u0DD4\\u0DB8\\u0DCA \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DBA\\u0DD2. \\u0D91\\u0DBA \\u0D9A\\u0DCA\\u0DC2\\u0DAB\\u0DD2\\u0D9A\\u0DC0 \\u0DB8\\u0DD4\\u0DAF\\u0DBD\\u0DCA \\u0DB8\\u0DC0\\u0DB1 \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0D9A\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DDA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 303,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DD2\\u0DAF\\u0DD4 \\u0DC0\\u0DB1\\u0DCA\\u0DB1\\u0DDA \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DC3\\u0DD2\\u0DAD\\u0DD4\\u0DC0\\u0DD2\\u0DBD\\u0DD2 \\u0DC3\\u0DC4 \\u0D9A\\u0DB8\\u0DCA\\u0DB4\\u0DB1 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA, \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0D9C\\u0DDA \\u0DC3\\u0DC4 \\u0DB0\\u0DB1\\u0D9C\\u0DDA \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC4\\u0DCF \\u0D85\\u0DB1\\u0DD4\\u0D9C\\u0DAD \\u0DC0\\u0DD3\\u0DB8\\u0DBA\\u0DD2.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                children: \"\\u0D91\\u0DB6\\u0DD0\\u0DC0\\u0DD2\\u0DB1\\u0DCA, \\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCA\\u0DB0\\u0DCF\\u0DC0, \\u0DB7\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0D9A\\u0DD0\\u0DB4\\u0DC0\\u0DD3\\u0DB8 \\u0DBA\\u0DB1 \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DD4 \\u0DAD\\u0DD4\\u0DB1 \\u0DB8\\u0DAD \\u0DB4\\u0DAF\\u0DB1\\u0DB8\\u0DCA\\u0DC0, \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DC0\\u0DD9\\u0DC4\\u0DD9\\u0DC3 \\u0DB8\\u0DC4\\u0DB1\\u0DCA\\u0DC3\\u0DD2 \\u0DC0\\u0DD3 \\u0D9A\\u0DBB\\u0DB1 \\u0D8B\\u0DAD\\u0DCA\\u0DC3\\u0DCF\\u0DC4\\u0DBA\\u0DA7 \\u0DB8\\u0DD9\\u0DB8 \\u0D85\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DB4\\u0DD4\\u0DC4\\u0DD4\\u0DAB\\u0DD4\\u0DC0\\u0DAF \\u0D91\\u0D9A\\u0DCA \\u0D9A\\u0DBB \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D94\\u0DB6\\u0DA7 \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0D89\\u0DBD\\u0D9A\\u0DCA\\u0D9A \\u0DC3\\u0DB5\\u0DBD \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DDA \\u0DB8\\u0DCF\\u0DC0\\u0DAD \\u0DC0\\u0DD2\\u0DC0\\u0DBB \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A\\u0DD2\\u0DBA.\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 301,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 293,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 292,\n        columnNumber: 7\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"kubera-footer\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"divine-blessing\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"blessing-text\",\n            children: \"\\uD83D\\uDE4F \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0D94\\u0DB6 \\u0DC3\\u0DB8\\u0D9F \\u0DC0\\u0DDA\\u0DC0\\u0DCF \\uD83D\\uDE4F\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 9\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 323,\n        columnNumber: 7\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 46,\n    columnNumber: 5\n  }, this);\n};\n_s(LandingPage, \"UnaGnsU1SFyGYic2fOp+4havgpA=\", false, function () {\n  return [useAnalytics, useComponentTracking];\n});\n_c = LandingPage;\nexport default LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");", "map": {"version": 3, "names": ["React", "useEffect", "ParticleBackground", "KuberaAnimation", "KuberaCardSection", "useAnalytics", "useComponentTracking", "jsxDEV", "_jsxDEV", "LandingPage", "_s", "analytics", "trackEvent", "event_category", "page_type", "content_type", "cards", "document", "querySelectorAll", "for<PERSON>ach", "card", "index", "style", "animationDelay", "classList", "add", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "card_index", "card_delay", "unobserve", "target", "threshold", "observe", "Translation<PERSON><PERSON>der", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "position", "top", "right", "zIndex", "LanguageSelector", "<PERSON><PERSON><PERSON><PERSON>", "mainTitle", "subtitle", "blessing", "importanceTitle", "whoIsKuberaTitle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport KuberaCardSection from './KuberaCardSection';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\n\nconst LandingPage = () => {\n  const analytics = useAnalytics();\n\n  // Track component mounting\n  useComponentTracking('LandingPage');\n\n  useEffect(() => {\n    // Track landing page view with additional context\n    analytics.trackEvent('landing_page_loaded', {\n      event_category: 'page_interaction',\n      page_type: 'landing',\n      content_type: 'kubera_guide'\n    });\n\n    // Add floating animation to content cards with staggered delay\n    const cards = document.querySelectorAll('.kubera-content-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.2}s`;\n      card.classList.add('floating');\n\n      // Track card visibility\n      const observer = new IntersectionObserver((entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            analytics.trackEvent('content_card_viewed', {\n              event_category: 'content_engagement',\n              card_index: index,\n              card_delay: index * 0.2\n            });\n            observer.unobserve(entry.target);\n          }\n        });\n      }, { threshold: 0.5 });\n\n      observer.observe(card);\n    });\n  }, [analytics]);\n\n  return (\n    <TranslationLoader>\n      <div className=\"landing-page kubera-guide-page\">\n      <ParticleBackground />\n      <KuberaAnimation />\n\n      {/* Language Selector */}\n      <div style={{\n        position: 'fixed',\n        top: '20px',\n        right: '20px',\n        zIndex: 1001\n      }}>\n        <LanguageSelector />\n      </div>\n\n      {/* Header Section */}\n      <div className=\"landing-header\">\n        <h1 className=\"main-title\">{translatedContent.mainTitle || 'කුබේර මන්ත්‍රේ බලය'}</h1>\n        <h2 className=\"subtitle\">{translatedContent.subtitle || 'ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීමේ සම්පූර්ණ මාර්ගෝපදේශය'}</h2>\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">{translatedContent.blessing || '🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ 🙏'}</span>\n        </div>\n      </div>\n\n      {/* Kubera Card Product Section */}\n      <KuberaCardSection />\n\n\n\n      {/* Introduction Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">{translatedContent.importanceTitle || 'කුබේර මන්ත්‍රයේ වැදගත්කම'}</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <p>\n              සෑම පුද්ගලයකුගේම ජීවිතේ මූලික ස්ථාවරත්වය සහ සෞභාග්‍යය යනු ඉතා වැදගත්\n              අංගයකි. වෛදික සහ හින්දු සම්ප්‍රදායන්ට අනුව, විශ්වේ ඇති ධනයට අධිපති සහ එහි\n              භාරකරු වන්නේ කුබේර දෙවියන්ය.\n            </p>\n            <p>\n              එතුමන්ගේ ආශීර්වාදය ලබා ගැනීමෙන් ධන සම්පත්, සමෘද්ධිය සහ ජීවිතේ මූලික\n              බාධක ඉවත් කරගත හැකි බවට ගැඹුරු විශ්වාසයක් පවතී.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Who is Kubera Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">{translatedContent.whoIsKuberaTitle || 'කවුද මේ කුබේර දෙවියන්?'}</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <p>\n              හින්දු දේව පුරාණයට අනුව, කුබේර යනු යක්ෂයන්ට අධිපති, දෙවියන්ගේ භාණ්ඩාගාරික\n              සහ උතුරු දිශාවේ ආරක්ෂකයා (දික්පාල) ලෙස සැලකේ.\n            </p>\n            <p>\n              ශ්‍රී ලාංකික අපට කුබේර යනු වඩාත් සමීප චරිතයකි, මන්ද එතුමන් ලංකාපුරේ රාවණ\n              රජුගේ අර්ධ සහෝදරයා ලෙසද සැලකෙන බැවිනි. බෞද්ධ සම්ප්‍රදායේදී එතුමන්\n              \"වෛශ්‍රවණ\" (වෙසමුණි) ලෙස හඳුන්වනු ලබන අතර, සතරවරම් දෙවිවරුන්ගෙන් කෙනෙකු\n              ලෙස උතුරු දිශාවට අධිපතිත්වය දරයි.\n            </p>\n            <p>\n              කුබේර යනු කෑදර ලෙස ධනය රැස් කරන්නෙකු නොව, ධාර්මික ව උපයන ලද ධනය\n              ලෝකයට බෙදාහරින පාලකයෙකි.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* The Mantra Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card mantra-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">බලගතු කුබේර මන්ත්‍රය සහ එහි තේරුම</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"mantra-section\">\n              <h4 className=\"mantra-subtitle\">මන්ත්‍රය (සංස්කෘත):</h4>\n              <div className=\"sanskrit-mantra\">\n                ॐ यक्षाय कुबेराय वैश्रवणाय धनधान्याधिपतये<br/>\n                धनधान्यसमृद्धिं मे देहि दापय स्वाहा ॥\n              </div>\n\n              <h4 className=\"mantra-subtitle\">උච්චාරණය සඳහා (සිංහල):</h4>\n              <div className=\"sinhala-pronunciation\">\n                ඕම් යක්ෂාය කුබේරාය වෛශ්‍රවණාය ධනධාන්‍යාධිපතයේ,<br/>\n                ධනධාන්‍ය සමෘද්ධිම් මේ දේහි දාපය ස්වාහා ॥\n              </div>\n\n              <h4 className=\"mantra-subtitle\">මන්ත්‍රේ සරල අර්ථය:</h4>\n              <div className=\"mantra-meaning\">\n                \"ඕම්, යක්ෂයන්ගේ රජු වූත්, ධනයට සහ ධාන්‍යයට අධිපති වූත්, වෛශ්‍රවණ ලෙසද\n                හැඳින්වෙන කුබේර දෙවියනි, ඔබ වහන්සේට මම නමස්කාර කරමි. කරුණාකර මට\n                ධනයෙන් සහ ධාන්‍යයෙන් සමෘද්ධිය ලබා දෙනු මැනව.\"\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* How to Use Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">මන්ත්‍රය නිවැරදිව භාවිත කරන ආකාරය</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"usage-guidelines\">\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">1. සුදුසුම වේලාව:</h4>\n                <p>\n                  දිනපතා උදෑසන ස්නානය කිරීමෙන් අනතුරුව පිරිසිදුව හෝ සන්ධ්‍යා කාලේදී\n                  මන්ත්‍රය ජප කිරීම වඩාත් සුදුසුය. විශේෂයෙන් බ්‍රහ්ම මුහුර්තය\n                  (අලුයම 4:30 - 5:30 පමණ) ඉතා බලගතුය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">2. ස්ථානය:</h4>\n                <p>\n                  නිවසේ පිරිසිදු, නිස්කලංක ස්ථානයක් තෝරාගන්න. හැකි නම් පූජාසනයක්\n                  සකසා කුබේර දෙවියන්ගේ රූපයක් හෝ යන්ත්‍රයක් තබා ගැනීමෙන් මනස\n                  ඒකාග්‍ර කරගැනීම පහසුය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">3. පිරිසිදුකම:</h4>\n                <p>\n                  ශාරීරික පිරිසිදුකම මෙන්ම මානසික පිරිසිදුකමද අතිවශ්‍යය. කිසිදු\n                  ක්‍රෝධයක්, වෛරයක් හෝ නිෂේධාත්මක සිතුවිල්ලක් සිතේ තබා නොගෙන,\n                  සැහැල්ලු මනසකින් මන්ත්‍ර ජප කිරීම ආරම්භ කරන්න.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">4. දිශාව:</h4>\n                <p>\n                  කුබේර දෙවියන් උතුරු දිශාවට අධිපති නිසා, මන්ත්‍රය ජප කරන විට උතුරු\n                  දිශාවට මුහුණලා වාඩි වීම ඉතා යෝග්‍යය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">5. ජප කරන වාර ගණන:</h4>\n                <p>\n                  ස්ඵටික, රුද්‍රාක්ෂ හෝ තුල්සි (මදුරුතලා) ඇටවලින් සැදූ ජපමාලයක්\n                  භාවිත කර 108 වතාවක් මන්ත්‍රය ජප කිරීම සම්ප්‍රදායයි. ආරම්භේදී\n                  ඔබට හැකි වාර ගණනක් (උදා: 9, 27, 54) ජප කර ක්‍රමයෙන් 108 දක්වා\n                  වැඩි කරගත හැක.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">6. පූජාව:</h4>\n                <p>\n                  මන්ත්‍රය ජප කිරීමට පෙර කුබේර දෙවියන් උදෙසා පහනක් දැල්වීම, සුවඳ\n                  හඳුන්කූරක් පත්තු කිරීම සහ නැවුම් මල් කිහිපයක් පූජා කිරීමෙන් ඔබගේ\n                  භක්තිය සහ ශ්‍රද්ධාව ප්‍රකාශ කළ හැකිය.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Benefits Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card benefits-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">ප්‍රතිලාභ සහ නිවැරදි මානසික ආකල්පය</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"benefits-list\">\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">💰</div>\n                <div className=\"benefit-content\">\n                  <h4>මූලික බාධක ඉවත් වීම</h4>\n                  <p>\n                    රැකියාවේ, ව්‍යාපාරේ හෝ වෙනත් ආදායම් මාර්ගවල ඇති බාධක ක්‍රමයෙන්\n                    ඉවත් වී යහපත උදාවේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🌟</div>\n                <div className=\"benefit-content\">\n                  <h4>නව ආදායම් මාර්ග විවෘත වීම</h4>\n                  <p>\n                    ධනය ආකර්ෂණය වීමට නව අවස්ථා සහ මාර්ග විවෘතවේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🏦</div>\n                <div className=\"benefit-content\">\n                  <h4>ණය බරින් මිදීම</h4>\n                  <p>\n                    මූලික ස්ථාවරත්වයක් ඇතිවීම නිසා ණයතුරුස් වලින් නිදහස් වීමට මග පෑදේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🛡️</div>\n                <div className=\"benefit-content\">\n                  <h4>ධනය සුරක්ෂිත වීම</h4>\n                  <p>\n                    උපයන ලද ධනය අනවශ්‍ය ලෙස වියදම් නොවී ඉතිරි කරගැනීමට සහ\n                    වර්ධනය කරගැනීමට අවශ්‍ය ශක්තිය ලැබේ.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Important Notes Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card important-notes-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">වැදගත්ම දේ</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"important-note\">\n              <p>\n                කුබේර මන්ත්‍රය යනු මැජික් යෂ්ටියක් නොවන බව තේරුම් ගැනීමයි.\n                එය ක්ෂණිකව මුදල් මවන ක්‍රමයක් නොවේ.\n              </p>\n              <p>\n                මන්ත්‍ර ජප කිරීමෙන් සිදු වන්නේ ඔබගේ සිතුවිලි සහ කම්පන ශක්තිය,\n                සමෘද්ධිගේ සහ ධනගේ විශ්ව ශක්තිය හා අනුගත වීමයි.\n              </p>\n              <p>\n                එබැවින්, ශ්‍රද්ධාව, භක්තිය සහ කැපවීම යන කරුණු තුන මත පදනම්ව,\n                ඔබගේ වෙහෙස මහන්සි වී කරන උත්සාහයට මෙම අධ්‍යාත්මික පුහුණුවද\n                එක් කර ගැනීමෙන් ඔබට ඔබගේ මූලික ඉලක්ක සඵල කරගැනීමේ මාවත\n                විවර කරගත හැකිය.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer with Blessing */}\n      <div className=\"kubera-footer\">\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>\n        </div>\n      </div>\n    </div>\n    </TranslationLoader>\n  );\n};\n\nexport default LandingPage;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxB,MAAMC,SAAS,GAAGN,YAAY,CAAC,CAAC;;EAEhC;EACAC,oBAAoB,CAAC,aAAa,CAAC;EAEnCL,SAAS,CAAC,MAAM;IACd;IACAU,SAAS,CAACC,UAAU,CAAC,qBAAqB,EAAE;MAC1CC,cAAc,EAAE,kBAAkB;MAClCC,SAAS,EAAE,SAAS;MACpBC,YAAY,EAAE;IAChB,CAAC,CAAC;;IAEF;IACA,MAAMC,KAAK,GAAGC,QAAQ,CAACC,gBAAgB,CAAC,sBAAsB,CAAC;IAC/DF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAK;MAC7BD,IAAI,CAACE,KAAK,CAACC,cAAc,GAAG,GAAGF,KAAK,GAAG,GAAG,GAAG;MAC7CD,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC;;MAE9B;MACA,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;QACrDA,OAAO,CAACT,OAAO,CAACU,KAAK,IAAI;UACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;YACxBnB,SAAS,CAACC,UAAU,CAAC,qBAAqB,EAAE;cAC1CC,cAAc,EAAE,oBAAoB;cACpCkB,UAAU,EAAEV,KAAK;cACjBW,UAAU,EAAEX,KAAK,GAAG;YACtB,CAAC,CAAC;YACFK,QAAQ,CAACO,SAAS,CAACJ,KAAK,CAACK,MAAM,CAAC;UAClC;QACF,CAAC,CAAC;MACJ,CAAC,EAAE;QAAEC,SAAS,EAAE;MAAI,CAAC,CAAC;MAEtBT,QAAQ,CAACU,OAAO,CAAChB,IAAI,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,SAAS,CAAC,CAAC;EAEf,oBACEH,OAAA,CAAC6B,iBAAiB;IAAAC,QAAA,eAChB9B,OAAA;MAAK+B,SAAS,EAAC,gCAAgC;MAAAD,QAAA,gBAC/C9B,OAAA,CAACN,kBAAkB;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtBnC,OAAA,CAACL,eAAe;QAAAqC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGnBnC,OAAA;QAAKc,KAAK,EAAE;UACVsB,QAAQ,EAAE,OAAO;UACjBC,GAAG,EAAE,MAAM;UACXC,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE;QACV,CAAE;QAAAT,QAAA,eACA9B,OAAA,CAACwC,gBAAgB;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,gBAAgB;QAAAD,QAAA,gBAC7B9B,OAAA;UAAI+B,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAEW,iBAAiB,CAACC,SAAS,IAAI;QAAoB;UAAAV,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrFnC,OAAA;UAAI+B,SAAS,EAAC,UAAU;UAAAD,QAAA,EAAEW,iBAAiB,CAACE,QAAQ,IAAI;QAAuD;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACrHnC,OAAA;UAAK+B,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9B9B,OAAA;YAAM+B,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAEW,iBAAiB,CAACG,QAAQ,IAAI;UAAqC;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA,CAACJ,iBAAiB;QAAAoC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAKrBnC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC9B,OAAA;UAAK+B,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAClD9B,OAAA;YAAK+B,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnC,OAAA;YAAK+B,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAElCnC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7B9B,OAAA;cAAI+B,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAEW,iBAAiB,CAACI,eAAe,IAAI;YAA0B;cAAAb,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjG,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3B9B,OAAA;cAAA8B,QAAA,EAAG;YAIH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnC,OAAA;cAAA8B,QAAA,EAAG;YAGH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC9B,OAAA;UAAK+B,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAClD9B,OAAA;YAAK+B,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnC,OAAA;YAAK+B,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAElCnC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7B9B,OAAA;cAAI+B,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAEW,iBAAiB,CAACK,gBAAgB,IAAI;YAAwB;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChG,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAD,QAAA,gBAC3B9B,OAAA;cAAA8B,QAAA,EAAG;YAGH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnC,OAAA;cAAA8B,QAAA,EAAG;YAKH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJnC,OAAA;cAAA8B,QAAA,EAAG;YAGH;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC9B,OAAA;UAAK+B,SAAS,EAAC,iDAAiD;UAAAD,QAAA,gBAC9D9B,OAAA;YAAK+B,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnC,OAAA;YAAK+B,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAElCnC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7B9B,OAAA;cAAI+B,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B9B,OAAA;cAAK+B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B9B,OAAA;gBAAI+B,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDnC,OAAA;gBAAK+B,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,GAAC,oOACU,eAAA9B,OAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,yMAEhD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnC,OAAA;gBAAI+B,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC3DnC,OAAA;gBAAK+B,SAAS,EAAC,uBAAuB;gBAAAD,QAAA,GAAC,6PACS,eAAA9B,OAAA;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,sNAErD;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAENnC,OAAA;gBAAI+B,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,EAAC;cAAmB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACxDnC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,EAAC;cAIhC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC9B,OAAA;UAAK+B,SAAS,EAAC,qCAAqC;UAAAD,QAAA,gBAClD9B,OAAA;YAAK+B,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnC,OAAA;YAAK+B,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAElCnC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7B9B,OAAA;cAAI+B,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAiC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B9B,OAAA;cAAK+B,SAAS,EAAC,kBAAkB;cAAAD,QAAA,gBAC/B9B,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7B9B,OAAA;kBAAI+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,EAAC;gBAAiB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtDnC,OAAA;kBAAA8B,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7B9B,OAAA;kBAAI+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,EAAC;gBAAU;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/CnC,OAAA;kBAAA8B,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7B9B,OAAA;kBAAI+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,EAAC;gBAAc;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACnDnC,OAAA;kBAAA8B,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7B9B,OAAA;kBAAI+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9CnC,OAAA;kBAAA8B,QAAA,EAAG;gBAGH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7B9B,OAAA;kBAAI+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,EAAC;gBAAkB;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvDnC,OAAA;kBAAA8B,QAAA,EAAG;gBAKH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,gBAAgB;gBAAAD,QAAA,gBAC7B9B,OAAA;kBAAI+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,EAAC;gBAAS;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9CnC,OAAA;kBAAA8B,QAAA,EAAG;gBAIH;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC9B,OAAA;UAAK+B,SAAS,EAAC,mDAAmD;UAAAD,QAAA,gBAChE9B,OAAA;YAAK+B,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnC,OAAA;YAAK+B,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAElCnC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7B9B,OAAA;cAAI+B,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAkC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClE,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B9B,OAAA;cAAK+B,SAAS,EAAC,eAAe;cAAAD,QAAA,gBAC5B9B,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC3B9B,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtCnC,OAAA;kBAAK+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9B,OAAA;oBAAA8B,QAAA,EAAI;kBAAmB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAC5BnC,OAAA;oBAAA8B,QAAA,EAAG;kBAGH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC3B9B,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtCnC,OAAA;kBAAK+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9B,OAAA;oBAAA8B,QAAA,EAAI;kBAAyB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eAClCnC,OAAA;oBAAA8B,QAAA,EAAG;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC3B9B,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtCnC,OAAA;kBAAK+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9B,OAAA;oBAAA8B,QAAA,EAAI;kBAAc;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACvBnC,OAAA;oBAAA8B,QAAA,EAAG;kBAEH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eAENnC,OAAA;gBAAK+B,SAAS,EAAC,cAAc;gBAAAD,QAAA,gBAC3B9B,OAAA;kBAAK+B,SAAS,EAAC,cAAc;kBAAAD,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACvCnC,OAAA;kBAAK+B,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAC9B9B,OAAA;oBAAA8B,QAAA,EAAI;kBAAgB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzBnC,OAAA;oBAAA8B,QAAA,EAAG;kBAGH;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,wBAAwB;QAAAD,QAAA,eACrC9B,OAAA;UAAK+B,SAAS,EAAC,0DAA0D;UAAAD,QAAA,gBACvE9B,OAAA;YAAK+B,SAAS,EAAC;UAAW;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACjCnC,OAAA;YAAK+B,SAAS,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAElCnC,OAAA;YAAK+B,SAAS,EAAC,gBAAgB;YAAAD,QAAA,eAC7B9B,OAAA;cAAI+B,SAAS,EAAC,eAAe;cAAAD,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C,CAAC,eAENnC,OAAA;YAAK+B,SAAS,EAAC,cAAc;YAAAD,QAAA,eAC3B9B,OAAA;cAAK+B,SAAS,EAAC,gBAAgB;cAAAD,QAAA,gBAC7B9B,OAAA;gBAAA8B,QAAA,EAAG;cAGH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnC,OAAA;gBAAA8B,QAAA,EAAG;cAGH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC,eACJnC,OAAA;gBAAA8B,QAAA,EAAG;cAKH;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK+B,SAAS,EAAC,eAAe;QAAAD,QAAA,eAC5B9B,OAAA;UAAK+B,SAAS,EAAC,iBAAiB;UAAAD,QAAA,eAC9B9B,OAAA;YAAM+B,SAAS,EAAC,eAAe;YAAAD,QAAA,EAAC;UAA2C;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACa,CAAC;AAExB,CAAC;AAACjC,EAAA,CApUID,WAAW;EAAA,QACGJ,YAAY,EAG9BC,oBAAoB;AAAA;AAAAiD,EAAA,GAJhB9C,WAAW;AAsUjB,eAAeA,WAAW;AAAC,IAAA8C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}