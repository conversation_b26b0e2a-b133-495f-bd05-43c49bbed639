{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\KuberaCardSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KuberaCardSection = () => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [showCheckout, setShowCheckout] = useState(false);\n\n  // Kubera Card product images (placeholder URLs - you can replace with actual images)\n  const productImages = ['/images/kubera-card-1.jpg', '/images/kubera-card-2.jpg', '/images/kubera-card-3.jpg', '/images/kubera-card-4.jpg'];\n\n  // Auto slider functionality\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentImageIndex(prevIndex => (prevIndex + 1) % productImages.length);\n    }, 3000); // Change image every 3 seconds\n\n    return () => clearInterval(interval);\n  }, [productImages.length]);\n  const handleBuyNow = () => {\n    setShowCheckout(true);\n  };\n  const nextImage = () => {\n    setCurrentImageIndex(prevIndex => (prevIndex + 1) % productImages.length);\n  };\n  const prevImage = () => {\n    setCurrentImageIndex(prevIndex => prevIndex === 0 ? productImages.length - 1 : prevIndex - 1);\n  };\n  const goToImage = index => {\n    setCurrentImageIndex(index);\n  };\n  if (showCheckout) {\n    return /*#__PURE__*/_jsxDEV(KuberaCheckout, {\n      onClose: () => setShowCheckout(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"kubera-card-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-card-container dark-glass-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-glow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 53,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-shine\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"product-title\",\n          children: \"\\uD83C\\uDFB4 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0D9A\\u0DCF\\u0DA9\\u0DCA\\u0DB4\\u0DAD\\u0DCA \\uD83C\\uDFB4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"product-subtitle\",\n          children: \"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8 \\u0DC3\\u0DB3\\u0DC4\\u0DCF \\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0D9A\\u0DCF\\u0DA9\\u0DCA\\u0DB4\\u0DAD\\u0DCA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-slider\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"slider-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"slider-btn prev-btn\",\n            onClick: prevImage,\n            children: \"\\u2039\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: productImages[currentImageIndex],\n              alt: `කුබේර කාඩ්පත් ${currentImageIndex + 1}`,\n              className: \"product-image\",\n              onError: e => {\n                // Fallback to a placeholder if image doesn't exist\n                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2Y0ZDAzZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuC2muC3lOC2tOC3mOC2u+C2uyDgtrDgtrDgtrDgt4Pgtrrgtr3gt4Pgtrsg4La04LeS4La6PC90ZXh0Pjwvc3ZnPg==';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlay-content\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"image-counter\",\n                  children: [currentImageIndex + 1, \" / \", productImages.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 83,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"slider-btn next-btn\",\n            onClick: nextImage,\n            children: \"\\u203A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 92,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"slider-dots\",\n          children: productImages.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `dot ${index === currentImageIndex ? 'active' : ''}`,\n            onClick: () => goToImage(index)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DC3\\u0DC4\\u0DD2\\u0DAD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0DB0\\u0DB1\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0D9A\\u0DBB\\u0DB1 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDEE1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 121,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0D8B\\u0DC3\\u0DC3\\u0DCA \\u0DAD\\u0DAD\\u0DCA\\u0DAD\\u0DCA\\u0DC0\\u0DBA\\u0DDA \\u0D9A\\u0DCF\\u0DA9\\u0DCA\\u0DB4\\u0DAD\\u0DCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 120,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0D86\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0DD2\\u0DAD \\u0D87\\u0DC3\\u0DD4\\u0DBB\\u0DD4\\u0DB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 126,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-pricing\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"original-price\",\n              children: \"\\u0DBB\\u0DD4. 2,500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-price\",\n              children: \"\\u0DBB\\u0DD4. 1,999\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 133,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"discount-badge\",\n              children: \"20% OFF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 134,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 131,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"price-note\",\n            children: \"* \\u0DB1\\u0DDC\\u0DB8\\u0DD2\\u0DBD\\u0DDA \\u0D9C\\u0DD9\\u0DAF\\u0DBB \\u0D9C\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DCF \\u0DAF\\u0DD3\\u0DB8 (Cash on Delivery)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 136,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"buy-now-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"buy-now-btn\",\n          onClick: handleBuyNow,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-icon\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 145,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-text\",\n            children: \"\\u0DAF\\u0DD0\\u0DB1\\u0DCA\\u0DB8 \\u0DB8\\u0DD2\\u0DBD\\u0DAF\\u0DD3 \\u0D9C\\u0DB1\\u0DCA\\u0DB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 146,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-arrow\",\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 144,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-method\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"payment-icon\",\n              children: \"\\uD83D\\uDCB3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"payment-text\",\n              children: \"Cash on Delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"delivery-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"delivery-icon\",\n              children: \"\\uD83D\\uDE9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"delivery-text\",\n              children: \"2-3 \\u0DAF\\u0DD2\\u0DB1\\u0D9A\\u0DD2\\u0DB1\\u0DCA \\u0D9C\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DCF \\u0DAF\\u0DD3\\u0DB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 150,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 52,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 51,\n    columnNumber: 5\n  }, this);\n};\n\n// Placeholder for checkout component (will be created separately)\n_s(KuberaCardSection, \"brNItE3OrC7EFPy381UYKY09h90=\");\n_c = KuberaCardSection;\nconst KuberaCheckout = ({\n  onClose\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"checkout-overlay\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"checkout-container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        children: \"Checkout Component\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Checkout functionality will be implemented here\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 172,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: onClose,\n        children: \"Close\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 169,\n    columnNumber: 5\n  }, this);\n};\n_c2 = KuberaCheckout;\nexport default KuberaCardSection;\nvar _c, _c2;\n$RefreshReg$(_c, \"KuberaCardSection\");\n$RefreshReg$(_c2, \"KuberaCheckout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "KuberaCardSection", "_s", "currentImageIndex", "setCurrentImageIndex", "showCheckout", "setShowCheckout", "productImages", "interval", "setInterval", "prevIndex", "length", "clearInterval", "handleBuyNow", "nextImage", "prevImage", "goToImage", "index", "KuberaCheckout", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "src", "alt", "onError", "e", "target", "map", "_", "_c", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/KuberaCardSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst KuberaCardSection = () => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [showCheckout, setShowCheckout] = useState(false);\n\n  // Kubera Card product images (placeholder URLs - you can replace with actual images)\n  const productImages = [\n    '/images/kubera-card-1.jpg',\n    '/images/kubera-card-2.jpg',\n    '/images/kubera-card-3.jpg',\n    '/images/kubera-card-4.jpg'\n  ];\n\n  // Auto slider functionality\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setCurrentImageIndex((prevIndex) => \n        (prevIndex + 1) % productImages.length\n      );\n    }, 3000); // Change image every 3 seconds\n\n    return () => clearInterval(interval);\n  }, [productImages.length]);\n\n  const handleBuyNow = () => {\n    setShowCheckout(true);\n  };\n\n  const nextImage = () => {\n    setCurrentImageIndex((prevIndex) => \n      (prevIndex + 1) % productImages.length\n    );\n  };\n\n  const prevImage = () => {\n    setCurrentImageIndex((prevIndex) => \n      prevIndex === 0 ? productImages.length - 1 : prevIndex - 1\n    );\n  };\n\n  const goToImage = (index) => {\n    setCurrentImageIndex(index);\n  };\n\n  if (showCheckout) {\n    return <KuberaCheckout onClose={() => setShowCheckout(false)} />;\n  }\n\n  return (\n    <div className=\"kubera-card-section\">\n      <div className=\"kubera-card-container dark-glass-card\">\n        <div className=\"card-glow\"></div>\n        <div className=\"card-shine\"></div>\n\n        {/* Product Header */}\n        <div className=\"product-header\">\n          <h3 className=\"product-title\">🎴 කුබේර කාඩ්පත් 🎴</h3>\n          <p className=\"product-subtitle\">\n            ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීම සඳහා විශේෂ කුබේර කාඩ්පත්\n          </p>\n        </div>\n\n        {/* Product Image Slider */}\n        <div className=\"product-slider\">\n          <div className=\"slider-container\">\n            <button className=\"slider-btn prev-btn\" onClick={prevImage}>\n              ‹\n            </button>\n            \n            <div className=\"image-container\">\n              <img \n                src={productImages[currentImageIndex]} \n                alt={`කුබේර කාඩ්පත් ${currentImageIndex + 1}`}\n                className=\"product-image\"\n                onError={(e) => {\n                  // Fallback to a placeholder if image doesn't exist\n                  e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAwIiBoZWlnaHQ9IjMwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjMzMzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxOCIgZmlsbD0iI2Y0ZDAzZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuC2muC3lOC2tOC3mOC2u+C2uyDgtrDgtrDgtrDgt4Pgtrrgtr3gt4Pgtrsg4La04LeS4La6PC90ZXh0Pjwvc3ZnPg==';\n                }}\n              />\n              \n              {/* Image overlay with product info */}\n              <div className=\"image-overlay\">\n                <div className=\"overlay-content\">\n                  <span className=\"image-counter\">\n                    {currentImageIndex + 1} / {productImages.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n            \n            <button className=\"slider-btn next-btn\" onClick={nextImage}>\n              ›\n            </button>\n          </div>\n\n          {/* Slider Dots */}\n          <div className=\"slider-dots\">\n            {productImages.map((_, index) => (\n              <button\n                key={index}\n                className={`dot ${index === currentImageIndex ? 'active' : ''}`}\n                onClick={() => goToImage(index)}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Product Details */}\n        <div className=\"product-details\">\n          <div className=\"product-features\">\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">✨</span>\n              <span className=\"feature-text\">විශේෂ කුබේර මන්ත්‍ර සහිත</span>\n            </div>\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">🎯</span>\n              <span className=\"feature-text\">ධනය ආකර්ෂණය කරන ශක්තිය</span>\n            </div>\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">🛡️</span>\n              <span className=\"feature-text\">උසස් තත්ත්වයේ කාඩ්පත්</span>\n            </div>\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">📦</span>\n              <span className=\"feature-text\">ආරක්ෂිත ඇසුරුම</span>\n            </div>\n          </div>\n\n          <div className=\"product-pricing\">\n            <div className=\"price-section\">\n              <span className=\"original-price\">රු. 2,500</span>\n              <span className=\"current-price\">රු. 1,999</span>\n              <span className=\"discount-badge\">20% OFF</span>\n            </div>\n            <p className=\"price-note\">\n              * නොමිලේ ගෙදර ගෙන්වා දීම (Cash on Delivery)\n            </p>\n          </div>\n        </div>\n\n        {/* Buy Now Button */}\n        <div className=\"buy-now-section\">\n          <button className=\"buy-now-btn\" onClick={handleBuyNow}>\n            <span className=\"btn-icon\">🛒</span>\n            <span className=\"btn-text\">දැන්ම මිලදී ගන්න</span>\n            <span className=\"btn-arrow\">→</span>\n          </button>\n          \n          <div className=\"payment-info\">\n            <div className=\"payment-method\">\n              <span className=\"payment-icon\">💳</span>\n              <span className=\"payment-text\">Cash on Delivery</span>\n            </div>\n            <div className=\"delivery-info\">\n              <span className=\"delivery-icon\">🚚</span>\n              <span className=\"delivery-text\">2-3 දිනකින් ගෙන්වා දීම</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Placeholder for checkout component (will be created separately)\nconst KuberaCheckout = ({ onClose }) => {\n  return (\n    <div className=\"checkout-overlay\">\n      <div className=\"checkout-container\">\n        <h3>Checkout Component</h3>\n        <p>Checkout functionality will be implemented here</p>\n        <button onClick={onClose}>Close</button>\n      </div>\n    </div>\n  );\n};\n\nexport default KuberaCardSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGP,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACQ,YAAY,EAAEC,eAAe,CAAC,GAAGT,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMU,aAAa,GAAG,CACpB,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,CAC5B;;EAED;EACAT,SAAS,CAAC,MAAM;IACd,MAAMU,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCL,oBAAoB,CAAEM,SAAS,IAC7B,CAACA,SAAS,GAAG,CAAC,IAAIH,aAAa,CAACI,MAClC,CAAC;IACH,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMC,aAAa,CAACJ,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACD,aAAa,CAACI,MAAM,CAAC,CAAC;EAE1B,MAAME,YAAY,GAAGA,CAAA,KAAM;IACzBP,eAAe,CAAC,IAAI,CAAC;EACvB,CAAC;EAED,MAAMQ,SAAS,GAAGA,CAAA,KAAM;IACtBV,oBAAoB,CAAEM,SAAS,IAC7B,CAACA,SAAS,GAAG,CAAC,IAAIH,aAAa,CAACI,MAClC,CAAC;EACH,CAAC;EAED,MAAMI,SAAS,GAAGA,CAAA,KAAM;IACtBX,oBAAoB,CAAEM,SAAS,IAC7BA,SAAS,KAAK,CAAC,GAAGH,aAAa,CAACI,MAAM,GAAG,CAAC,GAAGD,SAAS,GAAG,CAC3D,CAAC;EACH,CAAC;EAED,MAAMM,SAAS,GAAIC,KAAK,IAAK;IAC3Bb,oBAAoB,CAACa,KAAK,CAAC;EAC7B,CAAC;EAED,IAAIZ,YAAY,EAAE;IAChB,oBAAOL,OAAA,CAACkB,cAAc;MAACC,OAAO,EAAEA,CAAA,KAAMb,eAAe,CAAC,KAAK;IAAE;MAAAc,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClE;EAEA,oBACEvB,OAAA;IAAKwB,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClCzB,OAAA;MAAKwB,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzB,OAAA;QAAKwB,SAAS,EAAC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjCvB,OAAA;QAAKwB,SAAS,EAAC;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGlCvB,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzB,OAAA;UAAIwB,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDvB,OAAA;UAAGwB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvB,OAAA;QAAKwB,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzB,OAAA;UAAKwB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzB,OAAA;YAAQwB,SAAS,EAAC,qBAAqB;YAACE,OAAO,EAAEX,SAAU;YAAAU,QAAA,EAAC;UAE5D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvB,OAAA;YAAKwB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzB,OAAA;cACE2B,GAAG,EAAEpB,aAAa,CAACJ,iBAAiB,CAAE;cACtCyB,GAAG,EAAE,iBAAiBzB,iBAAiB,GAAG,CAAC,EAAG;cAC9CqB,SAAS,EAAC,eAAe;cACzBK,OAAO,EAAGC,CAAC,IAAK;gBACd;gBACAA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,oaAAoa;cACrb;YAAE;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGFvB,OAAA;cAAKwB,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzB,OAAA;gBAAKwB,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9BzB,OAAA;kBAAMwB,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAC5BtB,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACI,aAAa,CAACI,MAAM;gBAAA;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvB,OAAA;YAAQwB,SAAS,EAAC,qBAAqB;YAACE,OAAO,EAAEZ,SAAU;YAAAW,QAAA,EAAC;UAE5D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNvB,OAAA;UAAKwB,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBlB,aAAa,CAACyB,GAAG,CAAC,CAACC,CAAC,EAAEhB,KAAK,kBAC1BjB,OAAA;YAEEwB,SAAS,EAAE,OAAOP,KAAK,KAAKd,iBAAiB,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEuB,OAAO,EAAEA,CAAA,KAAMV,SAAS,CAACC,KAAK;UAAE,GAF3BA,KAAK;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKwB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzB,OAAA;UAAKwB,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCvB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAwB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNvB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCvB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAsB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNvB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCvB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNvB,OAAA;YAAKwB,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCvB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvB,OAAA;UAAKwB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzB,OAAA;YAAKwB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzB,OAAA;cAAMwB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDvB,OAAA;cAAMwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvB,OAAA;cAAMwB,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNvB,OAAA;YAAGwB,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE1B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKwB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzB,OAAA;UAAQwB,SAAS,EAAC,aAAa;UAACE,OAAO,EAAEb,YAAa;UAAAY,QAAA,gBACpDzB,OAAA;YAAMwB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCvB,OAAA;YAAMwB,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAgB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDvB,OAAA;YAAMwB,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAETvB,OAAA;UAAKwB,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzB,OAAA;YAAKwB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCvB,OAAA;cAAMwB,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNvB,OAAA;YAAKwB,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzB,OAAA;cAAMwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCvB,OAAA;cAAMwB,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAArB,EAAA,CAnKMD,iBAAiB;AAAAiC,EAAA,GAAjBjC,iBAAiB;AAoKvB,MAAMiB,cAAc,GAAGA,CAAC;EAAEC;AAAQ,CAAC,KAAK;EACtC,oBACEnB,OAAA;IAAKwB,SAAS,EAAC,kBAAkB;IAAAC,QAAA,eAC/BzB,OAAA;MAAKwB,SAAS,EAAC,oBAAoB;MAAAC,QAAA,gBACjCzB,OAAA;QAAAyB,QAAA,EAAI;MAAkB;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC3BvB,OAAA;QAAAyB,QAAA,EAAG;MAA+C;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACtDvB,OAAA;QAAQ0B,OAAO,EAAEP,OAAQ;QAAAM,QAAA,EAAC;MAAK;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACrC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACY,GAAA,GAVIjB,cAAc;AAYpB,eAAejB,iBAAiB;AAAC,IAAAiC,EAAA,EAAAC,GAAA;AAAAC,YAAA,CAAAF,EAAA;AAAAE,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}