{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\TranslationTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationTest = () => {\n  _s();\n  const {\n    currentLanguage,\n    translateText,\n    changeLanguage,\n    isTranslating\n  } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n\n      // Test direct API call first\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API URL:', apiUrl);\n      console.log('NODE_ENV:', process.env.NODE_ENV);\n      console.log('REACT_APP_API_URL:', process.env.REACT_APP_API_URL);\n      try {\n        const response = await fetch(`${apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            text: 'ආදරය',\n            targetLanguage: currentLanguage,\n            targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',\n            context: 'test'\n          })\n        });\n        console.log('Direct API response status:', response.status);\n        const data = await response.json();\n        console.log('Direct API response data:', data);\n        if (data.success) {\n          setTestResult(`Direct API: ${data.translatedText}`);\n        } else {\n          setError(`Direct API Error: ${data.error}`);\n        }\n      } catch (apiError) {\n        console.error('Direct API error:', apiError);\n        setError(`Direct API Error: ${apiError.message}`);\n      }\n\n      // Also test through translation context\n      try {\n        const result = await translateText('ආදරය', currentLanguage, 'test');\n        console.log('Context translation result:', result);\n        setTestResult(prev => prev + ` | Context: ${result}`);\n      } catch (contextError) {\n        console.error('Context translation error:', contextError);\n        setError(prev => prev + ` | Context Error: ${contextError.message}`);\n      }\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Translation Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Current Language: \", currentLanguage]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Is Translating: \", isTranslating ? 'Yes' : 'No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('sinhala'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Sinhala\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('english'),\n        style: {\n          margin: '5px'\n        },\n        children: \"English\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 98,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('tamil'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Tamil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 101,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 94,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: testTranslation,\n      disabled: isTranslating,\n      children: \"Test Translation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'lightgreen'\n      },\n      children: testResult\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'red'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 79,\n    columnNumber: 5\n  }, this);\n};\n_s(TranslationTest, \"ofjRKXIuHGl7R9hpsA1r9bfRQW4=\", false, function () {\n  return [useTranslation];\n});\n_c = TranslationTest;\nexport default TranslationTest;\nvar _c;\n$RefreshReg$(_c, \"TranslationTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "jsxDEV", "_jsxDEV", "TranslationTest", "_s", "currentLanguage", "translateText", "changeLanguage", "isTranslating", "testResult", "setTestResult", "error", "setError", "testTranslation", "console", "log", "apiUrl", "process", "env", "REACT_APP_API_URL", "NODE_ENV", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "text", "targetLanguage", "targetLanguageName", "context", "status", "data", "json", "success", "translatedText", "apiError", "message", "result", "prev", "contextError", "err", "style", "position", "top", "left", "background", "color", "padding", "borderRadius", "zIndex", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "margin", "disabled", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/TranslationTest.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\nconst TranslationTest = () => {\n  const { currentLanguage, translateText, changeLanguage, isTranslating } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n\n      // Test direct API call first\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API URL:', apiUrl);\n      console.log('NODE_ENV:', process.env.NODE_ENV);\n      console.log('REACT_APP_API_URL:', process.env.REACT_APP_API_URL);\n\n      try {\n        const response = await fetch(`${apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            text: 'ආදරය',\n            targetLanguage: currentLanguage,\n            targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',\n            context: 'test'\n          })\n        });\n\n        console.log('Direct API response status:', response.status);\n        const data = await response.json();\n        console.log('Direct API response data:', data);\n\n        if (data.success) {\n          setTestResult(`Direct API: ${data.translatedText}`);\n        } else {\n          setError(`Direct API Error: ${data.error}`);\n        }\n      } catch (apiError) {\n        console.error('Direct API error:', apiError);\n        setError(`Direct API Error: ${apiError.message}`);\n      }\n\n      // Also test through translation context\n      try {\n        const result = await translateText('ආදරය', currentLanguage, 'test');\n        console.log('Context translation result:', result);\n        setTestResult(prev => prev + ` | Context: ${result}`);\n      } catch (contextError) {\n        console.error('Context translation error:', contextError);\n        setError(prev => prev + ` | Context Error: ${contextError.message}`);\n      }\n\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    }}>\n      <h3>Translation Test</h3>\n      <p>Current Language: {currentLanguage}</p>\n      <p>Is Translating: {isTranslating ? 'Yes' : 'No'}</p>\n      \n      <div style={{ marginBottom: '10px' }}>\n        <button onClick={() => changeLanguage('sinhala')} style={{ margin: '5px' }}>\n          Sinhala\n        </button>\n        <button onClick={() => changeLanguage('english')} style={{ margin: '5px' }}>\n          English\n        </button>\n        <button onClick={() => changeLanguage('tamil')} style={{ margin: '5px' }}>\n          Tamil\n        </button>\n      </div>\n      \n      <button onClick={testTranslation} disabled={isTranslating}>\n        Test Translation\n      </button>\n      \n      {testResult && (\n        <div style={{ marginTop: '10px', color: 'lightgreen' }}>\n          {testResult}\n        </div>\n      )}\n      \n      {error && (\n        <div style={{ marginTop: '10px', color: 'red' }}>\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TranslationTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,eAAe;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC1F,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFD,QAAQ,CAAC,EAAE,CAAC;MACZF,aAAa,CAAC,YAAY,CAAC;MAE3BI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;MACjDS,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,OAAOT,aAAa,CAAC;MAEpE,IAAID,eAAe,KAAK,SAAS,EAAE;QACjCK,aAAa,CAAC,oDAAoD,CAAC;QACnE;MACF;;MAEA;MACA,MAAMM,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MAC3EL,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,MAAM,CAAC;MAC/BF,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEE,OAAO,CAACC,GAAG,CAACE,QAAQ,CAAC;MAC9CN,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEE,OAAO,CAACC,GAAG,CAACC,iBAAiB,CAAC;MAEhE,IAAI;QACF,MAAME,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGN,MAAM,YAAY,EAAE;UAClDO,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,IAAI,EAAE,MAAM;YACZC,cAAc,EAAExB,eAAe;YAC/ByB,kBAAkB,EAAEzB,eAAe,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;YACvE0B,OAAO,EAAE;UACX,CAAC;QACH,CAAC,CAAC;QAEFjB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEM,QAAQ,CAACW,MAAM,CAAC;QAC3D,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;QAClCpB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEkB,IAAI,CAAC;QAE9C,IAAIA,IAAI,CAACE,OAAO,EAAE;UAChBzB,aAAa,CAAC,eAAeuB,IAAI,CAACG,cAAc,EAAE,CAAC;QACrD,CAAC,MAAM;UACLxB,QAAQ,CAAC,qBAAqBqB,IAAI,CAACtB,KAAK,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC,OAAO0B,QAAQ,EAAE;QACjBvB,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAE0B,QAAQ,CAAC;QAC5CzB,QAAQ,CAAC,qBAAqByB,QAAQ,CAACC,OAAO,EAAE,CAAC;MACnD;;MAEA;MACA,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMjC,aAAa,CAAC,MAAM,EAAED,eAAe,EAAE,MAAM,CAAC;QACnES,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEwB,MAAM,CAAC;QAClD7B,aAAa,CAAC8B,IAAI,IAAIA,IAAI,GAAG,eAAeD,MAAM,EAAE,CAAC;MACvD,CAAC,CAAC,OAAOE,YAAY,EAAE;QACrB3B,OAAO,CAACH,KAAK,CAAC,4BAA4B,EAAE8B,YAAY,CAAC;QACzD7B,QAAQ,CAAC4B,IAAI,IAAIA,IAAI,GAAG,qBAAqBC,YAAY,CAACH,OAAO,EAAE,CAAC;MACtE;IAEF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ5B,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAE+B,GAAG,CAAC;MAC7C9B,QAAQ,CAAC,UAAU8B,GAAG,CAACJ,OAAO,EAAE,CAAC;IACnC;EACF,CAAC;EAEDvC,SAAS,CAAC,MAAM;IACde,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;IACjDS,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,aAAa,CAAC;EAC/C,CAAC,EAAE,CAACH,eAAe,EAAEG,aAAa,CAAC,CAAC;EAEpC,oBACEN,OAAA;IAAKyC,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,iBAAiB;MAC7BC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACAnD,OAAA;MAAAmD,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBvD,OAAA;MAAAmD,QAAA,GAAG,oBAAkB,EAAChD,eAAe;IAAA;MAAAiD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1CvD,OAAA;MAAAmD,QAAA,GAAG,kBAAgB,EAAC7C,aAAa,GAAG,KAAK,GAAG,IAAI;IAAA;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErDvD,OAAA;MAAKyC,KAAK,EAAE;QAAEe,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnCnD,OAAA;QAAQyD,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAAC,SAAS,CAAE;QAACoC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvD,OAAA;QAAQyD,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAAC,SAAS,CAAE;QAACoC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvD,OAAA;QAAQyD,OAAO,EAAEA,CAAA,KAAMpD,cAAc,CAAC,OAAO,CAAE;QAACoC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE1E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENvD,OAAA;MAAQyD,OAAO,EAAE9C,eAAgB;MAACgD,QAAQ,EAAErD,aAAc;MAAA6C,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAERhD,UAAU,iBACTP,OAAA;MAAKyC,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAa,CAAE;MAAAK,QAAA,EACpD5C;IAAU;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,EAEA9C,KAAK,iBACJT,OAAA;MAAKyC,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAM,CAAE;MAAAK,QAAA,EAC7C1C;IAAK;MAAA2C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACrD,EAAA,CAvHID,eAAe;EAAA,QACuDH,cAAc;AAAA;AAAA+D,EAAA,GADpF5D,eAAe;AAyHrB,eAAeA,eAAe;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}