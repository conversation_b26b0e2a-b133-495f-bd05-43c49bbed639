{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useCallback}from'react';import{useTranslation}from'../contexts/TranslationContext';// Custom hook for managing translated content\nexport const useTranslatedContent=()=>{const{currentLanguage,translateText,translateMultiple,isTranslating}=useTranslation();const[translatedTexts,setTranslatedTexts]=useState({});const[isLoading,setIsLoading]=useState(false);// Translate a single text\nconst getTranslatedText=useCallback(async function(originalText){let context=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';if(!originalText||currentLanguage==='sinhala'){return originalText;}const cacheKey=\"\".concat(originalText,\"_\").concat(currentLanguage,\"_\").concat(context);// Return cached translation if available\nif(translatedTexts[cacheKey]){return translatedTexts[cacheKey];}try{setIsLoading(true);const translated=await translateText(originalText,currentLanguage,context);// Cache the translation\nsetTranslatedTexts(prev=>_objectSpread(_objectSpread({},prev),{},{[cacheKey]:translated}));return translated;}catch(error){console.error('Translation error:',error);return originalText;// Fallback to original text\n}finally{setIsLoading(false);}},[currentLanguage,translateText,translatedTexts]);// Translate horoscope categories\nconst getTranslatedCategories=useCallback(async categories=>{if(!categories||categories.length===0||currentLanguage==='sinhala'){return categories;}try{setIsLoading(true);const translatedCategories=await Promise.all(categories.map(async category=>{const titleKey=\"\".concat(category.title,\"_\").concat(currentLanguage,\"_category_title\");const contentKey=\"\".concat(category.content,\"_\").concat(currentLanguage,\"_horoscope_content\");// Check cache first\nconst cachedTitle=translatedTexts[titleKey];const cachedContent=translatedTexts[contentKey];let translatedTitle=cachedTitle;let translatedContent=cachedContent;// Translate title if not cached\nif(!cachedTitle){translatedTitle=await translateText(category.title,currentLanguage,'category_title');setTranslatedTexts(prev=>_objectSpread(_objectSpread({},prev),{},{[titleKey]:translatedTitle}));}// Translate content if not cached\nif(!cachedContent){translatedContent=await translateText(category.content,currentLanguage,'horoscope_content');setTranslatedTexts(prev=>_objectSpread(_objectSpread({},prev),{},{[contentKey]:translatedContent}));}return _objectSpread(_objectSpread({},category),{},{title:translatedTitle,content:translatedContent});}));return translatedCategories;}catch(error){console.error('Categories translation error:',error);return categories;// Return original categories on error\n}finally{setIsLoading(false);}},[currentLanguage,translateText,translatedTexts]);// Clear translations when language changes\nuseEffect(()=>{setTranslatedTexts({});},[currentLanguage]);return{getTranslatedText,getTranslatedCategories,isLoading:isLoading||isTranslating,currentLanguage};};// Hook for UI text translations\nexport const useUITranslations=()=>{const{getTranslatedText,currentLanguage}=useTranslatedContent();const uiTexts={sinhala:{backToHome:'← මුල් පිටුවට',horoscopeDate:'📅 රාශිඵල දිනය',refreshHoroscope:'🔄 නව රාශිඵලයක්',loading:'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.',error:'රාශිඵල ලබා ගැනීමේදී දෝෂයක් සිදු විය. කරුණාකර නැවත උත්සාහ කරන්න.',lastUpdated:'අවසන් වරට යාවත්කාලීන කළේ',refreshing:'නව රාශිඵලයක් ලබා ගනිමින්...',zodiacSign:'රාශිය',soundOn:'🔊 ශබ්දය නිශ්ශබ්ද කරන්න',soundOff:'🔇 ශබ්දය සක්‍රිය කරන්න',spiritualMessage:'\"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"',todaysHoroscope:'අද දිනයේ රාශිඵල'},english:{backToHome:'← Back to Home',horoscopeDate:'📅 Horoscope Date',refreshHoroscope:'🔄 New Horoscope',loading:'Preparing horoscope... Please wait.',error:'An error occurred while fetching the horoscope. Please try again.',lastUpdated:'Last updated',refreshing:'Getting new horoscope...',zodiacSign:'Sign',soundOn:'🔊 Mute Sound',soundOff:'🔇 Enable Sound',spiritualMessage:'\"May Lord Kubera\\'s blessings be with you\"',todaysHoroscope:'Today\\'s Horoscope'},tamil:{backToHome:'← முகப்புக்கு திரும்பு',horoscopeDate:'📅 ராசிபலன் தேதி',refreshHoroscope:'🔄 புதிய ராசிபலன்',loading:'ராசிபலன் தயாராகிறது... தயவுசெய்து காத்திருக்கவும்.',error:'ராசிபலன் பெறுவதில் பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.',lastUpdated:'கடைசியாக புதுப்பிக்கப்பட்டது',refreshing:'புதிய ராசிபலன் பெறுகிறது...',zodiacSign:'ராசி',soundOn:'🔊 ஒலியை அமைதிப்படுத்து',soundOff:'🔇 ஒலியை இயக்கு',spiritualMessage:'\"குபேர பகவானின் ஆசீர்வாதம் உங்களுடன் இருக்கட்டும்\"',todaysHoroscope:'இன்றைய ராசிபலன்'}};const getUIText=key=>{var _uiTexts$currentLangu;return((_uiTexts$currentLangu=uiTexts[currentLanguage])===null||_uiTexts$currentLangu===void 0?void 0:_uiTexts$currentLangu[key])||uiTexts.sinhala[key]||key;};return{getUIText};};export default useTranslatedContent;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useTranslation", "useTranslatedContent", "currentLanguage", "translateText", "translateMultiple", "isTranslating", "translatedTexts", "setTranslatedTexts", "isLoading", "setIsLoading", "getTranslatedText", "originalText", "context", "arguments", "length", "undefined", "cache<PERSON>ey", "concat", "translated", "prev", "_objectSpread", "error", "console", "getTranslatedCategories", "categories", "translatedCategories", "Promise", "all", "map", "category", "<PERSON><PERSON><PERSON>", "title", "contentKey", "content", "cachedTitle", "cachedContent", "translatedTitle", "<PERSON><PERSON><PERSON><PERSON>", "useUITranslations", "uiTexts", "sinhala", "backToHome", "horoscopeDate", "refreshHoroscope", "loading", "lastUpdated", "refreshing", "zodiacSign", "soundOn", "soundOff", "spiritualMessage", "todaysHoroscope", "english", "tamil", "getUIText", "key", "_uiTexts$currentLangu"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/hooks/useTranslatedContent.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\n// Custom hook for managing translated content\nexport const useTranslatedContent = () => {\n  const { currentLanguage, translateText, translateMultiple, isTranslating } = useTranslation();\n  const [translatedTexts, setTranslatedTexts] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Translate a single text\n  const getTranslatedText = useCallback(async (originalText, context = '') => {\n    if (!originalText || currentLanguage === 'sinhala') {\n      return originalText;\n    }\n\n    const cacheKey = `${originalText}_${currentLanguage}_${context}`;\n    \n    // Return cached translation if available\n    if (translatedTexts[cacheKey]) {\n      return translatedTexts[cacheKey];\n    }\n\n    try {\n      setIsLoading(true);\n      const translated = await translateText(originalText, currentLanguage, context);\n      \n      // Cache the translation\n      setTranslatedTexts(prev => ({\n        ...prev,\n        [cacheKey]: translated\n      }));\n\n      return translated;\n    } catch (error) {\n      console.error('Translation error:', error);\n      return originalText; // Fallback to original text\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentLanguage, translateText, translatedTexts]);\n\n  // Translate horoscope categories\n  const getTranslatedCategories = useCallback(async (categories) => {\n    if (!categories || categories.length === 0 || currentLanguage === 'sinhala') {\n      return categories;\n    }\n\n    try {\n      setIsLoading(true);\n      const translatedCategories = await Promise.all(\n        categories.map(async (category) => {\n          const titleKey = `${category.title}_${currentLanguage}_category_title`;\n          const contentKey = `${category.content}_${currentLanguage}_horoscope_content`;\n\n          // Check cache first\n          const cachedTitle = translatedTexts[titleKey];\n          const cachedContent = translatedTexts[contentKey];\n\n          let translatedTitle = cachedTitle;\n          let translatedContent = cachedContent;\n\n          // Translate title if not cached\n          if (!cachedTitle) {\n            translatedTitle = await translateText(category.title, currentLanguage, 'category_title');\n            setTranslatedTexts(prev => ({\n              ...prev,\n              [titleKey]: translatedTitle\n            }));\n          }\n\n          // Translate content if not cached\n          if (!cachedContent) {\n            translatedContent = await translateText(category.content, currentLanguage, 'horoscope_content');\n            setTranslatedTexts(prev => ({\n              ...prev,\n              [contentKey]: translatedContent\n            }));\n          }\n\n          return {\n            ...category,\n            title: translatedTitle,\n            content: translatedContent\n          };\n        })\n      );\n\n      return translatedCategories;\n    } catch (error) {\n      console.error('Categories translation error:', error);\n      return categories; // Return original categories on error\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentLanguage, translateText, translatedTexts]);\n\n  // Clear translations when language changes\n  useEffect(() => {\n    setTranslatedTexts({});\n  }, [currentLanguage]);\n\n  return {\n    getTranslatedText,\n    getTranslatedCategories,\n    isLoading: isLoading || isTranslating,\n    currentLanguage\n  };\n};\n\n// Hook for UI text translations\nexport const useUITranslations = () => {\n  const { getTranslatedText, currentLanguage } = useTranslatedContent();\n\n  const uiTexts = {\n    sinhala: {\n      backToHome: '← මුල් පිටුවට',\n      horoscopeDate: '📅 රාශිඵල දිනය',\n      refreshHoroscope: '🔄 නව රාශිඵලයක්',\n      loading: 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.',\n      error: 'රාශිඵල ලබා ගැනීමේදී දෝෂයක් සිදු විය. කරුණාකර නැවත උත්සාහ කරන්න.',\n      lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',\n      refreshing: 'නව රාශිඵලයක් ලබා ගනිමින්...',\n      zodiacSign: 'රාශිය',\n      soundOn: '🔊 ශබ්දය නිශ්ශබ්ද කරන්න',\n      soundOff: '🔇 ශබ්දය සක්‍රිය කරන්න',\n      spiritualMessage: '\"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"',\n      todaysHoroscope: 'අද දිනයේ රාශිඵල'\n    },\n    english: {\n      backToHome: '← Back to Home',\n      horoscopeDate: '📅 Horoscope Date',\n      refreshHoroscope: '🔄 New Horoscope',\n      loading: 'Preparing horoscope... Please wait.',\n      error: 'An error occurred while fetching the horoscope. Please try again.',\n      lastUpdated: 'Last updated',\n      refreshing: 'Getting new horoscope...',\n      zodiacSign: 'Sign',\n      soundOn: '🔊 Mute Sound',\n      soundOff: '🔇 Enable Sound',\n      spiritualMessage: '\"May Lord Kubera\\'s blessings be with you\"',\n      todaysHoroscope: 'Today\\'s Horoscope'\n    },\n    tamil: {\n      backToHome: '← முகப்புக்கு திரும்பு',\n      horoscopeDate: '📅 ராசிபலன் தேதி',\n      refreshHoroscope: '🔄 புதிய ராசிபலன்',\n      loading: 'ராசிபலன் தயாராகிறது... தயவுசெய்து காத்திருக்கவும்.',\n      error: 'ராசிபலன் பெறுவதில் பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.',\n      lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',\n      refreshing: 'புதிய ராசிபலன் பெறுகிறது...',\n      zodiacSign: 'ராசி',\n      soundOn: '🔊 ஒலியை அமைதிப்படுத்து',\n      soundOff: '🔇 ஒலியை இயக்கு',\n      spiritualMessage: '\"குபேர பகவானின் ஆசீர்வாதம் உங்களுடன் இருக்கட்டும்\"',\n      todaysHoroscope: 'இன்றைய ராசிபலன்'\n    }\n  };\n\n  const getUIText = (key) => {\n    return uiTexts[currentLanguage]?.[key] || uiTexts.sinhala[key] || key;\n  };\n\n  return { getUIText };\n};\n\nexport default useTranslatedContent;\n"], "mappings": "qHAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CACxD,OAASC,cAAc,KAAQ,gCAAgC,CAE/D;AACA,MAAO,MAAM,CAAAC,oBAAoB,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAEC,eAAe,CAAEC,aAAa,CAAEC,iBAAiB,CAAEC,aAAc,CAAC,CAAGL,cAAc,CAAC,CAAC,CAC7F,KAAM,CAACM,eAAe,CAAEC,kBAAkB,CAAC,CAAGV,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC1D,KAAM,CAACW,SAAS,CAAEC,YAAY,CAAC,CAAGZ,QAAQ,CAAC,KAAK,CAAC,CAEjD;AACA,KAAM,CAAAa,iBAAiB,CAAGX,WAAW,CAAC,eAAOY,YAAY,CAAmB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACrE,GAAI,CAACF,YAAY,EAAIT,eAAe,GAAK,SAAS,CAAE,CAClD,MAAO,CAAAS,YAAY,CACrB,CAEA,KAAM,CAAAK,QAAQ,IAAAC,MAAA,CAAMN,YAAY,MAAAM,MAAA,CAAIf,eAAe,MAAAe,MAAA,CAAIL,OAAO,CAAE,CAEhE;AACA,GAAIN,eAAe,CAACU,QAAQ,CAAC,CAAE,CAC7B,MAAO,CAAAV,eAAe,CAACU,QAAQ,CAAC,CAClC,CAEA,GAAI,CACFP,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAS,UAAU,CAAG,KAAM,CAAAf,aAAa,CAACQ,YAAY,CAAET,eAAe,CAAEU,OAAO,CAAC,CAE9E;AACAL,kBAAkB,CAACY,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAClBD,IAAI,MACP,CAACH,QAAQ,EAAGE,UAAU,EACtB,CAAC,CAEH,MAAO,CAAAA,UAAU,CACnB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C,MAAO,CAAAV,YAAY,CAAE;AACvB,CAAC,OAAS,CACRF,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACP,eAAe,CAAEC,aAAa,CAAEG,eAAe,CAAC,CAAC,CAErD;AACA,KAAM,CAAAiB,uBAAuB,CAAGxB,WAAW,CAAC,KAAO,CAAAyB,UAAU,EAAK,CAChE,GAAI,CAACA,UAAU,EAAIA,UAAU,CAACV,MAAM,GAAK,CAAC,EAAIZ,eAAe,GAAK,SAAS,CAAE,CAC3E,MAAO,CAAAsB,UAAU,CACnB,CAEA,GAAI,CACFf,YAAY,CAAC,IAAI,CAAC,CAClB,KAAM,CAAAgB,oBAAoB,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CAC5CH,UAAU,CAACI,GAAG,CAAC,KAAO,CAAAC,QAAQ,EAAK,CACjC,KAAM,CAAAC,QAAQ,IAAAb,MAAA,CAAMY,QAAQ,CAACE,KAAK,MAAAd,MAAA,CAAIf,eAAe,mBAAiB,CACtE,KAAM,CAAA8B,UAAU,IAAAf,MAAA,CAAMY,QAAQ,CAACI,OAAO,MAAAhB,MAAA,CAAIf,eAAe,sBAAoB,CAE7E;AACA,KAAM,CAAAgC,WAAW,CAAG5B,eAAe,CAACwB,QAAQ,CAAC,CAC7C,KAAM,CAAAK,aAAa,CAAG7B,eAAe,CAAC0B,UAAU,CAAC,CAEjD,GAAI,CAAAI,eAAe,CAAGF,WAAW,CACjC,GAAI,CAAAG,iBAAiB,CAAGF,aAAa,CAErC;AACA,GAAI,CAACD,WAAW,CAAE,CAChBE,eAAe,CAAG,KAAM,CAAAjC,aAAa,CAAC0B,QAAQ,CAACE,KAAK,CAAE7B,eAAe,CAAE,gBAAgB,CAAC,CACxFK,kBAAkB,CAACY,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAClBD,IAAI,MACP,CAACW,QAAQ,EAAGM,eAAe,EAC3B,CAAC,CACL,CAEA;AACA,GAAI,CAACD,aAAa,CAAE,CAClBE,iBAAiB,CAAG,KAAM,CAAAlC,aAAa,CAAC0B,QAAQ,CAACI,OAAO,CAAE/B,eAAe,CAAE,mBAAmB,CAAC,CAC/FK,kBAAkB,CAACY,IAAI,EAAAC,aAAA,CAAAA,aAAA,IAClBD,IAAI,MACP,CAACa,UAAU,EAAGK,iBAAiB,EAC/B,CAAC,CACL,CAEA,OAAAjB,aAAA,CAAAA,aAAA,IACKS,QAAQ,MACXE,KAAK,CAAEK,eAAe,CACtBH,OAAO,CAAEI,iBAAiB,GAE9B,CAAC,CACH,CAAC,CAED,MAAO,CAAAZ,oBAAoB,CAC7B,CAAE,MAAOJ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrD,MAAO,CAAAG,UAAU,CAAE;AACrB,CAAC,OAAS,CACRf,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAAE,CAACP,eAAe,CAAEC,aAAa,CAAEG,eAAe,CAAC,CAAC,CAErD;AACAR,SAAS,CAAC,IAAM,CACdS,kBAAkB,CAAC,CAAC,CAAC,CAAC,CACxB,CAAC,CAAE,CAACL,eAAe,CAAC,CAAC,CAErB,MAAO,CACLQ,iBAAiB,CACjBa,uBAAuB,CACvBf,SAAS,CAAEA,SAAS,EAAIH,aAAa,CACrCH,eACF,CAAC,CACH,CAAC,CAED;AACA,MAAO,MAAM,CAAAoC,iBAAiB,CAAGA,CAAA,GAAM,CACrC,KAAM,CAAE5B,iBAAiB,CAAER,eAAgB,CAAC,CAAGD,oBAAoB,CAAC,CAAC,CAErE,KAAM,CAAAsC,OAAO,CAAG,CACdC,OAAO,CAAE,CACPC,UAAU,CAAE,eAAe,CAC3BC,aAAa,CAAE,gBAAgB,CAC/BC,gBAAgB,CAAE,iBAAiB,CACnCC,OAAO,CAAE,6CAA6C,CACtDvB,KAAK,CAAE,iEAAiE,CACxEwB,WAAW,CAAE,0BAA0B,CACvCC,UAAU,CAAE,6BAA6B,CACzCC,UAAU,CAAE,OAAO,CACnBC,OAAO,CAAE,yBAAyB,CAClCC,QAAQ,CAAE,wBAAwB,CAClCC,gBAAgB,CAAE,yCAAyC,CAC3DC,eAAe,CAAE,iBACnB,CAAC,CACDC,OAAO,CAAE,CACPX,UAAU,CAAE,gBAAgB,CAC5BC,aAAa,CAAE,mBAAmB,CAClCC,gBAAgB,CAAE,kBAAkB,CACpCC,OAAO,CAAE,qCAAqC,CAC9CvB,KAAK,CAAE,mEAAmE,CAC1EwB,WAAW,CAAE,cAAc,CAC3BC,UAAU,CAAE,0BAA0B,CACtCC,UAAU,CAAE,MAAM,CAClBC,OAAO,CAAE,eAAe,CACxBC,QAAQ,CAAE,iBAAiB,CAC3BC,gBAAgB,CAAE,4CAA4C,CAC9DC,eAAe,CAAE,oBACnB,CAAC,CACDE,KAAK,CAAE,CACLZ,UAAU,CAAE,wBAAwB,CACpCC,aAAa,CAAE,kBAAkB,CACjCC,gBAAgB,CAAE,mBAAmB,CACrCC,OAAO,CAAE,oDAAoD,CAC7DvB,KAAK,CAAE,wEAAwE,CAC/EwB,WAAW,CAAE,8BAA8B,CAC3CC,UAAU,CAAE,6BAA6B,CACzCC,UAAU,CAAE,MAAM,CAClBC,OAAO,CAAE,yBAAyB,CAClCC,QAAQ,CAAE,iBAAiB,CAC3BC,gBAAgB,CAAE,oDAAoD,CACtEC,eAAe,CAAE,iBACnB,CACF,CAAC,CAED,KAAM,CAAAG,SAAS,CAAIC,GAAG,EAAK,KAAAC,qBAAA,CACzB,MAAO,EAAAA,qBAAA,CAAAjB,OAAO,CAACrC,eAAe,CAAC,UAAAsD,qBAAA,iBAAxBA,qBAAA,CAA2BD,GAAG,CAAC,GAAIhB,OAAO,CAACC,OAAO,CAACe,GAAG,CAAC,EAAIA,GAAG,CACvE,CAAC,CAED,MAAO,CAAED,SAAU,CAAC,CACtB,CAAC,CAED,cAAe,CAAArD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}