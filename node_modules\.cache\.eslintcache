[{"/mnt/c/Users/<USER>/Desktop/Horoscope/src/index.js": "1", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/App.js": "2", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js": "3", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/AnalyticsDebugger.js": "4", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js": "5", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/hooks/useAnalytics.js": "6", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/contexts/TranslationContext.js": "7", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/analytics.js": "8", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaAnimation.js": "9", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ParticleBackground.js": "10", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/TranslationLoader.js": "11", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js": "12", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js": "13", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LanguageSelector.js": "14", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaCardSection.js": "15", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/hooks/useTranslatedContent.js": "16", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/TranslationService.js": "17", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/config/analytics.js": "18", "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaCheckout.js": "19"}, {"size": 253, "mtime": 1750790409543, "results": "20", "hashOfConfig": "21"}, {"size": 4031, "mtime": 1751813460961, "results": "22", "hashOfConfig": "21"}, {"size": 17108, "mtime": 1751813221992, "results": "23", "hashOfConfig": "21"}, {"size": 8284, "mtime": 1751565326750, "results": "24", "hashOfConfig": "21"}, {"size": 29855, "mtime": 1751813479077, "results": "25", "hashOfConfig": "21"}, {"size": 8069, "mtime": 1751564462690, "results": "26", "hashOfConfig": "21"}, {"size": 3419, "mtime": 1751809259878, "results": "27", "hashOfConfig": "21"}, {"size": 8332, "mtime": 1751565228966, "results": "28", "hashOfConfig": "21"}, {"size": 7496, "mtime": 1750614946000, "results": "29", "hashOfConfig": "21"}, {"size": 3837, "mtime": 1750614946000, "results": "30", "hashOfConfig": "21"}, {"size": 4023, "mtime": 1751810683304, "results": "31", "hashOfConfig": "21"}, {"size": 2802, "mtime": 1750614946000, "results": "32", "hashOfConfig": "21"}, {"size": 6127, "mtime": 1751389760889, "results": "33", "hashOfConfig": "21"}, {"size": 9515, "mtime": 1751813434814, "results": "34", "hashOfConfig": "21"}, {"size": 7302, "mtime": 1751564707800, "results": "35", "hashOfConfig": "21"}, {"size": 6699, "mtime": 1751810422051, "results": "36", "hashOfConfig": "21"}, {"size": 5850, "mtime": 1751811886287, "results": "37", "hashOfConfig": "21"}, {"size": 6410, "mtime": 1751565137421, "results": "38", "hashOfConfig": "21"}, {"size": 13813, "mtime": 1751565044144, "results": "39", "hashOfConfig": "21"}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "p7tm3u", {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/mnt/c/Users/<USER>/Desktop/Horoscope/src/index.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/App.js", ["97"], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/AnalyticsDebugger.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js", ["98", "99", "100"], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/hooks/useAnalytics.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/contexts/TranslationContext.js", ["101"], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/analytics.js", ["102"], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaAnimation.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/ParticleBackground.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/TranslationLoader.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/SmokeAnimation.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LanguageSelector.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaCardSection.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/hooks/useTranslatedContent.js", ["103", "104"], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/TranslationService.js", [], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/config/analytics.js", ["105"], [], "/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/KuberaCheckout.js", [], [], {"ruleId": "106", "severity": 1, "message": "107", "line": 7, "column": 10, "nodeType": "108", "messageId": "109", "endLine": 7, "endColumn": 22}, {"ruleId": "106", "severity": 1, "message": "110", "line": 365, "column": 47, "nodeType": "108", "messageId": "109", "endLine": 365, "endColumn": 65}, {"ruleId": "111", "severity": 1, "message": "112", "line": 380, "column": 6, "nodeType": "113", "endLine": 380, "endColumn": 42, "suggestions": "114"}, {"ruleId": "111", "severity": 1, "message": "115", "line": 543, "column": 6, "nodeType": "113", "endLine": 543, "endColumn": 15, "suggestions": "116"}, {"ruleId": "111", "severity": 1, "message": "117", "line": 97, "column": 6, "nodeType": "113", "endLine": 97, "endColumn": 8, "suggestions": "118"}, {"ruleId": "106", "severity": 1, "message": "119", "line": 9, "column": 3, "nodeType": "108", "messageId": "109", "endLine": 9, "endColumn": 16}, {"ruleId": "106", "severity": 1, "message": "120", "line": 6, "column": 43, "nodeType": "108", "messageId": "109", "endLine": 6, "endColumn": 60}, {"ruleId": "106", "severity": 1, "message": "121", "line": 112, "column": 11, "nodeType": "108", "messageId": "109", "endLine": 112, "endColumn": 28}, {"ruleId": "122", "severity": 1, "message": "123", "line": 230, "column": 1, "nodeType": "124", "endLine": 248, "endColumn": 3}, "no-unused-vars", "'useAnalytics' is defined but never used.", "Identifier", "unusedVar", "'translationLoading' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'categories'. Either include it or remove the dependency array.", "ArrayExpression", ["125"], "React Hook useCallback has a missing dependency: 'analytics'. Either include it or remove the dependency array.", ["126"], "React Hook useCallback has a missing dependency: 'languages'. Either include it or remove the dependency array.", ["127"], "'CUSTOM_EVENTS' is defined but never used.", "'translateMultiple' is assigned a value but never used.", "'getTranslatedText' is assigned a value but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "128", "fix": "129"}, {"desc": "130", "fix": "131"}, {"desc": "132", "fix": "133"}, "Update the dependencies array to be: [horoscope, getTranslatedCategories, categories]", {"range": "134", "text": "135"}, "Update the dependencies array to be: [analytics, sign.id]", {"range": "136", "text": "137"}, "Update the dependencies array to be: [languages]", {"range": "138", "text": "139"}, [12367, 12403], "[horoscope, getTranslatedCategories, categories]", [17769, 17778], "[analytics, sign.id]", [2771, 2773], "[languages]"]