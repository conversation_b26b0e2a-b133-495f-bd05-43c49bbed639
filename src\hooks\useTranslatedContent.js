import { useState, useEffect, useCallback } from 'react';
import { useTranslation } from '../contexts/TranslationContext';

// Custom hook for managing translated content
export const useTranslatedContent = () => {
  const { currentLanguage, translateText, translateMultiple, isTranslating } = useTranslation();
  const [translatedTexts, setTranslatedTexts] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  // Translate a single text
  const getTranslatedText = useCallback(async (originalText, context = '') => {
    if (!originalText || currentLanguage === 'sinhala') {
      return originalText;
    }

    const cacheKey = `${originalText}_${currentLanguage}_${context}`;
    
    // Return cached translation if available
    if (translatedTexts[cacheKey]) {
      return translatedTexts[cacheKey];
    }

    try {
      setIsLoading(true);
      const translated = await translateText(originalText, currentLanguage, context);
      
      // Cache the translation
      setTranslatedTexts(prev => ({
        ...prev,
        [cacheKey]: translated
      }));

      return translated;
    } catch (error) {
      console.error('Translation error:', error);
      return originalText; // Fallback to original text
    } finally {
      setIsLoading(false);
    }
  }, [currentLanguage, translateText, translatedTexts]);

  // Translate horoscope categories
  const getTranslatedCategories = useCallback(async (categories) => {
    if (!categories || categories.length === 0 || currentLanguage === 'sinhala') {
      return categories;
    }

    try {
      setIsLoading(true);
      const translatedCategories = await Promise.all(
        categories.map(async (category) => {
          const titleKey = `${category.title}_${currentLanguage}_category_title`;
          const contentKey = `${category.content}_${currentLanguage}_horoscope_content`;

          // Check cache first
          const cachedTitle = translatedTexts[titleKey];
          const cachedContent = translatedTexts[contentKey];

          let translatedTitle = cachedTitle;
          let translatedContent = cachedContent;

          // Translate title if not cached
          if (!cachedTitle) {
            translatedTitle = await translateText(category.title, currentLanguage, 'category_title');
            setTranslatedTexts(prev => ({
              ...prev,
              [titleKey]: translatedTitle
            }));
          }

          // Translate content if not cached
          if (!cachedContent) {
            translatedContent = await translateText(category.content, currentLanguage, 'horoscope_content');
            setTranslatedTexts(prev => ({
              ...prev,
              [contentKey]: translatedContent
            }));
          }

          return {
            ...category,
            title: translatedTitle,
            content: translatedContent
          };
        })
      );

      return translatedCategories;
    } catch (error) {
      console.error('Categories translation error:', error);
      return categories; // Return original categories on error
    } finally {
      setIsLoading(false);
    }
  }, [currentLanguage, translateText, translatedTexts]);

  // Clear translations when language changes
  useEffect(() => {
    setTranslatedTexts({});
  }, [currentLanguage]);

  return {
    getTranslatedText,
    getTranslatedCategories,
    isLoading: isLoading || isTranslating,
    currentLanguage
  };
};

// Hook for UI text translations
export const useUITranslations = () => {
  const { getTranslatedText, currentLanguage } = useTranslatedContent();

  const uiTexts = {
    sinhala: {
      backToHome: '← මුල් පිටුවට',
      horoscopeDate: '📅 රාශිඵල දිනය',
      refreshHoroscope: '🔄 නව රාශිඵලයක්',
      loading: 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.',
      error: 'රාශිඵල ලබා ගැනීමේදී දෝෂයක් සිදු විය. කරුණාකර නැවත උත්සාහ කරන්න.',
      lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',
      refreshing: 'නව රාශිඵලයක් ලබා ගනිමින්...',
      zodiacSign: 'රාශිය',
      soundOn: '🔊 ශබ්දය නිශ්ශබ්ද කරන්න',
      soundOff: '🔇 ශබ්දය සක්‍රිය කරන්න',
      spiritualMessage: '"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා"',
      todaysHoroscope: 'අද දිනයේ රාශිඵල'
    },
    english: {
      backToHome: '← Back to Home',
      horoscopeDate: '📅 Horoscope Date',
      refreshHoroscope: '🔄 New Horoscope',
      loading: 'Preparing horoscope... Please wait.',
      error: 'An error occurred while fetching the horoscope. Please try again.',
      lastUpdated: 'Last updated',
      refreshing: 'Getting new horoscope...',
      zodiacSign: 'Sign',
      soundOn: '🔊 Mute Sound',
      soundOff: '🔇 Enable Sound',
      spiritualMessage: '"May Lord Kubera\'s blessings be with you"',
      todaysHoroscope: 'Today\'s Horoscope'
    },
    tamil: {
      backToHome: '← முகப்புக்கு திரும்பு',
      horoscopeDate: '📅 ராசிபலன் தேதி',
      refreshHoroscope: '🔄 புதிய ராசிபலன்',
      loading: 'ராசிபலன் தயாராகிறது... தயவுசெய்து காத்திருக்கவும்.',
      error: 'ராசிபலன் பெறுவதில் பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.',
      lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',
      refreshing: 'புதிய ராசிபலன் பெறுகிறது...',
      zodiacSign: 'ராசி',
      soundOn: '🔊 ஒலியை அமைதிப்படுத்து',
      soundOff: '🔇 ஒலியை இயக்கு',
      spiritualMessage: '"குபேர பகவானின் ஆசீர்வாதம் உங்களுடன் இருக்கட்டும்"',
      todaysHoroscope: 'இன்றைய ராசிபலன்'
    }
  };

  const getUIText = (key) => {
    return uiTexts[currentLanguage]?.[key] || uiTexts.sinhala[key] || key;
  };

  return { getUIText };
};

export default useTranslatedContent;
