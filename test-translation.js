const axios = require('axios');

async function testTranslation() {
  try {
    console.log('Testing translation API...');
    
    const response = await axios.post('http://localhost:5000/api/translate', {
      text: 'ආදරය',
      targetLanguage: 'english',
      targetLanguageName: 'English',
      context: 'horoscope_content'
    }, {
      headers: {
        'Content-Type': 'application/json'
      },
      timeout: 30000
    });
    
    console.log('Translation API Response:');
    console.log(JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('Translation API Error:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Test multiple translations
async function testMultipleTranslations() {
  const testCases = [
    { text: 'ආදරය', targetLanguage: 'english', targetLanguageName: 'English' },
    { text: 'ආදරය', targetLanguage: 'tamil', targetLanguageName: 'Tamil' },
    { text: 'සෞඛ්‍යය', targetLanguage: 'english', targetLanguageName: 'English' },
    { text: 'වෘත්තීය', targetLanguage: 'english', targetLanguageName: 'English' }
  ];
  
  for (const testCase of testCases) {
    console.log(`\n--- Testing: ${testCase.text} -> ${testCase.targetLanguage} ---`);
    try {
      const response = await axios.post('http://localhost:5000/api/translate', {
        ...testCase,
        context: 'horoscope_content'
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 30000
      });
      
      console.log('Success:', response.data.translatedText);
    } catch (error) {
      console.error('Error:', error.response?.data || error.message);
    }
    
    // Wait a bit between requests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Run tests
async function runTests() {
  await testTranslation();
  await testMultipleTranslations();
}

runTests();
