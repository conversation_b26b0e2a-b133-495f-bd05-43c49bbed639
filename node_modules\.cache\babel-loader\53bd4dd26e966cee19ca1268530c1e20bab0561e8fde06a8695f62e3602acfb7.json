{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './components/LandingPage';\nimport ZodiacPage from './components/ZodiacPage';\nimport AnalyticsDebugger from './components/AnalyticsDebugger';\nimport { TranslationProvider } from './contexts/TranslationContext';\nimport { useAnalytics } from './hooks/useAnalytics';\nimport { initGA, setUserProperties } from './services/analytics';\nimport './App.css';\n\n// Cache busting utility\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst cacheBuster = Date.now();\nwindow.CACHE_VERSION = cacheBuster;\nconst zodiacSigns = [{\n  id: 'aries',\n  sinhala: 'මේෂ',\n  english: 'Aries'\n}, {\n  id: 'taurus',\n  sinhala: 'වෘෂභ',\n  english: 'Taurus'\n}, {\n  id: 'gemini',\n  sinhala: 'මිථුන',\n  english: 'Gemini'\n}, {\n  id: 'cancer',\n  sinhala: 'කටක',\n  english: 'Cancer'\n}, {\n  id: 'leo',\n  sinhala: 'සිංහ',\n  english: 'Leo'\n}, {\n  id: 'virgo',\n  sinhala: 'කන්‍යා',\n  english: 'Virgo'\n}, {\n  id: 'libra',\n  sinhala: 'තුලා',\n  english: 'Libra'\n}, {\n  id: 'scorpio',\n  sinhala: 'වෘශ්චික',\n  english: 'Scorpio'\n}, {\n  id: 'sagittarius',\n  sinhala: 'ධනු',\n  english: 'Sagittarius'\n}, {\n  id: 'capricorn',\n  sinhala: 'මකර',\n  english: 'Capricorn'\n}, {\n  id: 'aquarius',\n  sinhala: 'කුම්භ',\n  english: 'Aquarius'\n}, {\n  id: 'pisces',\n  sinhala: 'මීන',\n  english: 'Pisces'\n}];\nfunction App() {\n  _s();\n  useEffect(() => {\n    // Initialize Google Analytics\n    const gaInitialized = initGA();\n    if (gaInitialized) {\n      // Set initial user properties\n      setUserProperties({\n        website_language: 'sinhala',\n        website_type: 'astrology',\n        content_category: 'horoscope'\n      });\n    }\n\n    // Disable right-click context menu\n    const handleContextMenu = e => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts\n    const handleKeyDown = e => {\n      // F12\n      if (e.keyCode === 123) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+Shift+I (Developer Tools)\n      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+U (View Source)\n      if (e.ctrlKey && e.keyCode === 85) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+S (Save Page)\n      if (e.ctrlKey && e.keyCode === 83) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+A (Select All)\n      if (e.ctrlKey && e.keyCode === 65) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+C (Copy)\n      if (e.ctrlKey && e.keyCode === 67) {\n        e.preventDefault();\n        return false;\n      }\n    };\n\n    // Disable drag and drop\n    const handleDragStart = e => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Add event listeners\n    document.addEventListener('contextmenu', handleContextMenu);\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('dragstart', handleDragStart);\n\n    // Cleanup event listeners\n    return () => {\n      document.removeEventListener('contextmenu', handleContextMenu);\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('dragstart', handleDragStart);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(TranslationProvider, {\n    children: /*#__PURE__*/_jsxDEV(Router, {\n      future: {\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"App\",\n        children: [/*#__PURE__*/_jsxDEV(Routes, {\n          children: [/*#__PURE__*/_jsxDEV(Route, {\n            path: \"/\",\n            element: /*#__PURE__*/_jsxDEV(LandingPage, {\n              zodiacSigns: zodiacSigns\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 112,\n              columnNumber: 38\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 13\n          }, this), zodiacSigns.map(sign => /*#__PURE__*/_jsxDEV(Route, {\n            path: `/${sign.id}`,\n            element: /*#__PURE__*/_jsxDEV(ZodiacPage, {\n              sign: sign\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 26\n            }, this)\n          }, sign.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 114,\n            columnNumber: 15\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 111,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(AnalyticsDebugger, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 106,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 105,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "LandingPage", "ZodiacPage", "AnalyticsDebugger", "TranslationProvider", "useAnalytics", "initGA", "setUserProperties", "jsxDEV", "_jsxDEV", "cacheBuster", "Date", "now", "window", "CACHE_VERSION", "zodiacSigns", "id", "sinhala", "english", "App", "_s", "gaInitialized", "website_language", "website_type", "content_category", "handleContextMenu", "e", "preventDefault", "handleKeyDown", "keyCode", "ctrl<PERSON>ey", "shift<PERSON>ey", "handleDragStart", "document", "addEventListener", "removeEventListener", "children", "future", "v7_startTransition", "v7_relativeSplatPath", "className", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "sign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/App.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport LandingPage from './components/LandingPage';\nimport ZodiacPage from './components/ZodiacPage';\nimport AnalyticsDebugger from './components/AnalyticsDebugger';\n\nimport { TranslationProvider } from './contexts/TranslationContext';\nimport { useAnalytics } from './hooks/useAnalytics';\nimport { initGA, setUserProperties } from './services/analytics';\nimport './App.css';\n\n// Cache busting utility\nconst cacheBuster = Date.now();\nwindow.CACHE_VERSION = cacheBuster;\n\nconst zodiacSigns = [\n  { id: 'aries', sinhala: 'මේෂ', english: 'Aries' },\n  { id: 'taurus', sinhala: 'වෘෂභ', english: 'Taurus' },\n  { id: 'gemini', sinhala: 'මිථුන', english: 'Gemini' },\n  { id: 'cancer', sinhala: 'කටක', english: 'Cancer' },\n  { id: 'leo', sinhala: 'සිංහ', english: 'Leo' },\n  { id: 'virgo', sinhala: 'කන්‍යා', english: 'Virgo' },\n  { id: 'libra', sinhala: 'තුලා', english: 'Libra' },\n  { id: 'scorpio', sinhala: 'වෘශ්චික', english: 'Scorpio' },\n  { id: 'sagittarius', sinhala: 'ධනු', english: 'Sagittarius' },\n  { id: 'capricorn', sinhala: 'මකර', english: 'Capricorn' },\n  { id: 'aquarius', sinhala: 'කුම්භ', english: 'Aquarius' },\n  { id: 'pisces', sinhala: 'මීන', english: 'Pisces' }\n];\n\nfunction App() {\n  useEffect(() => {\n    // Initialize Google Analytics\n    const gaInitialized = initGA();\n\n    if (gaInitialized) {\n      // Set initial user properties\n      setUserProperties({\n        website_language: 'sinhala',\n        website_type: 'astrology',\n        content_category: 'horoscope'\n      });\n    }\n\n    // Disable right-click context menu\n    const handleContextMenu = (e) => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Disable F12, Ctrl+Shift+I, Ctrl+U, Ctrl+S, and other developer shortcuts\n    const handleKeyDown = (e) => {\n      // F12\n      if (e.keyCode === 123) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+Shift+I (Developer Tools)\n      if (e.ctrlKey && e.shiftKey && e.keyCode === 73) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+U (View Source)\n      if (e.ctrlKey && e.keyCode === 85) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+S (Save Page)\n      if (e.ctrlKey && e.keyCode === 83) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+A (Select All)\n      if (e.ctrlKey && e.keyCode === 65) {\n        e.preventDefault();\n        return false;\n      }\n      // Ctrl+C (Copy)\n      if (e.ctrlKey && e.keyCode === 67) {\n        e.preventDefault();\n        return false;\n      }\n    };\n\n    // Disable drag and drop\n    const handleDragStart = (e) => {\n      e.preventDefault();\n      return false;\n    };\n\n    // Add event listeners\n    document.addEventListener('contextmenu', handleContextMenu);\n    document.addEventListener('keydown', handleKeyDown);\n    document.addEventListener('dragstart', handleDragStart);\n\n    // Cleanup event listeners\n    return () => {\n      document.removeEventListener('contextmenu', handleContextMenu);\n      document.removeEventListener('keydown', handleKeyDown);\n      document.removeEventListener('dragstart', handleDragStart);\n    };\n  }, []);\n\n  return (\n    <TranslationProvider>\n      <Router future={{\n        v7_startTransition: true,\n        v7_relativeSplatPath: true\n      }}>\n        <div className=\"App\">\n          <Routes>\n            <Route path=\"/\" element={<LandingPage zodiacSigns={zodiacSigns} />} />\n            {zodiacSigns.map(sign => (\n              <Route\n                key={sign.id}\n                path={`/${sign.id}`}\n                element={<ZodiacPage sign={sign} />}\n              />\n            ))}\n          </Routes>\n\n          {/* Analytics Debugger - only shows in development */}\n          <AnalyticsDebugger />\n        </div>\n      </Router>\n    </TranslationProvider>\n  );\n}\n\nexport default App;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,iBAAiB,MAAM,gCAAgC;AAE9D,SAASC,mBAAmB,QAAQ,+BAA+B;AACnE,SAASC,YAAY,QAAQ,sBAAsB;AACnD,SAASC,MAAM,EAAEC,iBAAiB,QAAQ,sBAAsB;AAChE,OAAO,WAAW;;AAElB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;AAC9BC,MAAM,CAACC,aAAa,GAAGJ,WAAW;AAElC,MAAMK,WAAW,GAAG,CAClB;EAAEC,EAAE,EAAE,OAAO;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAQ,CAAC,EACjD;EAAEF,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,MAAM;EAAEC,OAAO,EAAE;AAAS,CAAC,EACpD;EAAEF,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE;AAAS,CAAC,EACrD;EAAEF,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAS,CAAC,EACnD;EAAEF,EAAE,EAAE,KAAK;EAAEC,OAAO,EAAE,MAAM;EAAEC,OAAO,EAAE;AAAM,CAAC,EAC9C;EAAEF,EAAE,EAAE,OAAO;EAAEC,OAAO,EAAE,QAAQ;EAAEC,OAAO,EAAE;AAAQ,CAAC,EACpD;EAAEF,EAAE,EAAE,OAAO;EAAEC,OAAO,EAAE,MAAM;EAAEC,OAAO,EAAE;AAAQ,CAAC,EAClD;EAAEF,EAAE,EAAE,SAAS;EAAEC,OAAO,EAAE,SAAS;EAAEC,OAAO,EAAE;AAAU,CAAC,EACzD;EAAEF,EAAE,EAAE,aAAa;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAc,CAAC,EAC7D;EAAEF,EAAE,EAAE,WAAW;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAY,CAAC,EACzD;EAAEF,EAAE,EAAE,UAAU;EAAEC,OAAO,EAAE,OAAO;EAAEC,OAAO,EAAE;AAAW,CAAC,EACzD;EAAEF,EAAE,EAAE,QAAQ;EAAEC,OAAO,EAAE,KAAK;EAAEC,OAAO,EAAE;AAAS,CAAC,CACpD;AAED,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbxB,SAAS,CAAC,MAAM;IACd;IACA,MAAMyB,aAAa,GAAGf,MAAM,CAAC,CAAC;IAE9B,IAAIe,aAAa,EAAE;MACjB;MACAd,iBAAiB,CAAC;QAChBe,gBAAgB,EAAE,SAAS;QAC3BC,YAAY,EAAE,WAAW;QACzBC,gBAAgB,EAAE;MACpB,CAAC,CAAC;IACJ;;IAEA;IACA,MAAMC,iBAAiB,GAAIC,CAAC,IAAK;MAC/BA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,OAAO,KAAK;IACd,CAAC;;IAED;IACA,MAAMC,aAAa,GAAIF,CAAC,IAAK;MAC3B;MACA,IAAIA,CAAC,CAACG,OAAO,KAAK,GAAG,EAAE;QACrBH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACK,QAAQ,IAAIL,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QAC/CH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;MACA;MACA,IAAID,CAAC,CAACI,OAAO,IAAIJ,CAAC,CAACG,OAAO,KAAK,EAAE,EAAE;QACjCH,CAAC,CAACC,cAAc,CAAC,CAAC;QAClB,OAAO,KAAK;MACd;IACF,CAAC;;IAED;IACA,MAAMK,eAAe,GAAIN,CAAC,IAAK;MAC7BA,CAAC,CAACC,cAAc,CAAC,CAAC;MAClB,OAAO,KAAK;IACd,CAAC;;IAED;IACAM,QAAQ,CAACC,gBAAgB,CAAC,aAAa,EAAET,iBAAiB,CAAC;IAC3DQ,QAAQ,CAACC,gBAAgB,CAAC,SAAS,EAAEN,aAAa,CAAC;IACnDK,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEF,eAAe,CAAC;;IAEvD;IACA,OAAO,MAAM;MACXC,QAAQ,CAACE,mBAAmB,CAAC,aAAa,EAAEV,iBAAiB,CAAC;MAC9DQ,QAAQ,CAACE,mBAAmB,CAAC,SAAS,EAAEP,aAAa,CAAC;MACtDK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEH,eAAe,CAAC;IAC5D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEvB,OAAA,CAACL,mBAAmB;IAAAgC,QAAA,eAClB3B,OAAA,CAACX,MAAM;MAACuC,MAAM,EAAE;QACdC,kBAAkB,EAAE,IAAI;QACxBC,oBAAoB,EAAE;MACxB,CAAE;MAAAH,QAAA,eACA3B,OAAA;QAAK+B,SAAS,EAAC,KAAK;QAAAJ,QAAA,gBAClB3B,OAAA,CAACV,MAAM;UAAAqC,QAAA,gBACL3B,OAAA,CAACT,KAAK;YAACyC,IAAI,EAAC,GAAG;YAACC,OAAO,eAAEjC,OAAA,CAACR,WAAW;cAACc,WAAW,EAAEA;YAAY;cAAA4B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,EACrE/B,WAAW,CAACgC,GAAG,CAACC,IAAI,iBACnBvC,OAAA,CAACT,KAAK;YAEJyC,IAAI,EAAE,IAAIO,IAAI,CAAChC,EAAE,EAAG;YACpB0B,OAAO,eAAEjC,OAAA,CAACP,UAAU;cAAC8C,IAAI,EAAEA;YAAK;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAE,GAF/BE,IAAI,CAAChC,EAAE;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGb,CACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGTrC,OAAA,CAACN,iBAAiB;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACU,CAAC;AAE1B;AAAC1B,EAAA,CAjGQD,GAAG;AAAA8B,EAAA,GAAH9B,GAAG;AAmGZ,eAAeA,GAAG;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}