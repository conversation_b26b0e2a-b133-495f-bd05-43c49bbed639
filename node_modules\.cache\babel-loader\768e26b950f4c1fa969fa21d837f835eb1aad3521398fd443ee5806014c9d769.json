{"ast": null, "code": "var _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport { useState, useEffect, useCallback } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\n// Custom hook for managing translated content\nexport const useTranslatedContent = () => {\n  _s();\n  const {\n    currentLanguage,\n    translateText,\n    translateMultiple,\n    isTranslating\n  } = useTranslation();\n  const [translatedTexts, setTranslatedTexts] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Translate a single text\n  const getTranslatedText = useCallback(async (originalText, context = '') => {\n    if (!originalText || currentLanguage === 'sinhala') {\n      return originalText;\n    }\n    const cacheKey = `${originalText}_${currentLanguage}_${context}`;\n\n    // Return cached translation if available\n    if (translatedTexts[cacheKey]) {\n      return translatedTexts[cacheKey];\n    }\n    try {\n      setIsLoading(true);\n      const translated = await translateText(originalText, currentLanguage, context);\n\n      // Cache the translation\n      setTranslatedTexts(prev => ({\n        ...prev,\n        [cacheKey]: translated\n      }));\n      return translated;\n    } catch (error) {\n      console.error('Translation error:', error);\n      return originalText; // Fallback to original text\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentLanguage, translateText, translatedTexts]);\n\n  // Translate horoscope categories\n  const getTranslatedCategories = useCallback(async categories => {\n    if (!categories || categories.length === 0 || currentLanguage === 'sinhala') {\n      return categories;\n    }\n    try {\n      setIsLoading(true);\n      const translatedCategories = await Promise.all(categories.map(async category => {\n        const titleKey = `${category.title}_${currentLanguage}_category_title`;\n        const contentKey = `${category.content}_${currentLanguage}_horoscope_content`;\n\n        // Check cache first\n        const cachedTitle = translatedTexts[titleKey];\n        const cachedContent = translatedTexts[contentKey];\n        let translatedTitle = cachedTitle;\n        let translatedContent = cachedContent;\n\n        // Translate title if not cached\n        if (!cachedTitle) {\n          translatedTitle = await translateText(category.title, currentLanguage, 'category_title');\n          setTranslatedTexts(prev => ({\n            ...prev,\n            [titleKey]: translatedTitle\n          }));\n        }\n\n        // Translate content if not cached\n        if (!cachedContent) {\n          translatedContent = await translateText(category.content, currentLanguage, 'horoscope_content');\n          setTranslatedTexts(prev => ({\n            ...prev,\n            [contentKey]: translatedContent\n          }));\n        }\n        return {\n          ...category,\n          title: translatedTitle,\n          content: translatedContent\n        };\n      }));\n      return translatedCategories;\n    } catch (error) {\n      console.error('Categories translation error:', error);\n      return categories; // Return original categories on error\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentLanguage, translateText, translatedTexts]);\n\n  // Clear translations when language changes\n  useEffect(() => {\n    setTranslatedTexts({});\n  }, [currentLanguage]);\n  return {\n    getTranslatedText,\n    getTranslatedCategories,\n    isLoading: isLoading || isTranslating,\n    currentLanguage\n  };\n};\n\n// Hook for UI text translations\n_s(useTranslatedContent, \"ptCP4obZiW9CzO3Z1E1Pf/9Wz/o=\", false, function () {\n  return [useTranslation];\n});\nexport const useUITranslations = () => {\n  _s2();\n  const {\n    getTranslatedText,\n    currentLanguage\n  } = useTranslatedContent();\n  const uiTexts = {\n    sinhala: {\n      backToHome: '← මුල් පිටුවට',\n      horoscopeDate: '📅 රාශිඵල දිනය',\n      refreshHoroscope: '🔄 නව රාශිඵලයක්',\n      loading: 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.',\n      error: 'රාශිඵල ලබා ගැනීමේදී දෝෂයක් සිදු විය. කරුණාකර නැවත උත්සාහ කරන්න.',\n      lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',\n      refreshing: 'නව රාශිඵලයක් ලබා ගනිමින්...',\n      zodiacSign: 'රාශිය',\n      soundOn: '🔊 ශබ්දය නිශ්ශබ්ද කරන්න',\n      soundOff: '🔇 ශබ්දය සක්‍රිය කරන්න',\n      spiritualMessage: '\"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"',\n      todaysHoroscope: 'අද දිනයේ රාශිඵල'\n    },\n    english: {\n      backToHome: '← Back to Home',\n      horoscopeDate: '📅 Horoscope Date',\n      refreshHoroscope: '🔄 New Horoscope',\n      loading: 'Preparing horoscope... Please wait.',\n      error: 'An error occurred while fetching the horoscope. Please try again.',\n      lastUpdated: 'Last updated',\n      refreshing: 'Getting new horoscope...',\n      zodiacSign: 'Sign',\n      soundOn: '🔊 Mute Sound',\n      soundOff: '🔇 Enable Sound',\n      spiritualMessage: '\"May Lord Kubera\\'s blessings be with you\"',\n      todaysHoroscope: 'Today\\'s Horoscope'\n    },\n    tamil: {\n      backToHome: '← முகப்புக்கு திரும்பு',\n      horoscopeDate: '📅 ராசிபலன் தேதி',\n      refreshHoroscope: '🔄 புதிய ராசிபலன்',\n      loading: 'ராசிபலன் தயாராகிறது... தயவுசெய்து காத்திருக்கவும்.',\n      error: 'ராசிபலன் பெறுவதில் பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.',\n      lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',\n      refreshing: 'புதிய ராசிபலன் பெறுகிறது...',\n      zodiacSign: 'ராசி',\n      soundOn: '🔊 ஒலியை அமைதிப்படுத்து',\n      soundOff: '🔇 ஒலியை இயக்கு',\n      spiritualMessage: '\"குபேர பகவானின் ஆசீர்வாதம் உங்களுடன் இருக்கட்டும்\"',\n      todaysHoroscope: 'இன்றைய ராசிபலன்'\n    }\n  };\n  const getUIText = key => {\n    var _uiTexts$currentLangu;\n    return ((_uiTexts$currentLangu = uiTexts[currentLanguage]) === null || _uiTexts$currentLangu === void 0 ? void 0 : _uiTexts$currentLangu[key]) || uiTexts.sinhala[key] || key;\n  };\n  return {\n    getUIText\n  };\n};\n_s2(useUITranslations, \"RoW3ZcoO7JwDe4ziohQaXvSYaSs=\", false, function () {\n  return [useTranslatedContent];\n});\nexport default useTranslatedContent;", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useTranslation", "useTranslatedContent", "_s", "currentLanguage", "translateText", "translateMultiple", "isTranslating", "translatedTexts", "setTranslatedTexts", "isLoading", "setIsLoading", "getTranslatedText", "originalText", "context", "cache<PERSON>ey", "translated", "prev", "error", "console", "getTranslatedCategories", "categories", "length", "translatedCategories", "Promise", "all", "map", "category", "<PERSON><PERSON><PERSON>", "title", "contentKey", "content", "cachedTitle", "cachedContent", "translatedTitle", "<PERSON><PERSON><PERSON><PERSON>", "useUITranslations", "_s2", "uiTexts", "sinhala", "backToHome", "horoscopeDate", "refreshHoroscope", "loading", "lastUpdated", "refreshing", "zodiacSign", "soundOn", "soundOff", "spiritualMessage", "todaysHoroscope", "english", "tamil", "getUIText", "key", "_uiTexts$currentLangu"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/hooks/useTranslatedContent.js"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\n// Custom hook for managing translated content\nexport const useTranslatedContent = () => {\n  const { currentLanguage, translateText, translateMultiple, isTranslating } = useTranslation();\n  const [translatedTexts, setTranslatedTexts] = useState({});\n  const [isLoading, setIsLoading] = useState(false);\n\n  // Translate a single text\n  const getTranslatedText = useCallback(async (originalText, context = '') => {\n    if (!originalText || currentLanguage === 'sinhala') {\n      return originalText;\n    }\n\n    const cacheKey = `${originalText}_${currentLanguage}_${context}`;\n    \n    // Return cached translation if available\n    if (translatedTexts[cacheKey]) {\n      return translatedTexts[cacheKey];\n    }\n\n    try {\n      setIsLoading(true);\n      const translated = await translateText(originalText, currentLanguage, context);\n      \n      // Cache the translation\n      setTranslatedTexts(prev => ({\n        ...prev,\n        [cacheKey]: translated\n      }));\n\n      return translated;\n    } catch (error) {\n      console.error('Translation error:', error);\n      return originalText; // Fallback to original text\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentLanguage, translateText, translatedTexts]);\n\n  // Translate horoscope categories\n  const getTranslatedCategories = useCallback(async (categories) => {\n    if (!categories || categories.length === 0 || currentLanguage === 'sinhala') {\n      return categories;\n    }\n\n    try {\n      setIsLoading(true);\n      const translatedCategories = await Promise.all(\n        categories.map(async (category) => {\n          const titleKey = `${category.title}_${currentLanguage}_category_title`;\n          const contentKey = `${category.content}_${currentLanguage}_horoscope_content`;\n\n          // Check cache first\n          const cachedTitle = translatedTexts[titleKey];\n          const cachedContent = translatedTexts[contentKey];\n\n          let translatedTitle = cachedTitle;\n          let translatedContent = cachedContent;\n\n          // Translate title if not cached\n          if (!cachedTitle) {\n            translatedTitle = await translateText(category.title, currentLanguage, 'category_title');\n            setTranslatedTexts(prev => ({\n              ...prev,\n              [titleKey]: translatedTitle\n            }));\n          }\n\n          // Translate content if not cached\n          if (!cachedContent) {\n            translatedContent = await translateText(category.content, currentLanguage, 'horoscope_content');\n            setTranslatedTexts(prev => ({\n              ...prev,\n              [contentKey]: translatedContent\n            }));\n          }\n\n          return {\n            ...category,\n            title: translatedTitle,\n            content: translatedContent\n          };\n        })\n      );\n\n      return translatedCategories;\n    } catch (error) {\n      console.error('Categories translation error:', error);\n      return categories; // Return original categories on error\n    } finally {\n      setIsLoading(false);\n    }\n  }, [currentLanguage, translateText, translatedTexts]);\n\n  // Clear translations when language changes\n  useEffect(() => {\n    setTranslatedTexts({});\n  }, [currentLanguage]);\n\n  return {\n    getTranslatedText,\n    getTranslatedCategories,\n    isLoading: isLoading || isTranslating,\n    currentLanguage\n  };\n};\n\n// Hook for UI text translations\nexport const useUITranslations = () => {\n  const { getTranslatedText, currentLanguage } = useTranslatedContent();\n\n  const uiTexts = {\n    sinhala: {\n      backToHome: '← මුල් පිටුවට',\n      horoscopeDate: '📅 රාශිඵල දිනය',\n      refreshHoroscope: '🔄 නව රාශිඵලයක්',\n      loading: 'රාශිඵල සකස් කරමින්... කරුණාකර රැඳී සිටින්න.',\n      error: 'රාශිඵල ලබා ගැනීමේදී දෝෂයක් සිදු විය. කරුණාකර නැවත උත්සාහ කරන්න.',\n      lastUpdated: 'අවසන් වරට යාවත්කාලීන කළේ',\n      refreshing: 'නව රාශිඵලයක් ලබා ගනිමින්...',\n      zodiacSign: 'රාශිය',\n      soundOn: '🔊 ශබ්දය නිශ්ශබ්ද කරන්න',\n      soundOff: '🔇 ශබ්දය සක්‍රිය කරන්න',\n      spiritualMessage: '\"කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා\"',\n      todaysHoroscope: 'අද දිනයේ රාශිඵල'\n    },\n    english: {\n      backToHome: '← Back to Home',\n      horoscopeDate: '📅 Horoscope Date',\n      refreshHoroscope: '🔄 New Horoscope',\n      loading: 'Preparing horoscope... Please wait.',\n      error: 'An error occurred while fetching the horoscope. Please try again.',\n      lastUpdated: 'Last updated',\n      refreshing: 'Getting new horoscope...',\n      zodiacSign: 'Sign',\n      soundOn: '🔊 Mute Sound',\n      soundOff: '🔇 Enable Sound',\n      spiritualMessage: '\"May Lord Kubera\\'s blessings be with you\"',\n      todaysHoroscope: 'Today\\'s Horoscope'\n    },\n    tamil: {\n      backToHome: '← முகப்புக்கு திரும்பு',\n      horoscopeDate: '📅 ராசிபலன் தேதி',\n      refreshHoroscope: '🔄 புதிய ராசிபலன்',\n      loading: 'ராசிபலன் தயாராகிறது... தயவுசெய்து காத்திருக்கவும்.',\n      error: 'ராசிபலன் பெறுவதில் பிழை ஏற்பட்டது. தயவுசெய்து மீண்டும் முயற்சிக்கவும்.',\n      lastUpdated: 'கடைசியாக புதுப்பிக்கப்பட்டது',\n      refreshing: 'புதிய ராசிபலன் பெறுகிறது...',\n      zodiacSign: 'ராசி',\n      soundOn: '🔊 ஒலியை அமைதிப்படுத்து',\n      soundOff: '🔇 ஒலியை இயக்கு',\n      spiritualMessage: '\"குபேர பகவானின் ஆசீர்வாதம் உங்களுடன் இருக்கட்டும்\"',\n      todaysHoroscope: 'இன்றைய ராசிபலன்'\n    }\n  };\n\n  const getUIText = (key) => {\n    return uiTexts[currentLanguage]?.[key] || uiTexts.sinhala[key] || key;\n  };\n\n  return { getUIText };\n};\n\nexport default useTranslatedContent;\n"], "mappings": ";;AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,QAAQ,OAAO;AACxD,SAASC,cAAc,QAAQ,gCAAgC;;AAE/D;AACA,OAAO,MAAMC,oBAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACxC,MAAM;IAAEC,eAAe;IAAEC,aAAa;IAAEC,iBAAiB;IAAEC;EAAc,CAAC,GAAGN,cAAc,CAAC,CAAC;EAC7F,MAAM,CAACO,eAAe,EAAEC,kBAAkB,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1D,MAAM,CAACY,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;;EAEjD;EACA,MAAMc,iBAAiB,GAAGZ,WAAW,CAAC,OAAOa,YAAY,EAAEC,OAAO,GAAG,EAAE,KAAK;IAC1E,IAAI,CAACD,YAAY,IAAIT,eAAe,KAAK,SAAS,EAAE;MAClD,OAAOS,YAAY;IACrB;IAEA,MAAME,QAAQ,GAAG,GAAGF,YAAY,IAAIT,eAAe,IAAIU,OAAO,EAAE;;IAEhE;IACA,IAAIN,eAAe,CAACO,QAAQ,CAAC,EAAE;MAC7B,OAAOP,eAAe,CAACO,QAAQ,CAAC;IAClC;IAEA,IAAI;MACFJ,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMK,UAAU,GAAG,MAAMX,aAAa,CAACQ,YAAY,EAAET,eAAe,EAAEU,OAAO,CAAC;;MAE9E;MACAL,kBAAkB,CAACQ,IAAI,KAAK;QAC1B,GAAGA,IAAI;QACP,CAACF,QAAQ,GAAGC;MACd,CAAC,CAAC,CAAC;MAEH,OAAOA,UAAU;IACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C,OAAOL,YAAY,CAAC,CAAC;IACvB,CAAC,SAAS;MACRF,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,eAAe,EAAEC,aAAa,EAAEG,eAAe,CAAC,CAAC;;EAErD;EACA,MAAMY,uBAAuB,GAAGpB,WAAW,CAAC,MAAOqB,UAAU,IAAK;IAChE,IAAI,CAACA,UAAU,IAAIA,UAAU,CAACC,MAAM,KAAK,CAAC,IAAIlB,eAAe,KAAK,SAAS,EAAE;MAC3E,OAAOiB,UAAU;IACnB;IAEA,IAAI;MACFV,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMY,oBAAoB,GAAG,MAAMC,OAAO,CAACC,GAAG,CAC5CJ,UAAU,CAACK,GAAG,CAAC,MAAOC,QAAQ,IAAK;QACjC,MAAMC,QAAQ,GAAG,GAAGD,QAAQ,CAACE,KAAK,IAAIzB,eAAe,iBAAiB;QACtE,MAAM0B,UAAU,GAAG,GAAGH,QAAQ,CAACI,OAAO,IAAI3B,eAAe,oBAAoB;;QAE7E;QACA,MAAM4B,WAAW,GAAGxB,eAAe,CAACoB,QAAQ,CAAC;QAC7C,MAAMK,aAAa,GAAGzB,eAAe,CAACsB,UAAU,CAAC;QAEjD,IAAII,eAAe,GAAGF,WAAW;QACjC,IAAIG,iBAAiB,GAAGF,aAAa;;QAErC;QACA,IAAI,CAACD,WAAW,EAAE;UAChBE,eAAe,GAAG,MAAM7B,aAAa,CAACsB,QAAQ,CAACE,KAAK,EAAEzB,eAAe,EAAE,gBAAgB,CAAC;UACxFK,kBAAkB,CAACQ,IAAI,KAAK;YAC1B,GAAGA,IAAI;YACP,CAACW,QAAQ,GAAGM;UACd,CAAC,CAAC,CAAC;QACL;;QAEA;QACA,IAAI,CAACD,aAAa,EAAE;UAClBE,iBAAiB,GAAG,MAAM9B,aAAa,CAACsB,QAAQ,CAACI,OAAO,EAAE3B,eAAe,EAAE,mBAAmB,CAAC;UAC/FK,kBAAkB,CAACQ,IAAI,KAAK;YAC1B,GAAGA,IAAI;YACP,CAACa,UAAU,GAAGK;UAChB,CAAC,CAAC,CAAC;QACL;QAEA,OAAO;UACL,GAAGR,QAAQ;UACXE,KAAK,EAAEK,eAAe;UACtBH,OAAO,EAAEI;QACX,CAAC;MACH,CAAC,CACH,CAAC;MAED,OAAOZ,oBAAoB;IAC7B,CAAC,CAAC,OAAOL,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAOG,UAAU,CAAC,CAAC;IACrB,CAAC,SAAS;MACRV,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC,EAAE,CAACP,eAAe,EAAEC,aAAa,EAAEG,eAAe,CAAC,CAAC;;EAErD;EACAT,SAAS,CAAC,MAAM;IACdU,kBAAkB,CAAC,CAAC,CAAC,CAAC;EACxB,CAAC,EAAE,CAACL,eAAe,CAAC,CAAC;EAErB,OAAO;IACLQ,iBAAiB;IACjBQ,uBAAuB;IACvBV,SAAS,EAAEA,SAAS,IAAIH,aAAa;IACrCH;EACF,CAAC;AACH,CAAC;;AAED;AAAAD,EAAA,CAzGaD,oBAAoB;EAAA,QAC8CD,cAAc;AAAA;AAyG7F,OAAO,MAAMmC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACrC,MAAM;IAAEzB,iBAAiB;IAAER;EAAgB,CAAC,GAAGF,oBAAoB,CAAC,CAAC;EAErE,MAAMoC,OAAO,GAAG;IACdC,OAAO,EAAE;MACPC,UAAU,EAAE,eAAe;MAC3BC,aAAa,EAAE,gBAAgB;MAC/BC,gBAAgB,EAAE,iBAAiB;MACnCC,OAAO,EAAE,6CAA6C;MACtDzB,KAAK,EAAE,iEAAiE;MACxE0B,WAAW,EAAE,0BAA0B;MACvCC,UAAU,EAAE,6BAA6B;MACzCC,UAAU,EAAE,OAAO;MACnBC,OAAO,EAAE,yBAAyB;MAClCC,QAAQ,EAAE,wBAAwB;MAClCC,gBAAgB,EAAE,yCAAyC;MAC3DC,eAAe,EAAE;IACnB,CAAC;IACDC,OAAO,EAAE;MACPX,UAAU,EAAE,gBAAgB;MAC5BC,aAAa,EAAE,mBAAmB;MAClCC,gBAAgB,EAAE,kBAAkB;MACpCC,OAAO,EAAE,qCAAqC;MAC9CzB,KAAK,EAAE,mEAAmE;MAC1E0B,WAAW,EAAE,cAAc;MAC3BC,UAAU,EAAE,0BAA0B;MACtCC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,eAAe;MACxBC,QAAQ,EAAE,iBAAiB;MAC3BC,gBAAgB,EAAE,4CAA4C;MAC9DC,eAAe,EAAE;IACnB,CAAC;IACDE,KAAK,EAAE;MACLZ,UAAU,EAAE,wBAAwB;MACpCC,aAAa,EAAE,kBAAkB;MACjCC,gBAAgB,EAAE,mBAAmB;MACrCC,OAAO,EAAE,oDAAoD;MAC7DzB,KAAK,EAAE,wEAAwE;MAC/E0B,WAAW,EAAE,8BAA8B;MAC3CC,UAAU,EAAE,6BAA6B;MACzCC,UAAU,EAAE,MAAM;MAClBC,OAAO,EAAE,yBAAyB;MAClCC,QAAQ,EAAE,iBAAiB;MAC3BC,gBAAgB,EAAE,oDAAoD;MACtEC,eAAe,EAAE;IACnB;EACF,CAAC;EAED,MAAMG,SAAS,GAAIC,GAAG,IAAK;IAAA,IAAAC,qBAAA;IACzB,OAAO,EAAAA,qBAAA,GAAAjB,OAAO,CAAClC,eAAe,CAAC,cAAAmD,qBAAA,uBAAxBA,qBAAA,CAA2BD,GAAG,CAAC,KAAIhB,OAAO,CAACC,OAAO,CAACe,GAAG,CAAC,IAAIA,GAAG;EACvE,CAAC;EAED,OAAO;IAAED;EAAU,CAAC;AACtB,CAAC;AAAChB,GAAA,CArDWD,iBAAiB;EAAA,QACmBlC,oBAAoB;AAAA;AAsDrE,eAAeA,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}