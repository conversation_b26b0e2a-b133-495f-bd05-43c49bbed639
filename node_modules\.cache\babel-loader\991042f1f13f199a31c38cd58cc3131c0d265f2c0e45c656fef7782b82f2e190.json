{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\TranslationLoader.js\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationLoader = ({\n  children,\n  showOverlay = false,\n  overlayMessage = 'Translating content...',\n  className = '',\n  style = {}\n}) => {\n  _s();\n  const {\n    isTranslating,\n    currentLanguage\n  } = useTranslation();\n  const getLoadingMessage = () => {\n    switch (currentLanguage) {\n      case 'english':\n        return 'Translating content...';\n      case 'tamil':\n        return 'உள்ளடக்கத்தை மொழிபெயர்க்கிறது...';\n      case 'sinhala':\n      default:\n        return 'අන්තර්ගතය පරිවර්තනය කරමින්...';\n    }\n  };\n  if (showOverlay && isTranslating) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `translation-loader-container ${className}`,\n      style: {\n        position: 'relative',\n        ...style\n      },\n      children: [children, /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'absolute',\n          top: 0,\n          left: 0,\n          right: 0,\n          bottom: 0,\n          background: 'rgba(20, 25, 40, 0.8)',\n          backdropFilter: 'blur(4px)',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n          zIndex: 999,\n          borderRadius: '15px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '12px',\n            background: 'rgba(244, 208, 63, 0.1)',\n            border: '1px solid rgba(244, 208, 63, 0.3)',\n            borderRadius: '12px',\n            padding: '16px 24px',\n            color: '#f4d03f',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            fontSize: '1rem',\n            fontWeight: '500'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              width: '20px',\n              height: '20px',\n              border: '2px solid rgba(244, 208, 63, 0.3)',\n              borderTop: '2px solid #f4d03f',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: overlayMessage || getLoadingMessage()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n        jsx: true,\n        children: `\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n        `\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 83,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `translation-loader-container ${className}`,\n    style: style,\n    children: [children, isTranslating && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        position: 'fixed',\n        top: '70px',\n        right: '20px',\n        background: 'rgba(244, 208, 63, 0.1)',\n        border: '1px solid rgba(244, 208, 63, 0.3)',\n        borderRadius: '8px',\n        padding: '8px 12px',\n        color: '#f4d03f',\n        fontSize: '0.85rem',\n        fontFamily: 'Noto Sans Sinhala, sans-serif',\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px',\n        zIndex: 1000,\n        backdropFilter: 'blur(10px)',\n        boxShadow: '0 4px 15px rgba(244, 208, 63, 0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          width: '12px',\n          height: '12px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          borderTop: '1px solid #f4d03f',\n          borderRadius: '50%',\n          animation: 'spin 1s linear infinite'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n        children: getLoadingMessage()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 129,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 99,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"style\", {\n      jsx: true,\n      children: `\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 94,\n    columnNumber: 5\n  }, this);\n};\n_s(TranslationLoader, \"0StapqP1xn4qMDSNH7QiCN2rtM0=\", false, function () {\n  return [useTranslation];\n});\n_c = TranslationLoader;\nexport default TranslationLoader;\nvar _c;\n$RefreshReg$(_c, \"TranslationLoader\");", "map": {"version": 3, "names": ["React", "useTranslation", "jsxDEV", "_jsxDEV", "Translation<PERSON><PERSON>der", "children", "showOverlay", "overlayMessage", "className", "style", "_s", "isTranslating", "currentLanguage", "getLoadingMessage", "position", "top", "left", "right", "bottom", "background", "<PERSON><PERSON>ilter", "display", "alignItems", "justifyContent", "zIndex", "borderRadius", "gap", "border", "padding", "color", "fontFamily", "fontSize", "fontWeight", "width", "height", "borderTop", "animation", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "jsx", "boxShadow", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/TranslationLoader.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\nconst TranslationLoader = ({ \n  children, \n  showOverlay = false, \n  overlayMessage = 'Translating content...',\n  className = '',\n  style = {} \n}) => {\n  const { isTranslating, currentLanguage } = useTranslation();\n\n  const getLoadingMessage = () => {\n    switch (currentLanguage) {\n      case 'english':\n        return 'Translating content...';\n      case 'tamil':\n        return 'உள்ளடக்கத்தை மொழிபெயர்க்கிறது...';\n      case 'sinhala':\n      default:\n        return 'අන්තර්ගතය පරිවර්තනය කරමින්...';\n    }\n  };\n\n  if (showOverlay && isTranslating) {\n    return (\n      <div \n        className={`translation-loader-container ${className}`}\n        style={{\n          position: 'relative',\n          ...style\n        }}\n      >\n        {children}\n        \n        {/* Translation Overlay */}\n        <div\n          style={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(20, 25, 40, 0.8)',\n            backdropFilter: 'blur(4px)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 999,\n            borderRadius: '15px'\n          }}\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px',\n              background: 'rgba(244, 208, 63, 0.1)',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderRadius: '12px',\n              padding: '16px 24px',\n              color: '#f4d03f',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              fontSize: '1rem',\n              fontWeight: '500'\n            }}\n          >\n            <div\n              style={{\n                width: '20px',\n                height: '20px',\n                border: '2px solid rgba(244, 208, 63, 0.3)',\n                borderTop: '2px solid #f4d03f',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}\n            />\n            <span>{overlayMessage || getLoadingMessage()}</span>\n          </div>\n        </div>\n\n        {/* CSS Animation */}\n        <style jsx>{`\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n        `}</style>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`translation-loader-container ${className}`} style={style}>\n      {children}\n      \n      {/* Subtle loading indicator */}\n      {isTranslating && (\n        <div\n          style={{\n            position: 'fixed',\n            top: '70px',\n            right: '20px',\n            background: 'rgba(244, 208, 63, 0.1)',\n            border: '1px solid rgba(244, 208, 63, 0.3)',\n            borderRadius: '8px',\n            padding: '8px 12px',\n            color: '#f4d03f',\n            fontSize: '0.85rem',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            zIndex: 1000,\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 15px rgba(244, 208, 63, 0.1)'\n          }}\n        >\n          <div\n            style={{\n              width: '12px',\n              height: '12px',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderTop: '1px solid #f4d03f',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }}\n          />\n          <span>{getLoadingMessage()}</span>\n        </div>\n      )}\n\n      {/* CSS Animation */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default TranslationLoader;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,QAAQ;EACRC,WAAW,GAAG,KAAK;EACnBC,cAAc,GAAG,wBAAwB;EACzCC,SAAS,GAAG,EAAE;EACdC,KAAK,GAAG,CAAC;AACX,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IAAEC,aAAa;IAAEC;EAAgB,CAAC,GAAGX,cAAc,CAAC,CAAC;EAE3D,MAAMY,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,QAAQD,eAAe;MACrB,KAAK,SAAS;QACZ,OAAO,wBAAwB;MACjC,KAAK,OAAO;QACV,OAAO,kCAAkC;MAC3C,KAAK,SAAS;MACd;QACE,OAAO,+BAA+B;IAC1C;EACF,CAAC;EAED,IAAIN,WAAW,IAAIK,aAAa,EAAE;IAChC,oBACER,OAAA;MACEK,SAAS,EAAE,gCAAgCA,SAAS,EAAG;MACvDC,KAAK,EAAE;QACLK,QAAQ,EAAE,UAAU;QACpB,GAAGL;MACL,CAAE;MAAAJ,QAAA,GAEDA,QAAQ,eAGTF,OAAA;QACEM,KAAK,EAAE;UACLK,QAAQ,EAAE,UAAU;UACpBC,GAAG,EAAE,CAAC;UACNC,IAAI,EAAE,CAAC;UACPC,KAAK,EAAE,CAAC;UACRC,MAAM,EAAE,CAAC;UACTC,UAAU,EAAE,uBAAuB;UACnCC,cAAc,EAAE,WAAW;UAC3BC,OAAO,EAAE,MAAM;UACfC,UAAU,EAAE,QAAQ;UACpBC,cAAc,EAAE,QAAQ;UACxBC,MAAM,EAAE,GAAG;UACXC,YAAY,EAAE;QAChB,CAAE;QAAApB,QAAA,eAEFF,OAAA;UACEM,KAAK,EAAE;YACLY,OAAO,EAAE,MAAM;YACfC,UAAU,EAAE,QAAQ;YACpBI,GAAG,EAAE,MAAM;YACXP,UAAU,EAAE,yBAAyB;YACrCQ,MAAM,EAAE,mCAAmC;YAC3CF,YAAY,EAAE,MAAM;YACpBG,OAAO,EAAE,WAAW;YACpBC,KAAK,EAAE,SAAS;YAChBC,UAAU,EAAE,+BAA+B;YAC3CC,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE;UACd,CAAE;UAAA3B,QAAA,gBAEFF,OAAA;YACEM,KAAK,EAAE;cACLwB,KAAK,EAAE,MAAM;cACbC,MAAM,EAAE,MAAM;cACdP,MAAM,EAAE,mCAAmC;cAC3CQ,SAAS,EAAE,mBAAmB;cAC9BV,YAAY,EAAE,KAAK;cACnBW,SAAS,EAAE;YACb;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFrC,OAAA;YAAAE,QAAA,EAAOE,cAAc,IAAIM,iBAAiB,CAAC;UAAC;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNrC,OAAA;QAAOsC,GAAG;QAAApC,QAAA,EAAE;AACpB;AACA;AACA;AACA;AACA;MAAS;QAAAgC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAEV;EAEA,oBACErC,OAAA;IAAKK,SAAS,EAAE,gCAAgCA,SAAS,EAAG;IAACC,KAAK,EAAEA,KAAM;IAAAJ,QAAA,GACvEA,QAAQ,EAGRM,aAAa,iBACZR,OAAA;MACEM,KAAK,EAAE;QACLK,QAAQ,EAAE,OAAO;QACjBC,GAAG,EAAE,MAAM;QACXE,KAAK,EAAE,MAAM;QACbE,UAAU,EAAE,yBAAyB;QACrCQ,MAAM,EAAE,mCAAmC;QAC3CF,YAAY,EAAE,KAAK;QACnBG,OAAO,EAAE,UAAU;QACnBC,KAAK,EAAE,SAAS;QAChBE,QAAQ,EAAE,SAAS;QACnBD,UAAU,EAAE,+BAA+B;QAC3CT,OAAO,EAAE,MAAM;QACfC,UAAU,EAAE,QAAQ;QACpBI,GAAG,EAAE,KAAK;QACVF,MAAM,EAAE,IAAI;QACZJ,cAAc,EAAE,YAAY;QAC5BsB,SAAS,EAAE;MACb,CAAE;MAAArC,QAAA,gBAEFF,OAAA;QACEM,KAAK,EAAE;UACLwB,KAAK,EAAE,MAAM;UACbC,MAAM,EAAE,MAAM;UACdP,MAAM,EAAE,mCAAmC;UAC3CQ,SAAS,EAAE,mBAAmB;UAC9BV,YAAY,EAAE,KAAK;UACnBW,SAAS,EAAE;QACb;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eACFrC,OAAA;QAAAE,QAAA,EAAOQ,iBAAiB,CAAC;MAAC;QAAAwB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/B,CACN,eAGDrC,OAAA;MAAOsC,GAAG;MAAApC,QAAA,EAAE;AAClB;AACA;AACA;AACA;AACA;IAAO;MAAAgC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV,CAAC;AAAC9B,EAAA,CA1IIN,iBAAiB;EAAA,QAOsBH,cAAc;AAAA;AAAA0C,EAAA,GAPrDvC,iBAAiB;AA4IvB,eAAeA,iBAAiB;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}