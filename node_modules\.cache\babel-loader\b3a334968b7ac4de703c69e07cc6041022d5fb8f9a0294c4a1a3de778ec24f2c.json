{"ast": null, "code": "import _objectSpread from\"/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{createContext,useContext,useState,useCallback}from'react';import TranslationService from'../services/TranslationService';import{jsx as _jsx}from\"react/jsx-runtime\";const TranslationContext=/*#__PURE__*/createContext();export const useTranslation=()=>{const context=useContext(TranslationContext);if(!context){throw new Error('useTranslation must be used within a TranslationProvider');}return context;};export const TranslationProvider=_ref=>{let{children}=_ref;const[currentLanguage,setCurrentLanguage]=useState('sinhala');const[translationCache,setTranslationCache]=useState({});const[isTranslating,setIsTranslating]=useState(false);const languages={sinhala:{code:'si',name:'සිංහල',flag:'🇱🇰',nativeName:'සිංහල'},english:{code:'en',name:'English',flag:'🇺🇸',nativeName:'English'},tamil:{code:'ta',name:'தமிழ்',flag:'🇱🇰',nativeName:'தமிழ்'}};// Generate cache key for translation\nconst getCacheKey=function(text,targetLang){let sourceContext=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'';return\"\".concat(text,\"_\").concat(targetLang,\"_\").concat(sourceContext).replace(/\\s+/g,'_').toLowerCase();};// Translate text using Gemini API\nconst translateText=useCallback(async function(text,targetLanguage){let sourceContext=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'';// If target language is Sinhala (default), return original text\nif(targetLanguage==='sinhala'||!text||text.trim()===''){return text;}const cacheKey=getCacheKey(text,targetLanguage,sourceContext);// Check cache first\nif(translationCache[cacheKey]){return translationCache[cacheKey];}try{setIsTranslating(true);const translatedText=await TranslationService.translateText(text,targetLanguage,sourceContext);// Cache the translation\nsetTranslationCache(prev=>_objectSpread(_objectSpread({},prev),{},{[cacheKey]:translatedText}));return translatedText;}catch(error){console.error('Translation error:',error);// Return original text if translation fails\nreturn text;}finally{setIsTranslating(false);}},[translationCache]);// Translate multiple texts at once\nconst translateMultiple=useCallback(async function(textArray,targetLanguage){let sourceContext=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'';if(targetLanguage==='sinhala'){return textArray;}const translations=await Promise.all(textArray.map(text=>translateText(text,targetLanguage,sourceContext)));return translations;},[translateText]);// Change language\nconst changeLanguage=useCallback(languageKey=>{if(languages[languageKey]){setCurrentLanguage(languageKey);}},[]);// Get translated text with fallback\nconst t=useCallback(async function(text){let context=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';if(currentLanguage==='sinhala'){return text;}return await translateText(text,currentLanguage,context);},[currentLanguage,translateText]);const value={currentLanguage,languages,changeLanguage,translateText,translateMultiple,t,isTranslating,translationCache};return/*#__PURE__*/_jsx(TranslationContext.Provider,{value:value,children:children});};export default TranslationContext;", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "TranslationService", "jsx", "_jsx", "TranslationContext", "useTranslation", "context", "Error", "TranslationProvider", "_ref", "children", "currentLanguage", "setCurrentLanguage", "translationCache", "setTranslationCache", "isTranslating", "setIsTranslating", "languages", "sinhala", "code", "name", "flag", "nativeName", "english", "tamil", "get<PERSON><PERSON><PERSON><PERSON>", "text", "targetLang", "sourceContext", "arguments", "length", "undefined", "concat", "replace", "toLowerCase", "translateText", "targetLanguage", "trim", "cache<PERSON>ey", "translatedText", "prev", "_objectSpread", "error", "console", "translateMultiple", "textArray", "translations", "Promise", "all", "map", "changeLanguage", "languageKey", "t", "value", "Provider"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/contexts/TranslationContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback } from 'react';\nimport TranslationService from '../services/TranslationService';\n\nconst TranslationContext = createContext();\n\nexport const useTranslation = () => {\n  const context = useContext(TranslationContext);\n  if (!context) {\n    throw new Error('useTranslation must be used within a TranslationProvider');\n  }\n  return context;\n};\n\nexport const TranslationProvider = ({ children }) => {\n  const [currentLanguage, setCurrentLanguage] = useState('sinhala');\n  const [translationCache, setTranslationCache] = useState({});\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  const languages = {\n    sinhala: {\n      code: 'si',\n      name: 'සිංහල',\n      flag: '🇱🇰',\n      nativeName: 'සිංහල'\n    },\n    english: {\n      code: 'en',\n      name: 'English',\n      flag: '🇺🇸',\n      nativeName: 'English'\n    },\n    tamil: {\n      code: 'ta',\n      name: 'தமிழ்',\n      flag: '🇱🇰',\n      nativeName: 'தமிழ்'\n    }\n  };\n\n  // Generate cache key for translation\n  const getCacheKey = (text, targetLang, sourceContext = '') => {\n    return `${text}_${targetLang}_${sourceContext}`.replace(/\\s+/g, '_').toLowerCase();\n  };\n\n  // Translate text using Gemini API\n  const translateText = useCallback(async (text, targetLanguage, sourceContext = '') => {\n    // If target language is Sinhala (default), return original text\n    if (targetLanguage === 'sinhala' || !text || text.trim() === '') {\n      return text;\n    }\n\n    const cacheKey = getCacheKey(text, targetLanguage, sourceContext);\n    \n    // Check cache first\n    if (translationCache[cacheKey]) {\n      return translationCache[cacheKey];\n    }\n\n    try {\n      setIsTranslating(true);\n      const translatedText = await TranslationService.translateText(text, targetLanguage, sourceContext);\n      \n      // Cache the translation\n      setTranslationCache(prev => ({\n        ...prev,\n        [cacheKey]: translatedText\n      }));\n\n      return translatedText;\n    } catch (error) {\n      console.error('Translation error:', error);\n      // Return original text if translation fails\n      return text;\n    } finally {\n      setIsTranslating(false);\n    }\n  }, [translationCache]);\n\n  // Translate multiple texts at once\n  const translateMultiple = useCallback(async (textArray, targetLanguage, sourceContext = '') => {\n    if (targetLanguage === 'sinhala') {\n      return textArray;\n    }\n\n    const translations = await Promise.all(\n      textArray.map(text => translateText(text, targetLanguage, sourceContext))\n    );\n\n    return translations;\n  }, [translateText]);\n\n  // Change language\n  const changeLanguage = useCallback((languageKey) => {\n    if (languages[languageKey]) {\n      setCurrentLanguage(languageKey);\n    }\n  }, []);\n\n  // Get translated text with fallback\n  const t = useCallback(async (text, context = '') => {\n    if (currentLanguage === 'sinhala') {\n      return text;\n    }\n    return await translateText(text, currentLanguage, context);\n  }, [currentLanguage, translateText]);\n\n  const value = {\n    currentLanguage,\n    languages,\n    changeLanguage,\n    translateText,\n    translateMultiple,\n    t,\n    isTranslating,\n    translationCache\n  };\n\n  return (\n    <TranslationContext.Provider value={value}>\n      {children}\n    </TranslationContext.Provider>\n  );\n};\n\nexport default TranslationContext;\n"], "mappings": "yHAAA,MAAO,CAAAA,KAAK,EAAIC,aAAa,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CAC/E,MAAO,CAAAC,kBAAkB,KAAM,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,yBAEhE,KAAM,CAAAC,kBAAkB,cAAGP,aAAa,CAAC,CAAC,CAE1C,MAAO,MAAM,CAAAQ,cAAc,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,OAAO,CAAGR,UAAU,CAACM,kBAAkB,CAAC,CAC9C,GAAI,CAACE,OAAO,CAAE,CACZ,KAAM,IAAI,CAAAC,KAAK,CAAC,0DAA0D,CAAC,CAC7E,CACA,MAAO,CAAAD,OAAO,CAChB,CAAC,CAED,MAAO,MAAM,CAAAE,mBAAmB,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CAC9C,KAAM,CAACE,eAAe,CAAEC,kBAAkB,CAAC,CAAGb,QAAQ,CAAC,SAAS,CAAC,CACjE,KAAM,CAACc,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGf,QAAQ,CAAC,CAAC,CAAC,CAAC,CAC5D,KAAM,CAACgB,aAAa,CAAEC,gBAAgB,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAEzD,KAAM,CAAAkB,SAAS,CAAG,CAChBC,OAAO,CAAE,CACPC,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,MAAM,CACZC,UAAU,CAAE,OACd,CAAC,CACDC,OAAO,CAAE,CACPJ,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,SAAS,CACfC,IAAI,CAAE,MAAM,CACZC,UAAU,CAAE,SACd,CAAC,CACDE,KAAK,CAAE,CACLL,IAAI,CAAE,IAAI,CACVC,IAAI,CAAE,OAAO,CACbC,IAAI,CAAE,MAAM,CACZC,UAAU,CAAE,OACd,CACF,CAAC,CAED;AACA,KAAM,CAAAG,WAAW,CAAG,QAAAA,CAACC,IAAI,CAAEC,UAAU,CAAyB,IAAvB,CAAAC,aAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACvD,MAAO,GAAAG,MAAA,CAAGN,IAAI,MAAAM,MAAA,CAAIL,UAAU,MAAAK,MAAA,CAAIJ,aAAa,EAAGK,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,CACpF,CAAC,CAED;AACA,KAAM,CAAAC,aAAa,CAAGnC,WAAW,CAAC,eAAO0B,IAAI,CAAEU,cAAc,CAAyB,IAAvB,CAAAR,aAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC/E;AACA,GAAIO,cAAc,GAAK,SAAS,EAAI,CAACV,IAAI,EAAIA,IAAI,CAACW,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC/D,MAAO,CAAAX,IAAI,CACb,CAEA,KAAM,CAAAY,QAAQ,CAAGb,WAAW,CAACC,IAAI,CAAEU,cAAc,CAAER,aAAa,CAAC,CAEjE;AACA,GAAIf,gBAAgB,CAACyB,QAAQ,CAAC,CAAE,CAC9B,MAAO,CAAAzB,gBAAgB,CAACyB,QAAQ,CAAC,CACnC,CAEA,GAAI,CACFtB,gBAAgB,CAAC,IAAI,CAAC,CACtB,KAAM,CAAAuB,cAAc,CAAG,KAAM,CAAAtC,kBAAkB,CAACkC,aAAa,CAACT,IAAI,CAAEU,cAAc,CAAER,aAAa,CAAC,CAElG;AACAd,mBAAmB,CAAC0B,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACnBD,IAAI,MACP,CAACF,QAAQ,EAAGC,cAAc,EAC1B,CAAC,CAEH,MAAO,CAAAA,cAAc,CACvB,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,CAAEA,KAAK,CAAC,CAC1C;AACA,MAAO,CAAAhB,IAAI,CACb,CAAC,OAAS,CACRV,gBAAgB,CAAC,KAAK,CAAC,CACzB,CACF,CAAC,CAAE,CAACH,gBAAgB,CAAC,CAAC,CAEtB;AACA,KAAM,CAAA+B,iBAAiB,CAAG5C,WAAW,CAAC,eAAO6C,SAAS,CAAET,cAAc,CAAyB,IAAvB,CAAAR,aAAa,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACxF,GAAIO,cAAc,GAAK,SAAS,CAAE,CAChC,MAAO,CAAAS,SAAS,CAClB,CAEA,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAC,OAAO,CAACC,GAAG,CACpCH,SAAS,CAACI,GAAG,CAACvB,IAAI,EAAIS,aAAa,CAACT,IAAI,CAAEU,cAAc,CAAER,aAAa,CAAC,CAC1E,CAAC,CAED,MAAO,CAAAkB,YAAY,CACrB,CAAC,CAAE,CAACX,aAAa,CAAC,CAAC,CAEnB;AACA,KAAM,CAAAe,cAAc,CAAGlD,WAAW,CAAEmD,WAAW,EAAK,CAClD,GAAIlC,SAAS,CAACkC,WAAW,CAAC,CAAE,CAC1BvC,kBAAkB,CAACuC,WAAW,CAAC,CACjC,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAC,CAAC,CAAGpD,WAAW,CAAC,eAAO0B,IAAI,CAAmB,IAAjB,CAAApB,OAAO,CAAAuB,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC7C,GAAIlB,eAAe,GAAK,SAAS,CAAE,CACjC,MAAO,CAAAe,IAAI,CACb,CACA,MAAO,MAAM,CAAAS,aAAa,CAACT,IAAI,CAAEf,eAAe,CAAEL,OAAO,CAAC,CAC5D,CAAC,CAAE,CAACK,eAAe,CAAEwB,aAAa,CAAC,CAAC,CAEpC,KAAM,CAAAkB,KAAK,CAAG,CACZ1C,eAAe,CACfM,SAAS,CACTiC,cAAc,CACdf,aAAa,CACbS,iBAAiB,CACjBQ,CAAC,CACDrC,aAAa,CACbF,gBACF,CAAC,CAED,mBACEV,IAAA,CAACC,kBAAkB,CAACkD,QAAQ,EAACD,KAAK,CAAEA,KAAM,CAAA3C,QAAA,CACvCA,QAAQ,CACkB,CAAC,CAElC,CAAC,CAED,cAAe,CAAAN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}