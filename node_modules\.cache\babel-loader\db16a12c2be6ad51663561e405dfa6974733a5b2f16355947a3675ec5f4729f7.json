{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\TranslationTest.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationTest = () => {\n  _s();\n  const {\n    currentLanguage,\n    translateText,\n    changeLanguage,\n    isTranslating\n  } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n\n      // Test direct API call first\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API URL:', apiUrl);\n      try {\n        const response = await fetch(`${apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            text: 'ආදරය',\n            targetLanguage: currentLanguage,\n            targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',\n            context: 'test'\n          })\n        });\n        console.log('Direct API response status:', response.status);\n        const data = await response.json();\n        console.log('Direct API response data:', data);\n        if (data.success) {\n          setTestResult(`Direct API: ${data.translatedText}`);\n        } else {\n          setError(`Direct API Error: ${data.error}`);\n        }\n      } catch (apiError) {\n        console.error('Direct API error:', apiError);\n        setError(`Direct API Error: ${apiError.message}`);\n      }\n\n      // Also test through translation context\n      try {\n        const result = await translateText('ආදරය', currentLanguage, 'test');\n        console.log('Context translation result:', result);\n        setTestResult(prev => prev + ` | Context: ${result}`);\n      } catch (contextError) {\n        console.error('Context translation error:', contextError);\n        setError(prev => prev + ` | Context Error: ${contextError.message}`);\n      }\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n      children: \"Translation Test\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 88,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Current Language: \", currentLanguage]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 89,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      children: [\"Is Translating: \", isTranslating ? 'Yes' : 'No']\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '10px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('sinhala'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Sinhala\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('english'),\n        style: {\n          margin: '5px'\n        },\n        children: \"English\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => changeLanguage('tamil'),\n        style: {\n          margin: '5px'\n        },\n        children: \"Tamil\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: testTranslation,\n      disabled: isTranslating,\n      children: \"Test Translation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 104,\n      columnNumber: 7\n    }, this), testResult && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'lightgreen'\n      },\n      children: testResult\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 109,\n      columnNumber: 9\n    }, this), error && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '10px',\n        color: 'red'\n      },\n      children: error\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 77,\n    columnNumber: 5\n  }, this);\n};\n_s(TranslationTest, \"ofjRKXIuHGl7R9hpsA1r9bfRQW4=\", false, function () {\n  return [useTranslation];\n});\n_c = TranslationTest;\nexport default TranslationTest;\nvar _c;\n$RefreshReg$(_c, \"TranslationTest\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useTranslation", "jsxDEV", "_jsxDEV", "TranslationTest", "_s", "currentLanguage", "translateText", "changeLanguage", "isTranslating", "testResult", "setTestResult", "error", "setError", "testTranslation", "console", "log", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "text", "targetLanguage", "targetLanguageName", "context", "status", "data", "json", "success", "translatedText", "apiError", "message", "result", "prev", "contextError", "err", "style", "position", "top", "left", "background", "color", "padding", "borderRadius", "zIndex", "max<PERSON><PERSON><PERSON>", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "onClick", "margin", "disabled", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/TranslationTest.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\nconst TranslationTest = () => {\n  const { currentLanguage, translateText, changeLanguage, isTranslating } = useTranslation();\n  const [testResult, setTestResult] = useState('');\n  const [error, setError] = useState('');\n\n  const testTranslation = async () => {\n    try {\n      setError('');\n      setTestResult('Testing...');\n\n      console.log('Current language:', currentLanguage);\n      console.log('Translation function available:', typeof translateText);\n\n      if (currentLanguage === 'sinhala') {\n        setTestResult('Please select English or Tamil to test translation');\n        return;\n      }\n\n      // Test direct API call first\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n      console.log('API URL:', apiUrl);\n\n      try {\n        const response = await fetch(`${apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            text: 'ආදරය',\n            targetLanguage: currentLanguage,\n            targetLanguageName: currentLanguage === 'english' ? 'English' : 'Tamil',\n            context: 'test'\n          })\n        });\n\n        console.log('Direct API response status:', response.status);\n        const data = await response.json();\n        console.log('Direct API response data:', data);\n\n        if (data.success) {\n          setTestResult(`Direct API: ${data.translatedText}`);\n        } else {\n          setError(`Direct API Error: ${data.error}`);\n        }\n      } catch (apiError) {\n        console.error('Direct API error:', apiError);\n        setError(`Direct API Error: ${apiError.message}`);\n      }\n\n      // Also test through translation context\n      try {\n        const result = await translateText('ආදරය', currentLanguage, 'test');\n        console.log('Context translation result:', result);\n        setTestResult(prev => prev + ` | Context: ${result}`);\n      } catch (contextError) {\n        console.error('Context translation error:', contextError);\n        setError(prev => prev + ` | Context Error: ${contextError.message}`);\n      }\n\n    } catch (err) {\n      console.error('Translation test error:', err);\n      setError(`Error: ${err.message}`);\n    }\n  };\n\n  useEffect(() => {\n    console.log('TranslationTest component mounted');\n    console.log('Current language:', currentLanguage);\n    console.log('Is translating:', isTranslating);\n  }, [currentLanguage, isTranslating]);\n\n  return (\n    <div style={{\n      position: 'fixed',\n      top: '100px',\n      left: '20px',\n      background: 'rgba(0,0,0,0.8)',\n      color: 'white',\n      padding: '20px',\n      borderRadius: '10px',\n      zIndex: 9999,\n      maxWidth: '300px'\n    }}>\n      <h3>Translation Test</h3>\n      <p>Current Language: {currentLanguage}</p>\n      <p>Is Translating: {isTranslating ? 'Yes' : 'No'}</p>\n      \n      <div style={{ marginBottom: '10px' }}>\n        <button onClick={() => changeLanguage('sinhala')} style={{ margin: '5px' }}>\n          Sinhala\n        </button>\n        <button onClick={() => changeLanguage('english')} style={{ margin: '5px' }}>\n          English\n        </button>\n        <button onClick={() => changeLanguage('tamil')} style={{ margin: '5px' }}>\n          Tamil\n        </button>\n      </div>\n      \n      <button onClick={testTranslation} disabled={isTranslating}>\n        Test Translation\n      </button>\n      \n      {testResult && (\n        <div style={{ marginTop: '10px', color: 'lightgreen' }}>\n          {testResult}\n        </div>\n      )}\n      \n      {error && (\n        <div style={{ marginTop: '10px', color: 'red' }}>\n          {error}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default TranslationTest;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,cAAc,QAAQ,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,eAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM;IAAEC,eAAe;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAc,CAAC,GAAGR,cAAc,CAAC,CAAC;EAC1F,MAAM,CAACS,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EAEtC,MAAMe,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACFD,QAAQ,CAAC,EAAE,CAAC;MACZF,aAAa,CAAC,YAAY,CAAC;MAE3BI,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;MACjDS,OAAO,CAACC,GAAG,CAAC,iCAAiC,EAAE,OAAOT,aAAa,CAAC;MAEpE,IAAID,eAAe,KAAK,SAAS,EAAE;QACjCK,aAAa,CAAC,oDAAoD,CAAC;QACnE;MACF;;MAEA;MACA,MAAMM,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;MAC3EL,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEC,MAAM,CAAC;MAE/B,IAAI;QACF,MAAMI,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,YAAY,EAAE;UAClDM,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnBC,IAAI,EAAE,MAAM;YACZC,cAAc,EAAEvB,eAAe;YAC/BwB,kBAAkB,EAAExB,eAAe,KAAK,SAAS,GAAG,SAAS,GAAG,OAAO;YACvEyB,OAAO,EAAE;UACX,CAAC;QACH,CAAC,CAAC;QAEFhB,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEK,QAAQ,CAACW,MAAM,CAAC;QAC3D,MAAMC,IAAI,GAAG,MAAMZ,QAAQ,CAACa,IAAI,CAAC,CAAC;QAClCnB,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAEiB,IAAI,CAAC;QAE9C,IAAIA,IAAI,CAACE,OAAO,EAAE;UAChBxB,aAAa,CAAC,eAAesB,IAAI,CAACG,cAAc,EAAE,CAAC;QACrD,CAAC,MAAM;UACLvB,QAAQ,CAAC,qBAAqBoB,IAAI,CAACrB,KAAK,EAAE,CAAC;QAC7C;MACF,CAAC,CAAC,OAAOyB,QAAQ,EAAE;QACjBtB,OAAO,CAACH,KAAK,CAAC,mBAAmB,EAAEyB,QAAQ,CAAC;QAC5CxB,QAAQ,CAAC,qBAAqBwB,QAAQ,CAACC,OAAO,EAAE,CAAC;MACnD;;MAEA;MACA,IAAI;QACF,MAAMC,MAAM,GAAG,MAAMhC,aAAa,CAAC,MAAM,EAAED,eAAe,EAAE,MAAM,CAAC;QACnES,OAAO,CAACC,GAAG,CAAC,6BAA6B,EAAEuB,MAAM,CAAC;QAClD5B,aAAa,CAAC6B,IAAI,IAAIA,IAAI,GAAG,eAAeD,MAAM,EAAE,CAAC;MACvD,CAAC,CAAC,OAAOE,YAAY,EAAE;QACrB1B,OAAO,CAACH,KAAK,CAAC,4BAA4B,EAAE6B,YAAY,CAAC;QACzD5B,QAAQ,CAAC2B,IAAI,IAAIA,IAAI,GAAG,qBAAqBC,YAAY,CAACH,OAAO,EAAE,CAAC;MACtE;IAEF,CAAC,CAAC,OAAOI,GAAG,EAAE;MACZ3B,OAAO,CAACH,KAAK,CAAC,yBAAyB,EAAE8B,GAAG,CAAC;MAC7C7B,QAAQ,CAAC,UAAU6B,GAAG,CAACJ,OAAO,EAAE,CAAC;IACnC;EACF,CAAC;EAEDtC,SAAS,CAAC,MAAM;IACde,OAAO,CAACC,GAAG,CAAC,mCAAmC,CAAC;IAChDD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEV,eAAe,CAAC;IACjDS,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEP,aAAa,CAAC;EAC/C,CAAC,EAAE,CAACH,eAAe,EAAEG,aAAa,CAAC,CAAC;EAEpC,oBACEN,OAAA;IAAKwC,KAAK,EAAE;MACVC,QAAQ,EAAE,OAAO;MACjBC,GAAG,EAAE,OAAO;MACZC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE,iBAAiB;MAC7BC,KAAK,EAAE,OAAO;MACdC,OAAO,EAAE,MAAM;MACfC,YAAY,EAAE,MAAM;MACpBC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE;IACZ,CAAE;IAAAC,QAAA,gBACAlD,OAAA;MAAAkD,QAAA,EAAI;IAAgB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eACzBtD,OAAA;MAAAkD,QAAA,GAAG,oBAAkB,EAAC/C,eAAe;IAAA;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC1CtD,OAAA;MAAAkD,QAAA,GAAG,kBAAgB,EAAC5C,aAAa,GAAG,KAAK,GAAG,IAAI;IAAA;MAAA6C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAErDtD,OAAA;MAAKwC,KAAK,EAAE;QAAEe,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,gBACnClD,OAAA;QAAQwD,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC,SAAS,CAAE;QAACmC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtD,OAAA;QAAQwD,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC,SAAS,CAAE;QAACmC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE5E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtD,OAAA;QAAQwD,OAAO,EAAEA,CAAA,KAAMnD,cAAc,CAAC,OAAO,CAAE;QAACmC,KAAK,EAAE;UAAEiB,MAAM,EAAE;QAAM,CAAE;QAAAP,QAAA,EAAC;MAE1E;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC,eAENtD,OAAA;MAAQwD,OAAO,EAAE7C,eAAgB;MAAC+C,QAAQ,EAAEpD,aAAc;MAAA4C,QAAA,EAAC;IAE3D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,EAER/C,UAAU,iBACTP,OAAA;MAAKwC,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAa,CAAE;MAAAK,QAAA,EACpD3C;IAAU;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CACN,EAEA7C,KAAK,iBACJT,OAAA;MAAKwC,KAAK,EAAE;QAAEmB,SAAS,EAAE,MAAM;QAAEd,KAAK,EAAE;MAAM,CAAE;MAAAK,QAAA,EAC7CzC;IAAK;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACpD,EAAA,CArHID,eAAe;EAAA,QACuDH,cAAc;AAAA;AAAA8D,EAAA,GADpF3D,eAAe;AAuHrB,eAAeA,eAAe;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}