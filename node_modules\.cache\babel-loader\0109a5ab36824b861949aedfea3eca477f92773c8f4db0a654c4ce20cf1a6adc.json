{"ast": null, "code": "import _objectSpread from\"/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";class TranslationService{constructor(){// Use direct API URL for both development and production\nthis.apiUrl=process.env.REACT_APP_API_URL||'http://localhost:5000/api';this.cache=new Map();this.requestQueue=new Map();console.log('TranslationService initialized with API URL:',this.apiUrl);console.log('Environment:',process.env.NODE_ENV);}// Language mappings for better context\ngetLanguageInfo(languageKey){const languages={sinhala:{name:'Sinhala',nativeName:'සිංහල',code:'si'},english:{name:'English',nativeName:'English',code:'en'},tamil:{name:'Tamil',nativeName:'தமிழ்',code:'ta'}};return languages[languageKey]||languages.english;}// Generate cache key\ngetCacheKey(text,targetLanguage,context){return\"\".concat(text.substring(0,50),\"_\").concat(targetLanguage,\"_\").concat(context).replace(/\\s+/g,'_');}// Translate text using backend Gemini API\nasync translateText(text,targetLanguage){let context=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'';if(!text||text.trim()===''){return text;}// Return original text if target is Sinhala (default language)\nif(targetLanguage==='sinhala'){return text;}const cacheKey=this.getCacheKey(text,targetLanguage,context);// Check cache first\nif(this.cache.has(cacheKey)){return this.cache.get(cacheKey);}// Check if there's already a pending request for this translation\nif(this.requestQueue.has(cacheKey)){return await this.requestQueue.get(cacheKey);}// Create the translation request\nconst translationPromise=this.performTranslation(text,targetLanguage,context);this.requestQueue.set(cacheKey,translationPromise);try{const result=await translationPromise;this.cache.set(cacheKey,result);return result;}finally{this.requestQueue.delete(cacheKey);}}// Perform the actual translation via backend API\nasync performTranslation(text,targetLanguage){let context=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'';const maxRetries=2;let lastError=null;for(let attempt=0;attempt<=maxRetries;attempt++){try{const targetLangInfo=this.getLanguageInfo(targetLanguage);const controller=new AbortController();const timeoutId=setTimeout(()=>controller.abort(),15000);// 15 second timeout\nconst response=await fetch(\"\".concat(this.apiUrl,\"/translate\"),{method:'POST',headers:{'Content-Type':'application/json'},body:JSON.stringify({text:text,targetLanguage:targetLanguage,targetLanguageName:targetLangInfo.name,context:context||'horoscope_content'}),signal:controller.signal});clearTimeout(timeoutId);if(!response.ok){throw new Error(\"Translation API error: \".concat(response.status,\" - \").concat(response.statusText));}const data=await response.json();if(data.success&&data.translatedText){return data.translatedText;}else{throw new Error(data.error||'Translation failed - no translated text received');}}catch(error){lastError=error;console.warn(\"Translation attempt \".concat(attempt+1,\" failed:\"),error.message);// If it's the last attempt or a non-retryable error, break\nif(attempt===maxRetries||error.name==='AbortError'){break;}// Wait before retrying (exponential backoff)\nawait new Promise(resolve=>setTimeout(resolve,Math.pow(2,attempt)*1000));}}console.error('Translation service error after all retries:',lastError);// Fallback: return original text if all translation attempts fail\nreturn text;}// Translate multiple texts efficiently\nasync translateMultiple(textArray,targetLanguage){let context=arguments.length>2&&arguments[2]!==undefined?arguments[2]:'';if(targetLanguage==='sinhala'){return textArray;}try{const translations=await Promise.all(textArray.map(text=>this.translateText(text,targetLanguage,context)));return translations;}catch(error){console.error('Batch translation error:',error);return textArray;// Return original texts if batch translation fails\n}}// Translate horoscope categories\nasync translateHoroscopeCategories(categories,targetLanguage){if(targetLanguage==='sinhala'||!categories||categories.length===0){return categories;}try{const translatedCategories=await Promise.all(categories.map(async category=>{const translatedTitle=await this.translateText(category.title,targetLanguage,'horoscope_category_title');const translatedContent=await this.translateText(category.content,targetLanguage,'horoscope_content');return _objectSpread(_objectSpread({},category),{},{title:translatedTitle,content:translatedContent});}));return translatedCategories;}catch(error){console.error('Horoscope categories translation error:',error);return categories;// Return original categories if translation fails\n}}// Clear cache (useful for memory management)\nclearCache(){this.cache.clear();}// Get cache size for debugging\ngetCacheSize(){return this.cache.size;}}const translationService=new TranslationService();export default translationService;", "map": {"version": 3, "names": ["TranslationService", "constructor", "apiUrl", "process", "env", "REACT_APP_API_URL", "cache", "Map", "requestQueue", "console", "log", "NODE_ENV", "getLanguageInfo", "languageKey", "languages", "sinhala", "name", "nativeName", "code", "english", "tamil", "get<PERSON><PERSON><PERSON><PERSON>", "text", "targetLanguage", "context", "concat", "substring", "replace", "translateText", "arguments", "length", "undefined", "trim", "cache<PERSON>ey", "has", "get", "translationPromise", "performTranslation", "set", "result", "delete", "maxRetries", "lastError", "attempt", "targetLangInfo", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "targetLanguageName", "signal", "clearTimeout", "ok", "Error", "status", "statusText", "data", "json", "success", "translatedText", "error", "warn", "message", "Promise", "resolve", "Math", "pow", "translateMultiple", "textArray", "translations", "all", "map", "translateHoroscopeCategories", "categories", "translatedCategories", "category", "translatedTitle", "title", "<PERSON><PERSON><PERSON><PERSON>", "content", "_objectSpread", "clearCache", "clear", "getCacheSize", "size", "translationService"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/services/TranslationService.js"], "sourcesContent": ["class TranslationService {\n  constructor() {\n    // Use direct API URL for both development and production\n    this.apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.requestQueue = new Map();\n    console.log('TranslationService initialized with API URL:', this.apiUrl);\n    console.log('Environment:', process.env.NODE_ENV);\n  }\n\n  // Language mappings for better context\n  getLanguageInfo(languageKey) {\n    const languages = {\n      sinhala: {\n        name: 'Sinhala',\n        nativeName: 'සිංහල',\n        code: 'si'\n      },\n      english: {\n        name: 'English',\n        nativeName: 'English',\n        code: 'en'\n      },\n      tamil: {\n        name: 'Tamil',\n        nativeName: 'தமிழ்',\n        code: 'ta'\n      }\n    };\n    return languages[languageKey] || languages.english;\n  }\n\n  // Generate cache key\n  getCacheKey(text, targetLanguage, context) {\n    return `${text.substring(0, 50)}_${targetLanguage}_${context}`.replace(/\\s+/g, '_');\n  }\n\n  // Translate text using backend Gemini API\n  async translateText(text, targetLanguage, context = '') {\n    if (!text || text.trim() === '') {\n      return text;\n    }\n\n    // Return original text if target is Sinhala (default language)\n    if (targetLanguage === 'sinhala') {\n      return text;\n    }\n\n    const cacheKey = this.getCacheKey(text, targetLanguage, context);\n    \n    // Check cache first\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    // Check if there's already a pending request for this translation\n    if (this.requestQueue.has(cacheKey)) {\n      return await this.requestQueue.get(cacheKey);\n    }\n\n    // Create the translation request\n    const translationPromise = this.performTranslation(text, targetLanguage, context);\n    this.requestQueue.set(cacheKey, translationPromise);\n\n    try {\n      const result = await translationPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.requestQueue.delete(cacheKey);\n    }\n  }\n\n  // Perform the actual translation via backend API\n  async performTranslation(text, targetLanguage, context = '') {\n    const maxRetries = 2;\n    let lastError = null;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        const targetLangInfo = this.getLanguageInfo(targetLanguage);\n\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout\n\n        const response = await fetch(`${this.apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            text: text,\n            targetLanguage: targetLanguage,\n            targetLanguageName: targetLangInfo.name,\n            context: context || 'horoscope_content'\n          }),\n          signal: controller.signal\n        });\n\n        clearTimeout(timeoutId);\n\n        if (!response.ok) {\n          throw new Error(`Translation API error: ${response.status} - ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.translatedText) {\n          return data.translatedText;\n        } else {\n          throw new Error(data.error || 'Translation failed - no translated text received');\n        }\n      } catch (error) {\n        lastError = error;\n        console.warn(`Translation attempt ${attempt + 1} failed:`, error.message);\n\n        // If it's the last attempt or a non-retryable error, break\n        if (attempt === maxRetries || error.name === 'AbortError') {\n          break;\n        }\n\n        // Wait before retrying (exponential backoff)\n        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));\n      }\n    }\n\n    console.error('Translation service error after all retries:', lastError);\n\n    // Fallback: return original text if all translation attempts fail\n    return text;\n  }\n\n  // Translate multiple texts efficiently\n  async translateMultiple(textArray, targetLanguage, context = '') {\n    if (targetLanguage === 'sinhala') {\n      return textArray;\n    }\n\n    try {\n      const translations = await Promise.all(\n        textArray.map(text => this.translateText(text, targetLanguage, context))\n      );\n      return translations;\n    } catch (error) {\n      console.error('Batch translation error:', error);\n      return textArray; // Return original texts if batch translation fails\n    }\n  }\n\n  // Translate horoscope categories\n  async translateHoroscopeCategories(categories, targetLanguage) {\n    if (targetLanguage === 'sinhala' || !categories || categories.length === 0) {\n      return categories;\n    }\n\n    try {\n      const translatedCategories = await Promise.all(\n        categories.map(async (category) => {\n          const translatedTitle = await this.translateText(\n            category.title, \n            targetLanguage, \n            'horoscope_category_title'\n          );\n          \n          const translatedContent = await this.translateText(\n            category.content, \n            targetLanguage, \n            'horoscope_content'\n          );\n\n          return {\n            ...category,\n            title: translatedTitle,\n            content: translatedContent\n          };\n        })\n      );\n\n      return translatedCategories;\n    } catch (error) {\n      console.error('Horoscope categories translation error:', error);\n      return categories; // Return original categories if translation fails\n    }\n  }\n\n  // Clear cache (useful for memory management)\n  clearCache() {\n    this.cache.clear();\n  }\n\n  // Get cache size for debugging\n  getCacheSize() {\n    return this.cache.size;\n  }\n}\n\nconst translationService = new TranslationService();\nexport default translationService;\n"], "mappings": "yHAAA,KAAM,CAAAA,kBAAmB,CACvBC,WAAWA,CAAA,CAAG,CACZ;AACA,IAAI,CAACC,MAAM,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAC1E,IAAI,CAACC,KAAK,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACtB,IAAI,CAACC,YAAY,CAAG,GAAI,CAAAD,GAAG,CAAC,CAAC,CAC7BE,OAAO,CAACC,GAAG,CAAC,8CAA8C,CAAE,IAAI,CAACR,MAAM,CAAC,CACxEO,OAAO,CAACC,GAAG,CAAC,cAAc,CAAEP,OAAO,CAACC,GAAG,CAACO,QAAQ,CAAC,CACnD,CAEA;AACAC,eAAeA,CAACC,WAAW,CAAE,CAC3B,KAAM,CAAAC,SAAS,CAAG,CAChBC,OAAO,CAAE,CACPC,IAAI,CAAE,SAAS,CACfC,UAAU,CAAE,OAAO,CACnBC,IAAI,CAAE,IACR,CAAC,CACDC,OAAO,CAAE,CACPH,IAAI,CAAE,SAAS,CACfC,UAAU,CAAE,SAAS,CACrBC,IAAI,CAAE,IACR,CAAC,CACDE,KAAK,CAAE,CACLJ,IAAI,CAAE,OAAO,CACbC,UAAU,CAAE,OAAO,CACnBC,IAAI,CAAE,IACR,CACF,CAAC,CACD,MAAO,CAAAJ,SAAS,CAACD,WAAW,CAAC,EAAIC,SAAS,CAACK,OAAO,CACpD,CAEA;AACAE,WAAWA,CAACC,IAAI,CAAEC,cAAc,CAAEC,OAAO,CAAE,CACzC,MAAO,GAAAC,MAAA,CAAGH,IAAI,CAACI,SAAS,CAAC,CAAC,CAAE,EAAE,CAAC,MAAAD,MAAA,CAAIF,cAAc,MAAAE,MAAA,CAAID,OAAO,EAAGG,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CACrF,CAEA;AACA,KAAM,CAAAC,aAAaA,CAACN,IAAI,CAAEC,cAAc,CAAgB,IAAd,CAAAC,OAAO,CAAAK,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACpD,GAAI,CAACP,IAAI,EAAIA,IAAI,CAACU,IAAI,CAAC,CAAC,GAAK,EAAE,CAAE,CAC/B,MAAO,CAAAV,IAAI,CACb,CAEA;AACA,GAAIC,cAAc,GAAK,SAAS,CAAE,CAChC,MAAO,CAAAD,IAAI,CACb,CAEA,KAAM,CAAAW,QAAQ,CAAG,IAAI,CAACZ,WAAW,CAACC,IAAI,CAAEC,cAAc,CAAEC,OAAO,CAAC,CAEhE;AACA,GAAI,IAAI,CAAClB,KAAK,CAAC4B,GAAG,CAACD,QAAQ,CAAC,CAAE,CAC5B,MAAO,KAAI,CAAC3B,KAAK,CAAC6B,GAAG,CAACF,QAAQ,CAAC,CACjC,CAEA;AACA,GAAI,IAAI,CAACzB,YAAY,CAAC0B,GAAG,CAACD,QAAQ,CAAC,CAAE,CACnC,MAAO,MAAM,KAAI,CAACzB,YAAY,CAAC2B,GAAG,CAACF,QAAQ,CAAC,CAC9C,CAEA;AACA,KAAM,CAAAG,kBAAkB,CAAG,IAAI,CAACC,kBAAkB,CAACf,IAAI,CAAEC,cAAc,CAAEC,OAAO,CAAC,CACjF,IAAI,CAAChB,YAAY,CAAC8B,GAAG,CAACL,QAAQ,CAAEG,kBAAkB,CAAC,CAEnD,GAAI,CACF,KAAM,CAAAG,MAAM,CAAG,KAAM,CAAAH,kBAAkB,CACvC,IAAI,CAAC9B,KAAK,CAACgC,GAAG,CAACL,QAAQ,CAAEM,MAAM,CAAC,CAChC,MAAO,CAAAA,MAAM,CACf,CAAC,OAAS,CACR,IAAI,CAAC/B,YAAY,CAACgC,MAAM,CAACP,QAAQ,CAAC,CACpC,CACF,CAEA;AACA,KAAM,CAAAI,kBAAkBA,CAACf,IAAI,CAAEC,cAAc,CAAgB,IAAd,CAAAC,OAAO,CAAAK,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CACzD,KAAM,CAAAY,UAAU,CAAG,CAAC,CACpB,GAAI,CAAAC,SAAS,CAAG,IAAI,CAEpB,IAAK,GAAI,CAAAC,OAAO,CAAG,CAAC,CAAEA,OAAO,EAAIF,UAAU,CAAEE,OAAO,EAAE,CAAE,CACtD,GAAI,CACF,KAAM,CAAAC,cAAc,CAAG,IAAI,CAAChC,eAAe,CAACW,cAAc,CAAC,CAE3D,KAAM,CAAAsB,UAAU,CAAG,GAAI,CAAAC,eAAe,CAAC,CAAC,CACxC,KAAM,CAAAC,SAAS,CAAGC,UAAU,CAAC,IAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,CAAE,KAAK,CAAC,CAAE;AAE/D,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,IAAA1B,MAAA,CAAI,IAAI,CAACvB,MAAM,eAAc,CACvDkD,MAAM,CAAE,MAAM,CACdC,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CAAC,CACDC,IAAI,CAAEC,IAAI,CAACC,SAAS,CAAC,CACnBlC,IAAI,CAAEA,IAAI,CACVC,cAAc,CAAEA,cAAc,CAC9BkC,kBAAkB,CAAEb,cAAc,CAAC5B,IAAI,CACvCQ,OAAO,CAAEA,OAAO,EAAI,mBACtB,CAAC,CAAC,CACFkC,MAAM,CAAEb,UAAU,CAACa,MACrB,CAAC,CAAC,CAEFC,YAAY,CAACZ,SAAS,CAAC,CAEvB,GAAI,CAACG,QAAQ,CAACU,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,2BAAApC,MAAA,CAA2ByB,QAAQ,CAACY,MAAM,QAAArC,MAAA,CAAMyB,QAAQ,CAACa,UAAU,CAAE,CAAC,CACvF,CAEA,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAd,QAAQ,CAACe,IAAI,CAAC,CAAC,CAElC,GAAID,IAAI,CAACE,OAAO,EAAIF,IAAI,CAACG,cAAc,CAAE,CACvC,MAAO,CAAAH,IAAI,CAACG,cAAc,CAC5B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAN,KAAK,CAACG,IAAI,CAACI,KAAK,EAAI,kDAAkD,CAAC,CACnF,CACF,CAAE,MAAOA,KAAK,CAAE,CACd1B,SAAS,CAAG0B,KAAK,CACjB3D,OAAO,CAAC4D,IAAI,wBAAA5C,MAAA,CAAwBkB,OAAO,CAAG,CAAC,aAAYyB,KAAK,CAACE,OAAO,CAAC,CAEzE;AACA,GAAI3B,OAAO,GAAKF,UAAU,EAAI2B,KAAK,CAACpD,IAAI,GAAK,YAAY,CAAE,CACzD,MACF,CAEA;AACA,KAAM,IAAI,CAAAuD,OAAO,CAACC,OAAO,EAAIxB,UAAU,CAACwB,OAAO,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAE/B,OAAO,CAAC,CAAG,IAAI,CAAC,CAAC,CAChF,CACF,CAEAlC,OAAO,CAAC2D,KAAK,CAAC,8CAA8C,CAAE1B,SAAS,CAAC,CAExE;AACA,MAAO,CAAApB,IAAI,CACb,CAEA;AACA,KAAM,CAAAqD,iBAAiBA,CAACC,SAAS,CAAErD,cAAc,CAAgB,IAAd,CAAAC,OAAO,CAAAK,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC7D,GAAIN,cAAc,GAAK,SAAS,CAAE,CAChC,MAAO,CAAAqD,SAAS,CAClB,CAEA,GAAI,CACF,KAAM,CAAAC,YAAY,CAAG,KAAM,CAAAN,OAAO,CAACO,GAAG,CACpCF,SAAS,CAACG,GAAG,CAACzD,IAAI,EAAI,IAAI,CAACM,aAAa,CAACN,IAAI,CAAEC,cAAc,CAAEC,OAAO,CAAC,CACzE,CAAC,CACD,MAAO,CAAAqD,YAAY,CACrB,CAAE,MAAOT,KAAK,CAAE,CACd3D,OAAO,CAAC2D,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,MAAO,CAAAQ,SAAS,CAAE;AACpB,CACF,CAEA;AACA,KAAM,CAAAI,4BAA4BA,CAACC,UAAU,CAAE1D,cAAc,CAAE,CAC7D,GAAIA,cAAc,GAAK,SAAS,EAAI,CAAC0D,UAAU,EAAIA,UAAU,CAACnD,MAAM,GAAK,CAAC,CAAE,CAC1E,MAAO,CAAAmD,UAAU,CACnB,CAEA,GAAI,CACF,KAAM,CAAAC,oBAAoB,CAAG,KAAM,CAAAX,OAAO,CAACO,GAAG,CAC5CG,UAAU,CAACF,GAAG,CAAC,KAAO,CAAAI,QAAQ,EAAK,CACjC,KAAM,CAAAC,eAAe,CAAG,KAAM,KAAI,CAACxD,aAAa,CAC9CuD,QAAQ,CAACE,KAAK,CACd9D,cAAc,CACd,0BACF,CAAC,CAED,KAAM,CAAA+D,iBAAiB,CAAG,KAAM,KAAI,CAAC1D,aAAa,CAChDuD,QAAQ,CAACI,OAAO,CAChBhE,cAAc,CACd,mBACF,CAAC,CAED,OAAAiE,aAAA,CAAAA,aAAA,IACKL,QAAQ,MACXE,KAAK,CAAED,eAAe,CACtBG,OAAO,CAAED,iBAAiB,GAE9B,CAAC,CACH,CAAC,CAED,MAAO,CAAAJ,oBAAoB,CAC7B,CAAE,MAAOd,KAAK,CAAE,CACd3D,OAAO,CAAC2D,KAAK,CAAC,yCAAyC,CAAEA,KAAK,CAAC,CAC/D,MAAO,CAAAa,UAAU,CAAE;AACrB,CACF,CAEA;AACAQ,UAAUA,CAAA,CAAG,CACX,IAAI,CAACnF,KAAK,CAACoF,KAAK,CAAC,CAAC,CACpB,CAEA;AACAC,YAAYA,CAAA,CAAG,CACb,MAAO,KAAI,CAACrF,KAAK,CAACsF,IAAI,CACxB,CACF,CAEA,KAAM,CAAAC,kBAAkB,CAAG,GAAI,CAAA7F,kBAAkB,CAAC,CAAC,CACnD,cAAe,CAAA6F,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}