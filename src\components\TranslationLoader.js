import React from 'react';
import { useTranslation } from '../contexts/TranslationContext';

const TranslationLoader = ({ 
  children, 
  showOverlay = false, 
  overlayMessage = 'Translating content...',
  className = '',
  style = {} 
}) => {
  const { isTranslating, currentLanguage } = useTranslation();

  const getLoadingMessage = () => {
    switch (currentLanguage) {
      case 'english':
        return 'Translating content...';
      case 'tamil':
        return 'உள்ளடக்கத்தை மொழிபெயர்க்கிறது...';
      case 'sinhala':
      default:
        return 'අන්තර්ගතය පරිවර්තනය කරමින්...';
    }
  };

  if (showOverlay && isTranslating) {
    return (
      <div 
        className={`translation-loader-container ${className}`}
        style={{
          position: 'relative',
          ...style
        }}
      >
        {children}
        
        {/* Translation Overlay */}
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: 'rgba(20, 25, 40, 0.8)',
            backdropFilter: 'blur(4px)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 999,
            borderRadius: '15px'
          }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '12px',
              background: 'rgba(244, 208, 63, 0.1)',
              border: '1px solid rgba(244, 208, 63, 0.3)',
              borderRadius: '12px',
              padding: '16px 24px',
              color: '#f4d03f',
              fontFamily: 'Noto Sans Sinhala, sans-serif',
              fontSize: '1rem',
              fontWeight: '500'
            }}
          >
            <div
              style={{
                width: '20px',
                height: '20px',
                border: '2px solid rgba(244, 208, 63, 0.3)',
                borderTop: '2px solid #f4d03f',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite'
              }}
            />
            <span>{overlayMessage || getLoadingMessage()}</span>
          </div>
        </div>

        {/* CSS Animation */}
        <style jsx>{`
          @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
          }
        `}</style>
      </div>
    );
  }

  return (
    <div className={`translation-loader-container ${className}`} style={style}>
      {children}
      
      {/* Subtle loading indicator */}
      {isTranslating && (
        <div
          style={{
            position: 'fixed',
            top: '70px',
            right: '20px',
            background: 'rgba(244, 208, 63, 0.1)',
            border: '1px solid rgba(244, 208, 63, 0.3)',
            borderRadius: '8px',
            padding: '8px 12px',
            color: '#f4d03f',
            fontSize: '0.85rem',
            fontFamily: 'Noto Sans Sinhala, sans-serif',
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            zIndex: 1000,
            backdropFilter: 'blur(10px)',
            boxShadow: '0 4px 15px rgba(244, 208, 63, 0.1)'
          }}
        >
          <div
            style={{
              width: '12px',
              height: '12px',
              border: '1px solid rgba(244, 208, 63, 0.3)',
              borderTop: '1px solid #f4d03f',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}
          />
          <span>{getLoadingMessage()}</span>
        </div>
      )}

      {/* CSS Animation */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
};

export default TranslationLoader;
