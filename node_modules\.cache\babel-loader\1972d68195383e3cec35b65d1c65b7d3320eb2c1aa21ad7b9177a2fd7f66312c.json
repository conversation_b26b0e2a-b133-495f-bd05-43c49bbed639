{"ast": null, "code": "class TranslationService {\n  constructor() {\n    // Use relative URL in development to leverage proxy, absolute URL in production\n    this.apiUrl = process.env.NODE_ENV === 'development' ? '/api' : process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.requestQueue = new Map();\n    console.log('TranslationService initialized with API URL:', this.apiUrl);\n  }\n\n  // Language mappings for better context\n  getLanguageInfo(languageKey) {\n    const languages = {\n      sinhala: {\n        name: 'Sinhala',\n        nativeName: 'සිංහල',\n        code: 'si'\n      },\n      english: {\n        name: 'English',\n        nativeName: 'English',\n        code: 'en'\n      },\n      tamil: {\n        name: 'Tamil',\n        nativeName: 'தமிழ்',\n        code: 'ta'\n      }\n    };\n    return languages[languageKey] || languages.english;\n  }\n\n  // Generate cache key\n  getCacheKey(text, targetLanguage, context) {\n    return `${text.substring(0, 50)}_${targetLanguage}_${context}`.replace(/\\s+/g, '_');\n  }\n\n  // Translate text using backend Gemini API\n  async translateText(text, targetLanguage, context = '') {\n    if (!text || text.trim() === '') {\n      return text;\n    }\n\n    // Return original text if target is Sinhala (default language)\n    if (targetLanguage === 'sinhala') {\n      return text;\n    }\n    const cacheKey = this.getCacheKey(text, targetLanguage, context);\n\n    // Check cache first\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    // Check if there's already a pending request for this translation\n    if (this.requestQueue.has(cacheKey)) {\n      return await this.requestQueue.get(cacheKey);\n    }\n\n    // Create the translation request\n    const translationPromise = this.performTranslation(text, targetLanguage, context);\n    this.requestQueue.set(cacheKey, translationPromise);\n    try {\n      const result = await translationPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.requestQueue.delete(cacheKey);\n    }\n  }\n\n  // Perform the actual translation via backend API\n  async performTranslation(text, targetLanguage, context = '') {\n    const maxRetries = 2;\n    let lastError = null;\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        const targetLangInfo = this.getLanguageInfo(targetLanguage);\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout\n\n        const response = await fetch(`${this.apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json'\n          },\n          body: JSON.stringify({\n            text: text,\n            targetLanguage: targetLanguage,\n            targetLanguageName: targetLangInfo.name,\n            context: context || 'horoscope_content'\n          }),\n          signal: controller.signal\n        });\n        clearTimeout(timeoutId);\n        if (!response.ok) {\n          throw new Error(`Translation API error: ${response.status} - ${response.statusText}`);\n        }\n        const data = await response.json();\n        if (data.success && data.translatedText) {\n          return data.translatedText;\n        } else {\n          throw new Error(data.error || 'Translation failed - no translated text received');\n        }\n      } catch (error) {\n        lastError = error;\n        console.warn(`Translation attempt ${attempt + 1} failed:`, error.message);\n\n        // If it's the last attempt or a non-retryable error, break\n        if (attempt === maxRetries || error.name === 'AbortError') {\n          break;\n        }\n\n        // Wait before retrying (exponential backoff)\n        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));\n      }\n    }\n    console.error('Translation service error after all retries:', lastError);\n\n    // Fallback: return original text if all translation attempts fail\n    return text;\n  }\n\n  // Translate multiple texts efficiently\n  async translateMultiple(textArray, targetLanguage, context = '') {\n    if (targetLanguage === 'sinhala') {\n      return textArray;\n    }\n    try {\n      const translations = await Promise.all(textArray.map(text => this.translateText(text, targetLanguage, context)));\n      return translations;\n    } catch (error) {\n      console.error('Batch translation error:', error);\n      return textArray; // Return original texts if batch translation fails\n    }\n  }\n\n  // Translate horoscope categories\n  async translateHoroscopeCategories(categories, targetLanguage) {\n    if (targetLanguage === 'sinhala' || !categories || categories.length === 0) {\n      return categories;\n    }\n    try {\n      const translatedCategories = await Promise.all(categories.map(async category => {\n        const translatedTitle = await this.translateText(category.title, targetLanguage, 'horoscope_category_title');\n        const translatedContent = await this.translateText(category.content, targetLanguage, 'horoscope_content');\n        return {\n          ...category,\n          title: translatedTitle,\n          content: translatedContent\n        };\n      }));\n      return translatedCategories;\n    } catch (error) {\n      console.error('Horoscope categories translation error:', error);\n      return categories; // Return original categories if translation fails\n    }\n  }\n\n  // Clear cache (useful for memory management)\n  clearCache() {\n    this.cache.clear();\n  }\n\n  // Get cache size for debugging\n  getCacheSize() {\n    return this.cache.size;\n  }\n}\nconst translationService = new TranslationService();\nexport default translationService;", "map": {"version": 3, "names": ["TranslationService", "constructor", "apiUrl", "process", "env", "NODE_ENV", "REACT_APP_API_URL", "cache", "Map", "requestQueue", "console", "log", "getLanguageInfo", "languageKey", "languages", "sinhala", "name", "nativeName", "code", "english", "tamil", "get<PERSON><PERSON><PERSON><PERSON>", "text", "targetLanguage", "context", "substring", "replace", "translateText", "trim", "cache<PERSON>ey", "has", "get", "translationPromise", "performTranslation", "set", "result", "delete", "maxRetries", "lastError", "attempt", "targetLangInfo", "controller", "AbortController", "timeoutId", "setTimeout", "abort", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "targetLanguageName", "signal", "clearTimeout", "ok", "Error", "status", "statusText", "data", "json", "success", "translatedText", "error", "warn", "message", "Promise", "resolve", "Math", "pow", "translateMultiple", "textArray", "translations", "all", "map", "translateHoroscopeCategories", "categories", "length", "translatedCategories", "category", "translatedTitle", "title", "<PERSON><PERSON><PERSON><PERSON>", "content", "clearCache", "clear", "getCacheSize", "size", "translationService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/TranslationService.js"], "sourcesContent": ["class TranslationService {\n  constructor() {\n    // Use relative URL in development to leverage proxy, absolute URL in production\n    this.apiUrl = process.env.NODE_ENV === 'development'\n      ? '/api'\n      : (process.env.REACT_APP_API_URL || 'http://localhost:5000/api');\n    this.cache = new Map();\n    this.requestQueue = new Map();\n    console.log('TranslationService initialized with API URL:', this.apiUrl);\n  }\n\n  // Language mappings for better context\n  getLanguageInfo(languageKey) {\n    const languages = {\n      sinhala: {\n        name: 'Sinhala',\n        nativeName: 'සිංහල',\n        code: 'si'\n      },\n      english: {\n        name: 'English',\n        nativeName: 'English',\n        code: 'en'\n      },\n      tamil: {\n        name: 'Tamil',\n        nativeName: 'தமிழ்',\n        code: 'ta'\n      }\n    };\n    return languages[languageKey] || languages.english;\n  }\n\n  // Generate cache key\n  getCacheKey(text, targetLanguage, context) {\n    return `${text.substring(0, 50)}_${targetLanguage}_${context}`.replace(/\\s+/g, '_');\n  }\n\n  // Translate text using backend Gemini API\n  async translateText(text, targetLanguage, context = '') {\n    if (!text || text.trim() === '') {\n      return text;\n    }\n\n    // Return original text if target is Sinhala (default language)\n    if (targetLanguage === 'sinhala') {\n      return text;\n    }\n\n    const cacheKey = this.getCacheKey(text, targetLanguage, context);\n    \n    // Check cache first\n    if (this.cache.has(cacheKey)) {\n      return this.cache.get(cacheKey);\n    }\n\n    // Check if there's already a pending request for this translation\n    if (this.requestQueue.has(cacheKey)) {\n      return await this.requestQueue.get(cacheKey);\n    }\n\n    // Create the translation request\n    const translationPromise = this.performTranslation(text, targetLanguage, context);\n    this.requestQueue.set(cacheKey, translationPromise);\n\n    try {\n      const result = await translationPromise;\n      this.cache.set(cacheKey, result);\n      return result;\n    } finally {\n      this.requestQueue.delete(cacheKey);\n    }\n  }\n\n  // Perform the actual translation via backend API\n  async performTranslation(text, targetLanguage, context = '') {\n    const maxRetries = 2;\n    let lastError = null;\n\n    for (let attempt = 0; attempt <= maxRetries; attempt++) {\n      try {\n        const targetLangInfo = this.getLanguageInfo(targetLanguage);\n\n        const controller = new AbortController();\n        const timeoutId = setTimeout(() => controller.abort(), 15000); // 15 second timeout\n\n        const response = await fetch(`${this.apiUrl}/translate`, {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            text: text,\n            targetLanguage: targetLanguage,\n            targetLanguageName: targetLangInfo.name,\n            context: context || 'horoscope_content'\n          }),\n          signal: controller.signal\n        });\n\n        clearTimeout(timeoutId);\n\n        if (!response.ok) {\n          throw new Error(`Translation API error: ${response.status} - ${response.statusText}`);\n        }\n\n        const data = await response.json();\n\n        if (data.success && data.translatedText) {\n          return data.translatedText;\n        } else {\n          throw new Error(data.error || 'Translation failed - no translated text received');\n        }\n      } catch (error) {\n        lastError = error;\n        console.warn(`Translation attempt ${attempt + 1} failed:`, error.message);\n\n        // If it's the last attempt or a non-retryable error, break\n        if (attempt === maxRetries || error.name === 'AbortError') {\n          break;\n        }\n\n        // Wait before retrying (exponential backoff)\n        await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));\n      }\n    }\n\n    console.error('Translation service error after all retries:', lastError);\n\n    // Fallback: return original text if all translation attempts fail\n    return text;\n  }\n\n  // Translate multiple texts efficiently\n  async translateMultiple(textArray, targetLanguage, context = '') {\n    if (targetLanguage === 'sinhala') {\n      return textArray;\n    }\n\n    try {\n      const translations = await Promise.all(\n        textArray.map(text => this.translateText(text, targetLanguage, context))\n      );\n      return translations;\n    } catch (error) {\n      console.error('Batch translation error:', error);\n      return textArray; // Return original texts if batch translation fails\n    }\n  }\n\n  // Translate horoscope categories\n  async translateHoroscopeCategories(categories, targetLanguage) {\n    if (targetLanguage === 'sinhala' || !categories || categories.length === 0) {\n      return categories;\n    }\n\n    try {\n      const translatedCategories = await Promise.all(\n        categories.map(async (category) => {\n          const translatedTitle = await this.translateText(\n            category.title, \n            targetLanguage, \n            'horoscope_category_title'\n          );\n          \n          const translatedContent = await this.translateText(\n            category.content, \n            targetLanguage, \n            'horoscope_content'\n          );\n\n          return {\n            ...category,\n            title: translatedTitle,\n            content: translatedContent\n          };\n        })\n      );\n\n      return translatedCategories;\n    } catch (error) {\n      console.error('Horoscope categories translation error:', error);\n      return categories; // Return original categories if translation fails\n    }\n  }\n\n  // Clear cache (useful for memory management)\n  clearCache() {\n    this.cache.clear();\n  }\n\n  // Get cache size for debugging\n  getCacheSize() {\n    return this.cache.size;\n  }\n}\n\nconst translationService = new TranslationService();\nexport default translationService;\n"], "mappings": "AAAA,MAAMA,kBAAkB,CAAC;EACvBC,WAAWA,CAAA,EAAG;IACZ;IACA,IAAI,CAACC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,GAChD,MAAM,GACLF,OAAO,CAACC,GAAG,CAACE,iBAAiB,IAAI,2BAA4B;IAClE,IAAI,CAACC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,YAAY,GAAG,IAAID,GAAG,CAAC,CAAC;IAC7BE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE,IAAI,CAACT,MAAM,CAAC;EAC1E;;EAEA;EACAU,eAAeA,CAACC,WAAW,EAAE;IAC3B,MAAMC,SAAS,GAAG;MAChBC,OAAO,EAAE;QACPC,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,OAAO;QACnBC,IAAI,EAAE;MACR,CAAC;MACDC,OAAO,EAAE;QACPH,IAAI,EAAE,SAAS;QACfC,UAAU,EAAE,SAAS;QACrBC,IAAI,EAAE;MACR,CAAC;MACDE,KAAK,EAAE;QACLJ,IAAI,EAAE,OAAO;QACbC,UAAU,EAAE,OAAO;QACnBC,IAAI,EAAE;MACR;IACF,CAAC;IACD,OAAOJ,SAAS,CAACD,WAAW,CAAC,IAAIC,SAAS,CAACK,OAAO;EACpD;;EAEA;EACAE,WAAWA,CAACC,IAAI,EAAEC,cAAc,EAAEC,OAAO,EAAE;IACzC,OAAO,GAAGF,IAAI,CAACG,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,IAAIF,cAAc,IAAIC,OAAO,EAAE,CAACE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACrF;;EAEA;EACA,MAAMC,aAAaA,CAACL,IAAI,EAAEC,cAAc,EAAEC,OAAO,GAAG,EAAE,EAAE;IACtD,IAAI,CAACF,IAAI,IAAIA,IAAI,CAACM,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/B,OAAON,IAAI;IACb;;IAEA;IACA,IAAIC,cAAc,KAAK,SAAS,EAAE;MAChC,OAAOD,IAAI;IACb;IAEA,MAAMO,QAAQ,GAAG,IAAI,CAACR,WAAW,CAACC,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;;IAEhE;IACA,IAAI,IAAI,CAACjB,KAAK,CAACuB,GAAG,CAACD,QAAQ,CAAC,EAAE;MAC5B,OAAO,IAAI,CAACtB,KAAK,CAACwB,GAAG,CAACF,QAAQ,CAAC;IACjC;;IAEA;IACA,IAAI,IAAI,CAACpB,YAAY,CAACqB,GAAG,CAACD,QAAQ,CAAC,EAAE;MACnC,OAAO,MAAM,IAAI,CAACpB,YAAY,CAACsB,GAAG,CAACF,QAAQ,CAAC;IAC9C;;IAEA;IACA,MAAMG,kBAAkB,GAAG,IAAI,CAACC,kBAAkB,CAACX,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC;IACjF,IAAI,CAACf,YAAY,CAACyB,GAAG,CAACL,QAAQ,EAAEG,kBAAkB,CAAC;IAEnD,IAAI;MACF,MAAMG,MAAM,GAAG,MAAMH,kBAAkB;MACvC,IAAI,CAACzB,KAAK,CAAC2B,GAAG,CAACL,QAAQ,EAAEM,MAAM,CAAC;MAChC,OAAOA,MAAM;IACf,CAAC,SAAS;MACR,IAAI,CAAC1B,YAAY,CAAC2B,MAAM,CAACP,QAAQ,CAAC;IACpC;EACF;;EAEA;EACA,MAAMI,kBAAkBA,CAACX,IAAI,EAAEC,cAAc,EAAEC,OAAO,GAAG,EAAE,EAAE;IAC3D,MAAMa,UAAU,GAAG,CAAC;IACpB,IAAIC,SAAS,GAAG,IAAI;IAEpB,KAAK,IAAIC,OAAO,GAAG,CAAC,EAAEA,OAAO,IAAIF,UAAU,EAAEE,OAAO,EAAE,EAAE;MACtD,IAAI;QACF,MAAMC,cAAc,GAAG,IAAI,CAAC5B,eAAe,CAACW,cAAc,CAAC;QAE3D,MAAMkB,UAAU,GAAG,IAAIC,eAAe,CAAC,CAAC;QACxC,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAMH,UAAU,CAACI,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;;QAE/D,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAG,IAAI,CAAC7C,MAAM,YAAY,EAAE;UACvD8C,MAAM,EAAE,MAAM;UACdC,OAAO,EAAE;YACP,cAAc,EAAE;UAClB,CAAC;UACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;YACnB9B,IAAI,EAAEA,IAAI;YACVC,cAAc,EAAEA,cAAc;YAC9B8B,kBAAkB,EAAEb,cAAc,CAACxB,IAAI;YACvCQ,OAAO,EAAEA,OAAO,IAAI;UACtB,CAAC,CAAC;UACF8B,MAAM,EAAEb,UAAU,CAACa;QACrB,CAAC,CAAC;QAEFC,YAAY,CAACZ,SAAS,CAAC;QAEvB,IAAI,CAACG,QAAQ,CAACU,EAAE,EAAE;UAChB,MAAM,IAAIC,KAAK,CAAC,0BAA0BX,QAAQ,CAACY,MAAM,MAAMZ,QAAQ,CAACa,UAAU,EAAE,CAAC;QACvF;QAEA,MAAMC,IAAI,GAAG,MAAMd,QAAQ,CAACe,IAAI,CAAC,CAAC;QAElC,IAAID,IAAI,CAACE,OAAO,IAAIF,IAAI,CAACG,cAAc,EAAE;UACvC,OAAOH,IAAI,CAACG,cAAc;QAC5B,CAAC,MAAM;UACL,MAAM,IAAIN,KAAK,CAACG,IAAI,CAACI,KAAK,IAAI,kDAAkD,CAAC;QACnF;MACF,CAAC,CAAC,OAAOA,KAAK,EAAE;QACd1B,SAAS,GAAG0B,KAAK;QACjBtD,OAAO,CAACuD,IAAI,CAAC,uBAAuB1B,OAAO,GAAG,CAAC,UAAU,EAAEyB,KAAK,CAACE,OAAO,CAAC;;QAEzE;QACA,IAAI3B,OAAO,KAAKF,UAAU,IAAI2B,KAAK,CAAChD,IAAI,KAAK,YAAY,EAAE;UACzD;QACF;;QAEA;QACA,MAAM,IAAImD,OAAO,CAACC,OAAO,IAAIxB,UAAU,CAACwB,OAAO,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE/B,OAAO,CAAC,GAAG,IAAI,CAAC,CAAC;MAChF;IACF;IAEA7B,OAAO,CAACsD,KAAK,CAAC,8CAA8C,EAAE1B,SAAS,CAAC;;IAExE;IACA,OAAOhB,IAAI;EACb;;EAEA;EACA,MAAMiD,iBAAiBA,CAACC,SAAS,EAAEjD,cAAc,EAAEC,OAAO,GAAG,EAAE,EAAE;IAC/D,IAAID,cAAc,KAAK,SAAS,EAAE;MAChC,OAAOiD,SAAS;IAClB;IAEA,IAAI;MACF,MAAMC,YAAY,GAAG,MAAMN,OAAO,CAACO,GAAG,CACpCF,SAAS,CAACG,GAAG,CAACrD,IAAI,IAAI,IAAI,CAACK,aAAa,CAACL,IAAI,EAAEC,cAAc,EAAEC,OAAO,CAAC,CACzE,CAAC;MACD,OAAOiD,YAAY;IACrB,CAAC,CAAC,OAAOT,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAOQ,SAAS,CAAC,CAAC;IACpB;EACF;;EAEA;EACA,MAAMI,4BAA4BA,CAACC,UAAU,EAAEtD,cAAc,EAAE;IAC7D,IAAIA,cAAc,KAAK,SAAS,IAAI,CAACsD,UAAU,IAAIA,UAAU,CAACC,MAAM,KAAK,CAAC,EAAE;MAC1E,OAAOD,UAAU;IACnB;IAEA,IAAI;MACF,MAAME,oBAAoB,GAAG,MAAMZ,OAAO,CAACO,GAAG,CAC5CG,UAAU,CAACF,GAAG,CAAC,MAAOK,QAAQ,IAAK;QACjC,MAAMC,eAAe,GAAG,MAAM,IAAI,CAACtD,aAAa,CAC9CqD,QAAQ,CAACE,KAAK,EACd3D,cAAc,EACd,0BACF,CAAC;QAED,MAAM4D,iBAAiB,GAAG,MAAM,IAAI,CAACxD,aAAa,CAChDqD,QAAQ,CAACI,OAAO,EAChB7D,cAAc,EACd,mBACF,CAAC;QAED,OAAO;UACL,GAAGyD,QAAQ;UACXE,KAAK,EAAED,eAAe;UACtBG,OAAO,EAAED;QACX,CAAC;MACH,CAAC,CACH,CAAC;MAED,OAAOJ,oBAAoB;IAC7B,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdtD,OAAO,CAACsD,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAAC;MAC/D,OAAOa,UAAU,CAAC,CAAC;IACrB;EACF;;EAEA;EACAQ,UAAUA,CAAA,EAAG;IACX,IAAI,CAAC9E,KAAK,CAAC+E,KAAK,CAAC,CAAC;EACpB;;EAEA;EACAC,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAAChF,KAAK,CAACiF,IAAI;EACxB;AACF;AAEA,MAAMC,kBAAkB,GAAG,IAAIzF,kBAAkB,CAAC,CAAC;AACnD,eAAeyF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}