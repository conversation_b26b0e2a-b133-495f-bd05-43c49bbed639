import React, { createContext, useContext, useState, useCallback } from 'react';
import TranslationService from '../services/TranslationService';

const TranslationContext = createContext();

export const useTranslation = () => {
  const context = useContext(TranslationContext);
  if (!context) {
    throw new Error('useTranslation must be used within a TranslationProvider');
  }
  return context;
};

export const TranslationProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState('sinhala');
  const [translationCache, setTranslationCache] = useState({});
  const [isTranslating, setIsTranslating] = useState(false);

  const languages = {
    sinhala: {
      code: 'si',
      name: 'සිංහල',
      flag: '🇱🇰',
      nativeName: 'සිංහල'
    },
    english: {
      code: 'en',
      name: 'English',
      flag: '🇺🇸',
      nativeName: 'English'
    },
    tamil: {
      code: 'ta',
      name: 'தமிழ்',
      flag: '🇱🇰',
      nativeName: 'தமிழ்'
    }
  };

  // Generate cache key for translation
  const getCacheKey = (text, targetLang, sourceContext = '') => {
    return `${text}_${targetLang}_${sourceContext}`.replace(/\s+/g, '_').toLowerCase();
  };

  // Translate text using Gemini API
  const translateText = useCallback(async (text, targetLanguage, sourceContext = '') => {
    // If target language is Sinhala (default), return original text
    if (targetLanguage === 'sinhala' || !text || text.trim() === '') {
      return text;
    }

    const cacheKey = getCacheKey(text, targetLanguage, sourceContext);
    
    // Check cache first
    if (translationCache[cacheKey]) {
      return translationCache[cacheKey];
    }

    try {
      setIsTranslating(true);
      const translatedText = await TranslationService.translateText(text, targetLanguage, sourceContext);
      
      // Cache the translation
      setTranslationCache(prev => ({
        ...prev,
        [cacheKey]: translatedText
      }));

      return translatedText;
    } catch (error) {
      console.error('Translation error:', error);
      // Return original text if translation fails
      return text;
    } finally {
      setIsTranslating(false);
    }
  }, [translationCache]);

  // Translate multiple texts at once
  const translateMultiple = useCallback(async (textArray, targetLanguage, sourceContext = '') => {
    if (targetLanguage === 'sinhala') {
      return textArray;
    }

    const translations = await Promise.all(
      textArray.map(text => translateText(text, targetLanguage, sourceContext))
    );

    return translations;
  }, [translateText]);

  // Change language
  const changeLanguage = useCallback((languageKey) => {
    if (languages[languageKey]) {
      setCurrentLanguage(languageKey);
    }
  }, []);

  // Get translated text with fallback
  const t = useCallback(async (text, context = '') => {
    if (currentLanguage === 'sinhala') {
      return text;
    }
    return await translateText(text, currentLanguage, context);
  }, [currentLanguage, translateText]);

  const value = {
    currentLanguage,
    languages,
    changeLanguage,
    translateText,
    translateMultiple,
    t,
    isTranslating,
    translationCache
  };

  return (
    <TranslationContext.Provider value={value}>
      {children}
    </TranslationContext.Provider>
  );
};

export default TranslationContext;
