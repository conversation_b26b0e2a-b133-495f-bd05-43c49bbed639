import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from '../contexts/TranslationContext';

const LanguageSelector = ({ className = '', style = {} }) => {
  const { currentLanguage, languages, changeLanguage, isTranslating } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleLanguageChange = (languageKey) => {
    changeLanguage(languageKey);
    setIsOpen(false);
  };

  const currentLang = languages[currentLanguage];

  return (
    <div
      className={`language-selector ${className}`}
      style={{
        position: 'relative',
        zIndex: 1000,
        ...style
      }}
      ref={dropdownRef}
    >
      {/* Language Selector Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={isTranslating}
        className="language-selector-button"
        style={{
          background: 'rgba(244, 208, 63, 0.15)',
          border: '1px solid rgba(244, 208, 63, 0.3)',
          borderRadius: '15px',
          padding: '12px 20px',
          color: '#f4d03f',
          fontSize: '1rem',
          fontWeight: '600',
          cursor: isTranslating ? 'not-allowed' : 'pointer',
          display: 'flex',
          alignItems: 'center',
          gap: '10px',
          backdropFilter: 'blur(10px)',
          transition: 'all 0.3s ease',
          minWidth: '140px',
          justifyContent: 'space-between',
          fontFamily: 'Noto Sans Sinhala, sans-serif',
          opacity: isTranslating ? 0.7 : 1,
          boxShadow: '0 4px 15px rgba(244, 208, 63, 0.1)',
          width: '100%',
          maxWidth: '200px'
        }}
        onMouseEnter={(e) => {
          if (!isTranslating) {
            e.target.style.background = 'rgba(244, 208, 63, 0.25)';
            e.target.style.borderColor = 'rgba(244, 208, 63, 0.5)';
            e.target.style.transform = 'translateY(-2px)';
            e.target.style.boxShadow = '0 6px 20px rgba(244, 208, 63, 0.2)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isTranslating) {
            e.target.style.background = 'rgba(244, 208, 63, 0.15)';
            e.target.style.borderColor = 'rgba(244, 208, 63, 0.3)';
            e.target.style.transform = 'translateY(0)';
            e.target.style.boxShadow = '0 4px 15px rgba(244, 208, 63, 0.1)';
          }
        }}
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <span style={{ fontSize: '1.2rem' }}>{currentLang.flag}</span>
          <span>{currentLang.nativeName}</span>
        </div>
        
        {isTranslating ? (
          <div
            style={{
              width: '16px',
              height: '16px',
              border: '2px solid rgba(244, 208, 63, 0.3)',
              borderTop: '2px solid #f4d03f',
              borderRadius: '50%',
              animation: 'spin 1s linear infinite'
            }}
            title="Translating content..."
          />
        ) : (
          <span style={{
            fontSize: '0.8rem',
            transform: isOpen ? 'rotate(180deg)' : 'rotate(0deg)',
            transition: 'transform 0.3s ease'
          }}>
            ▼
          </span>
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div
          className="language-dropdown"
          style={{
            position: 'absolute',
            top: '100%',
            left: '0',
            right: '0',
            marginTop: '8px',
            background: 'rgba(20, 25, 40, 0.95)',
            border: '1px solid rgba(244, 208, 63, 0.3)',
            borderRadius: '15px',
            backdropFilter: 'blur(15px)',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3), 0 0 20px rgba(244, 208, 63, 0.1)',
            overflow: 'hidden',
            animation: 'fadeInDown 0.3s ease',
            minWidth: '200px',
            maxWidth: '250px'
          }}
        >
          {Object.entries(languages).map(([key, lang]) => (
            <button
              key={key}
              onClick={() => handleLanguageChange(key)}
              disabled={isTranslating}
              style={{
                width: '100%',
                padding: '12px 20px',
                background: currentLanguage === key 
                  ? 'rgba(244, 208, 63, 0.2)' 
                  : 'transparent',
                border: 'none',
                color: currentLanguage === key ? '#f4d03f' : '#e8f4fd',
                fontSize: '1rem',
                fontWeight: currentLanguage === key ? '600' : '400',
                cursor: isTranslating ? 'not-allowed' : 'pointer',
                display: 'flex',
                alignItems: 'center',
                gap: '12px',
                transition: 'all 0.3s ease',
                fontFamily: 'Noto Sans Sinhala, sans-serif',
                opacity: isTranslating ? 0.7 : 1,
                borderBottom: '1px solid rgba(244, 208, 63, 0.1)'
              }}
              onMouseEnter={(e) => {
                if (!isTranslating && currentLanguage !== key) {
                  e.target.style.background = 'rgba(244, 208, 63, 0.1)';
                  e.target.style.color = '#f4d03f';
                }
              }}
              onMouseLeave={(e) => {
                if (!isTranslating && currentLanguage !== key) {
                  e.target.style.background = 'transparent';
                  e.target.style.color = '#e8f4fd';
                }
              }}
            >
              <span style={{ fontSize: '1.2rem' }}>{lang.flag}</span>
              <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-start' }}>
                <span>{lang.nativeName}</span>
                <span style={{ 
                  fontSize: '0.8rem', 
                  opacity: 0.7,
                  color: currentLanguage === key ? '#f4d03f' : '#a0a0a0'
                }}>
                  {lang.name}
                </span>
              </div>
              {currentLanguage === key && (
                <span style={{ 
                  marginLeft: 'auto', 
                  fontSize: '1rem',
                  color: '#f4d03f'
                }}>
                  ✓
                </span>
              )}
            </button>
          ))}
        </div>
      )}

      {/* CSS Animations and Mobile Responsive Styles */}
      <style jsx>{`
        @keyframes spin {
          0% { transform: rotate(0deg); }
          100% { transform: rotate(360deg); }
        }

        @keyframes fadeInDown {
          0% {
            opacity: 0;
            transform: translateY(-10px);
          }
          100% {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .language-selector button:last-child {
          border-bottom: none !important;
        }

        /* Mobile Responsive Styles */
        @media (max-width: 768px) {
          .language-selector {
            width: 100%;
            max-width: 180px;
          }

          .language-selector-button {
            padding: 10px 16px !important;
            font-size: 0.9rem !important;
            min-width: 120px !important;
            gap: 8px !important;
          }

          .language-dropdown {
            min-width: 180px !important;
            max-width: 200px !important;
            left: 0 !important;
            right: auto !important;
          }
        }

        @media (max-width: 480px) {
          .language-selector {
            max-width: 160px;
          }

          .language-selector-button {
            padding: 8px 12px !important;
            font-size: 0.85rem !important;
            min-width: 100px !important;
            gap: 6px !important;
            border-radius: 12px !important;
          }

          .language-dropdown {
            min-width: 160px !important;
            max-width: 180px !important;
            border-radius: 12px !important;
          }

          .language-dropdown button {
            padding: 10px 16px !important;
            font-size: 0.9rem !important;
          }
        }

        @media (max-width: 360px) {
          .language-selector {
            max-width: 140px;
          }

          .language-selector-button {
            padding: 6px 10px !important;
            font-size: 0.8rem !important;
            min-width: 90px !important;
            gap: 4px !important;
          }

          .language-dropdown {
            min-width: 140px !important;
            max-width: 160px !important;
          }

          .language-dropdown button {
            padding: 8px 12px !important;
            font-size: 0.85rem !important;
          }
        }

        /* Touch-friendly improvements for mobile */
        @media (hover: none) and (pointer: coarse) {
          .language-selector-button {
            min-height: 44px;
          }

          .language-dropdown button {
            min-height: 44px;
            padding: 12px 16px !important;
          }
        }
      `}</style>
    </div>
  );
};

export default LanguageSelector;
