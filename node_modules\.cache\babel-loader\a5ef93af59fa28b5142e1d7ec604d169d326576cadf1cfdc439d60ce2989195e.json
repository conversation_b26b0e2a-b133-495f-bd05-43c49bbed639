{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState,useEffect,useCallback,useRef}from'react';import{Link}from'react-router-dom';import ParticleBackground from'./ParticleBackground';import SmokeAnimation from'./SmokeAnimation';import KuberaAnimation from'./KuberaAnimation';import LanguageSelector from'./LanguageSelector';import TranslationLoader from'./TranslationLoader';import HoroscopeService from'../services/HoroscopeService';import{useAnalytics,useComponentTracking}from'../hooks/useAnalytics';import{useTranslatedContent,useUITranslations}from'../hooks/useTranslatedContent';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const zodiacIcons={aries:'♈',taurus:'♉',gemini:'♊',cancer:'♋',leo:'♌',virgo:'♍',libra:'♎',scorpio:'♏',sagittarius:'♐',capricorn:'♑',aquarius:'♒',pisces:'♓'};// Advanced horoscope parser that extracts structured content\nconst parseHoroscopeIntoStructuredCategories=rawText=>{// Check if rawText is valid\nif(!rawText||typeof rawText!=='string'){return[];}// Clean the raw text first\nconst cleanText=rawText.replace(/\\*\\*/g,'').replace(/##/g,'').replace(/\\*/g,'').replace(/\\[.*?\\]/g,'').trim();const categories={love:{id:'love',title:'ආදරය සහ සම්බන්ධතා',emoji:'💕',icon:'❤️',content:'',keywords:['ආදර','සම්බන්ධතා','ප්‍රේම','විවාහ','මිත්‍ර']},career:{id:'career',title:'වෘත්තීය ජීවිතය',emoji:'💼',icon:'🏢',content:'',keywords:['වෘත්ති','කාර්','රැකියා','ව්‍යාපාර','සේවා']},health:{id:'health',title:'සෞඛ්‍ය සහ යහපැවැත්ම',emoji:'🌿',icon:'🏥',content:'',keywords:['සෞඛ්','සෞඛ','යහපැවැත්ම','ශරීර','මානසික']},finance:{id:'finance',title:'මූල්‍ය කටයුතු',emoji:'💰',icon:'💳',content:'',keywords:['මූල්','මුදල්','ආර්ථික','ආදායම','වියදම']},general:{id:'general',title:'සාමාන්‍ය උපදෙස්',emoji:'✨',icon:'🔮',content:'',keywords:['සාමාන්','උපදෙස්','සාර්ථක','ජීවිත','දිනය']}};// Split text into lines and process\nconst lines=cleanText.split('\\n').filter(line=>line.trim().length>0);let currentCategory=null;let contentBuffer=[];// If no clear structure, distribute content evenly across categories\nif(lines.length<5){// Short content - put everything in general\ncategories.general.content=cleanText;}else{// Process each line to categorize content\nfor(let i=0;i<lines.length;i++){const line=lines[i].trim();if(!line||line.length<2)continue;// Detect category by keywords or numbered sections\nlet detectedCategory=null;// Check for numbered sections (1., 2., 3., etc.)\nconst numberedMatch=line.match(/^(\\d+)\\./);if(numberedMatch){const num=parseInt(numberedMatch[1]);const categoryOrder=['love','career','health','finance','general'];if(num>=1&&num<=5){detectedCategory=categoryOrder[num-1];}}// Check for keyword-based detection (more flexible)\nif(!detectedCategory){for(const[catId,catData]of Object.entries(categories)){for(const keyword of catData.keywords){if(line.toLowerCase().includes(keyword.toLowerCase())){detectedCategory=catId;break;}}if(detectedCategory)break;}}// If we found a new category, save previous content\nif(detectedCategory&&detectedCategory!==currentCategory){if(currentCategory&&contentBuffer.length>0){categories[currentCategory].content=contentBuffer.join(' ').trim();}currentCategory=detectedCategory;contentBuffer=[];// Clean the line and add to buffer\nlet cleanContent=line.replace(/^\\d+\\.\\s*/,'').replace(/^[•-]\\s*/,'').replace(new RegExp(categories[detectedCategory].title,'gi'),'').replace(/:/g,'').trim();if(cleanContent.length>0){contentBuffer.push(cleanContent);}}else if(currentCategory){// Add content to current category\nlet cleanContent=line.trim();if(cleanContent.length>0){contentBuffer.push(cleanContent);}}else{// No category detected yet, start with general and add content\ncurrentCategory='general';contentBuffer.push(line.trim());}}// If no categories were detected, distribute content intelligently\nif(!Object.values(categories).some(cat=>cat.content)){const sentences=cleanText.split(/[.!?]/).filter(s=>s.trim().length>10);const categoriesArray=Object.keys(categories);sentences.forEach((sentence,index)=>{const categoryIndex=index%categoriesArray.length;const categoryKey=categoriesArray[categoryIndex];if(!categories[categoryKey].content){categories[categoryKey].content=sentence.trim();}else{categories[categoryKey].content+=' '+sentence.trim();}});}}// Save final category content\nif(currentCategory&&contentBuffer.length>0){categories[currentCategory].content=contentBuffer.join(' ').trim();}// Ensure all categories have meaningful content\nObject.values(categories).forEach((category,index)=>{if(!category.content||category.content.length<5){// If still no content, use a portion of the original text\nconst sentences=cleanText.split(/[.!?]/).filter(s=>s.trim().length>5);if(sentences.length>index){category.content=sentences[index].trim()||cleanText.substring(index*50,(index+1)*50).trim();}else{// Last resort - use generic content based on category\nconst genericContent={love:'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',health:'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',finance:'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',general:'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'};category.content=genericContent[category.id]||'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';}}});// Ensure each category has its id properly set and return as array\nreturn Object.entries(categories).map(_ref=>{let[key,category]=_ref;return _objectSpread(_objectSpread({},category),{},{id:key// Ensure id is properly set\n});});};// Beautiful category card component\nconst CategoryCard=_ref2=>{let{category,index,translatedTitle,translatedContent}=_ref2;const cardStyles={love:{background:'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',border:'1px solid rgba(255, 182, 193, 0.3)',shadow:'0 8px 32px rgba(255, 105, 180, 0.1)'},career:{background:'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',border:'1px solid rgba(70, 130, 180, 0.3)',shadow:'0 8px 32px rgba(30, 144, 255, 0.1)'},health:{background:'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',border:'1px solid rgba(144, 238, 144, 0.3)',shadow:'0 8px 32px rgba(50, 205, 50, 0.1)'},finance:{background:'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',border:'1px solid rgba(255, 215, 0, 0.3)',shadow:'0 8px 32px rgba(255, 165, 0, 0.1)'},general:{background:'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',border:'1px solid rgba(221, 160, 221, 0.3)',shadow:'0 8px 32px rgba(147, 112, 219, 0.1)'}};const style=cardStyles[category.id]||cardStyles.general;return/*#__PURE__*/_jsxs(\"div\",{className:\"horoscope-category-card\",style:{marginBottom:'2rem',padding:'2rem',background:style.background,border:style.border,borderRadius:'20px',boxShadow:style.shadow,backdropFilter:'blur(10px)',transition:'all 0.3s ease',position:'relative',overflow:'hidden'},onMouseEnter:e=>{e.currentTarget.style.transform='translateY(-5px)';e.currentTarget.style.boxShadow=style.shadow.replace('0.1)','0.2)');},onMouseLeave:e=>{e.currentTarget.style.transform='translateY(0)';e.currentTarget.style.boxShadow=style.shadow;},children:[/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:'-50%',right:'-50%',width:'200%',height:'200%',background:\"radial-gradient(circle, \".concat(style.border.replace('1px solid ','').replace('0.3)','0.05)'),\" 1px, transparent 1px)\"),backgroundSize:'20px 20px',opacity:0.3,pointerEvents:'none'}}),/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',marginBottom:'1.5rem',position:'relative',zIndex:1},children:[/*#__PURE__*/_jsx(\"div\",{style:{fontSize:'2.5rem',marginRight:'1rem',filter:'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'},children:category.emoji}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{style:{color:'#f4d03f',fontSize:'1.4rem',margin:0,fontFamily:'Noto Sans Sinhala, sans-serif',fontWeight:'600',textShadow:'0 2px 4px rgba(0,0,0,0.1)'},children:translatedTitle||category.title}),/*#__PURE__*/_jsx(\"div\",{style:{width:'50px',height:'3px',background:'linear-gradient(90deg, #f4d03f, transparent)',marginTop:'0.5rem',borderRadius:'2px'}})]})]}),/*#__PURE__*/_jsx(\"div\",{style:{position:'relative',zIndex:1},children:/*#__PURE__*/_jsx(\"p\",{style:{color:'#e8f4fd',lineHeight:'1.8',fontSize:'1.1rem',margin:0,fontFamily:'Noto Sans Sinhala, sans-serif',textAlign:'justify',textShadow:'0 1px 2px rgba(0,0,0,0.1)'},children:translatedContent||category.content})}),/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',bottom:0,left:0,right:0,height:'4px',background:\"linear-gradient(90deg, \".concat(style.border.replace('1px solid ','').replace('0.3)','0.6)'),\", transparent)\"),borderRadius:'0 0 20px 20px'}})]});};// Main display component\nconst StructuredHoroscopeDisplay=_ref3=>{let{horoscope}=_ref3;const{getTranslatedCategories,isLoading:translationLoading}=useTranslatedContent();const[translatedCategories,setTranslatedCategories]=useState(null);const{getUIText}=useUITranslations();let categories;// Translate categories when horoscope or language changes\nuseEffect(()=>{const translateCategories=async()=>{if(categories&&categories.length>0){const translated=await getTranslatedCategories(categories);setTranslatedCategories(translated);}};translateCategories();},[horoscope,getTranslatedCategories]);// Check if we have structured data from the new API\nif(horoscope&&horoscope.structured&&horoscope.categories){// Handle both object and array formats\nif(Array.isArray(horoscope.categories)){categories=horoscope.categories;}else{// Convert object to array with proper ids\ncategories=Object.entries(horoscope.categories).map(_ref4=>{let[key,category]=_ref4;return _objectSpread(_objectSpread({},category),{},{id:category.id||key});});}}else if(typeof horoscope==='string'){// Fallback to parsing raw text\ncategories=parseHoroscopeIntoStructuredCategories(horoscope);}else{categories=[];}// Fallback if no categories found\nif(!categories||categories.length===0){return/*#__PURE__*/_jsx(\"div\",{style:{textAlign:'center',padding:'2rem',color:'#e8f4fd',fontFamily:'Noto Sans Sinhala, sans-serif'},children:getUIText('loading')});}return/*#__PURE__*/_jsx(\"div\",{className:\"structured-horoscope-display\",style:{maxWidth:'800px',margin:'0 auto',padding:'1rem'},children:(translatedCategories||categories).map((category,index)=>{const originalCategory=categories[index];return/*#__PURE__*/_jsx(CategoryCard,{category:originalCategory,index:index,translatedTitle:category.title,translatedContent:category.content},category.id||\"category-\".concat(index));})});};const ZodiacPage=_ref5=>{let{sign}=_ref5;const[horoscope,setHoroscope]=useState('');const[loading,setLoading]=useState(true);const[error,setError]=useState('');const[soundEnabled,setSoundEnabled]=useState(true);// Default to true (on)\nconst[lastUpdated,setLastUpdated]=useState(null);const{getTranslatedText}=useTranslatedContent();const{getUIText}=useUITranslations();const[translatedSignName,setTranslatedSignName]=useState('');// Analytics integration\nconst analytics=useAnalytics();useComponentTracking('ZodiacPage');const[refreshing,setRefreshing]=useState(false);const[userInteracted,setUserInteracted]=useState(false);const[showAudioPrompt,setShowAudioPrompt]=useState(false);const audioRef=useRef(null);const fetchHoroscope=useCallback(async function(){let forceRefresh=arguments.length>0&&arguments[0]!==undefined?arguments[0]:false;try{// Track zodiac page view\nanalytics.trackZodiacView(sign.id);if(forceRefresh){setRefreshing(true);analytics.trackEvent('horoscope_refresh',{event_category:'user_action',zodiac_sign:sign.id});}else{setLoading(true);}setError('');// Check cache first (skip cache if force refresh)\nif(!forceRefresh){const cachedHoroscope=HoroscopeService.getCachedHoroscope(sign.id);if(cachedHoroscope){setHoroscope(cachedHoroscope);setLastUpdated(new Date());setLoading(false);return;}}// Fetch structured horoscope data from the new API\nconst horoscopeData=await HoroscopeService.getHoroscope(sign.id,forceRefresh);// Check if we got structured data\nif(horoscopeData&&horoscopeData.categories){// Convert API response to the format expected by the display component\nconst categoryConfig={love:{id:'love',title:'ආදරය සහ සම්බන්ධතා',emoji:'💕'},career:{id:'career',title:'වෘත්තීය ජීවිතය',emoji:'💼'},health:{id:'health',title:'සෞඛ්‍ය සහ යහපැවැත්ම',emoji:'🌿'},finance:{id:'finance',title:'මූල්‍ය කටයුතු',emoji:'💰'},general:{id:'general',title:'සාමාන්‍ය උපදෙස්',emoji:'✨'}};const categories=Object.entries(horoscopeData.categories).map(_ref6=>{let[key,content]=_ref6;return _objectSpread(_objectSpread({},categoryConfig[key]),{},{content:content||'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'});});setHoroscope({categories,structured:true,dateCreated:horoscopeData.date_created,createdAt:horoscopeData.created_at,rawContent:horoscopeData.raw_content});}else{// Fallback to old parsing method if we get raw text\nsetHoroscope(horoscopeData);}setLastUpdated(new Date());// Track successful horoscope load\nanalytics.trackEvent('horoscope_loaded',{event_category:'content_interaction',zodiac_sign:sign.id,load_type:forceRefresh?'refresh':'initial',content_length:typeof horoscopeData==='string'?horoscopeData.length:JSON.stringify(horoscopeData).length});// Cache the result\nHoroscopeService.setCachedHoroscope(sign.id,horoscopeData);}catch(err){setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');console.error('Error fetching horoscope:',err);// Track error\nanalytics.trackError(\"Horoscope fetch error: \".concat(err.message),\"ZodiacPage-\".concat(sign.id),false);}finally{setLoading(false);setRefreshing(false);}},[sign.id]);useEffect(()=>{fetchHoroscope();},[fetchHoroscope]);// Immediate audio play attempt on component mount\nuseEffect(()=>{const timer=setTimeout(()=>{if(audioRef.current&&soundEnabled&&!userInteracted){audioRef.current.loop=true;audioRef.current.volume=0.3;// Multiple attempts to play\nconst attemptPlay=()=>{const playPromise=audioRef.current.play();if(playPromise!==undefined){playPromise.then(()=>{setUserInteracted(true);setShowAudioPrompt(false);}).catch(()=>{// If first attempt fails, show prompt\nsetShowAudioPrompt(true);});}};attemptPlay();}},500);// Small delay to ensure component is fully mounted\nreturn()=>clearTimeout(timer);},[soundEnabled,userInteracted]);// Include dependencies\n// Auto-play background music when component mounts or user interacts\nuseEffect(()=>{if(audioRef.current&&soundEnabled){// Set audio properties\naudioRef.current.loop=true;audioRef.current.volume=0.3;// Set volume to 30%\n// Try to play the audio\nconst playPromise=audioRef.current.play();if(playPromise!==undefined){playPromise.then(()=>{setUserInteracted(true);setShowAudioPrompt(false);}).catch(error=>{// Show prompt to encourage user interaction\nsetShowAudioPrompt(true);});}}},[soundEnabled,userInteracted]);// Add user interaction listeners to enable audio\nuseEffect(()=>{const handleUserInteraction=()=>{if(!userInteracted&&audioRef.current&&soundEnabled){const playPromise=audioRef.current.play();if(playPromise!==undefined){playPromise.then(()=>{setUserInteracted(true);setShowAudioPrompt(false);}).catch(console.error);}}};// Add event listeners for user interaction\nconst events=['click','touchstart','keydown','scroll'];events.forEach(event=>{document.addEventListener(event,handleUserInteraction,{once:true});});// Cleanup\nreturn()=>{events.forEach(event=>{document.removeEventListener(event,handleUserInteraction);});};},[soundEnabled,userInteracted]);// Translate zodiac sign name when language changes\nuseEffect(()=>{const translateSignName=async()=>{const translated=await getTranslatedText(sign.sinhala,'zodiac_sign_name');setTranslatedSignName(translated);};translateSignName();},[sign.sinhala,getTranslatedText]);const handleRefresh=()=>{fetchHoroscope(true);};const toggleSound=()=>{const newSoundState=!soundEnabled;setSoundEnabled(newSoundState);// Track sound toggle\nanalytics.trackEvent('sound_toggle',{event_category:'user_preference',zodiac_sign:sign.id,sound_enabled:newSoundState,action:newSoundState?'enable':'disable'});if(audioRef.current){if(newSoundState){audioRef.current.play().then(()=>{setUserInteracted(true);setShowAudioPrompt(false);}).catch(console.error);}else{audioRef.current.pause();}}};const getCurrentDate=()=>{const today=new Date();const options={year:'numeric',month:'long',day:'numeric',weekday:'long'};return today.toLocaleDateString('si-LK',options);};return/*#__PURE__*/_jsx(TranslationLoader,{children:/*#__PURE__*/_jsxs(\"div\",{className:\"zodiac-page\",children:[/*#__PURE__*/_jsx(\"audio\",{ref:audioRef,src:\"/music.mp3?v=\".concat(window.CACHE_VERSION||Date.now()),loop:true,style:{display:'none'}}),/*#__PURE__*/_jsx(\"div\",{className:\"divine-background\",children:/*#__PURE__*/_jsx(\"img\",{src:\"/god.jpg?v=\".concat(window.CACHE_VERSION||Date.now()),alt:\"Divine Blessing\",className:\"god-image\"})}),/*#__PURE__*/_jsx(ParticleBackground,{}),/*#__PURE__*/_jsx(SmokeAnimation,{}),/*#__PURE__*/_jsx(KuberaAnimation,{}),/*#__PURE__*/_jsx(Link,{to:\"/\",className:\"back-button\",children:getUIText('backToHome')}),/*#__PURE__*/_jsx(\"div\",{className:\"language-selector-container\",style:{position:'fixed',top:'20px',right:'20px',zIndex:1001},children:/*#__PURE__*/_jsx(LanguageSelector,{})}),/*#__PURE__*/_jsxs(\"div\",{className:\"zodiac-content\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"zodiac-header\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"zodiac-icon\",style:{fontSize:'5rem',marginBottom:'1rem'},children:zodiacIcons[sign.id]}),/*#__PURE__*/_jsx(\"h1\",{className:\"zodiac-title\",children:translatedSignName||sign.sinhala}),/*#__PURE__*/_jsxs(\"h2\",{className:\"zodiac-subtitle\",children:[sign.english,\" \",getUIText('zodiacSign')]}),horoscope&&horoscope.dateCreated?/*#__PURE__*/_jsxs(\"div\",{style:{background:'rgba(244, 208, 63, 0.15)',border:'1px solid rgba(244, 208, 63, 0.3)',borderRadius:'15px',padding:'1rem',marginBottom:'2rem',textAlign:'center'},children:[/*#__PURE__*/_jsx(\"div\",{style:{color:'#f4d03f',fontSize:'1.1rem',fontWeight:'bold',marginBottom:'0.5rem'},children:getUIText('horoscopeDate')}),/*#__PURE__*/_jsx(\"div\",{style:{color:'#ffffff',fontSize:'1rem'},children:new Date(horoscope.dateCreated).toLocaleDateString('si-LK',{year:'numeric',month:'long',day:'numeric',weekday:'long'})}),horoscope.createdAt&&/*#__PURE__*/_jsxs(\"div\",{style:{color:'#aeb6bf',fontSize:'0.85rem',marginTop:'0.5rem'},children:[\"\\u0DA2\\u0DB1\\u0DB1\\u0DBA \\u0D9A\\u0DC5\\u0DDA: \",new Date(horoscope.createdAt).toLocaleString('si-LK')]})]}):/*#__PURE__*/_jsx(\"p\",{style:{color:'#aeb6bf',marginBottom:'2rem'},children:getCurrentDate()})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"horoscope-section\",children:[/*#__PURE__*/_jsx(\"div\",{style:{marginBottom:'1.5rem'},children:/*#__PURE__*/_jsx(\"h3\",{className:\"horoscope-title\",style:{margin:0},children:getUIText('todaysHoroscope')})}),lastUpdated&&/*#__PURE__*/_jsxs(\"div\",{style:{fontSize:'0.85rem',color:'#aeb6bf',marginBottom:'1rem',textAlign:'center',fontStyle:'italic'},children:[getUIText('lastUpdated'),\": \",lastUpdated.toLocaleTimeString('si-LK',{hour:'2-digit',minute:'2-digit',hour12:true})]}),loading&&/*#__PURE__*/_jsx(\"div\",{className:\"loading\",children:getUIText('loading')}),refreshing&&/*#__PURE__*/_jsx(\"div\",{className:\"loading\",children:getUIText('refreshing')}),error&&/*#__PURE__*/_jsxs(\"div\",{className:\"error\",children:[error,/*#__PURE__*/_jsx(\"button\",{onClick:handleRefresh,style:{marginLeft:'1rem',background:'rgba(231, 76, 60, 0.1)',border:'1px solid #e74c3c',color:'#e74c3c',padding:'0.4rem 0.8rem',borderRadius:'15px',cursor:'pointer',fontSize:'0.8rem'},children:getUIText('refreshHoroscope')})]}),!loading&&!refreshing&&!error&&horoscope&&/*#__PURE__*/_jsx(StructuredHoroscopeDisplay,{horoscope:horoscope})]}),showAudioPrompt&&soundEnabled&&/*#__PURE__*/_jsx(\"div\",{style:{position:'fixed',top:'20px',right:'20px',background:'rgba(244, 208, 63, 0.95)',color:'#1a1a2e',padding:'1rem 1.5rem',borderRadius:'15px',border:'2px solid #f4d03f',boxShadow:'0 8px 32px rgba(0, 0, 0, 0.3)',zIndex:1000,animation:'pulse 2s infinite',cursor:'pointer',fontFamily:'Noto Sans Sinhala, sans-serif',fontWeight:'bold',fontSize:'0.9rem',maxWidth:'250px',textAlign:'center'},onClick:()=>{if(audioRef.current){audioRef.current.play().then(()=>{setUserInteracted(true);setShowAudioPrompt(false);}).catch(console.error);}},children:\"\\uD83C\\uDFB5 \\u0DC1\\u0DB6\\u0DCA\\u0DAF \\u0DC3\\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DD2\\u0DBA \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DA7 \\u0D9A\\u0DCA\\u0DBD\\u0DD2\\u0D9A\\u0DCA \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\"}),/*#__PURE__*/_jsx(\"div\",{className:\"controls\",style:{marginTop:'2rem',display:'flex',gap:'1rem',flexWrap:'wrap',justifyContent:'center'},children:/*#__PURE__*/_jsx(\"button\",{onClick:toggleSound,className:\"sound-toggle\",style:{background:soundEnabled?'rgba(244, 208, 63, 0.2)':'rgba(255, 255, 255, 0.1)',border:'1px solid #f4d03f',color:'#f4d03f',padding:'0.8rem 1.5rem',borderRadius:'25px',cursor:'pointer',fontFamily:'Noto Sans Sinhala, sans-serif',transition:'all 0.3s ease'},children:soundEnabled?getUIText('soundOn'):getUIText('soundOff')})}),/*#__PURE__*/_jsx(\"div\",{className:\"spiritual-message\",style:{marginTop:'3rem',padding:'2rem',background:'rgba(244, 208, 63, 0.1)',borderRadius:'15px',border:'1px solid rgba(244, 208, 63, 0.3)',textAlign:'center'},children:/*#__PURE__*/_jsx(\"p\",{style:{color:'#f4d03f',fontStyle:'italic',fontSize:'1.1rem'},children:getUIText('spiritualMessage')})})]})]})});};export default ZodiacPage;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useCallback", "useRef", "Link", "ParticleBackground", "SmokeAnimation", "KuberaAnimation", "LanguageSelector", "Translation<PERSON><PERSON>der", "HoroscopeService", "useAnalytics", "useComponentTracking", "useTranslatedContent", "useUITranslations", "jsx", "_jsx", "jsxs", "_jsxs", "zodiacIcons", "aries", "taurus", "gemini", "cancer", "leo", "virgo", "libra", "scorpio", "sagittarius", "capricorn", "aquarius", "pisces", "parseHoroscopeIntoStructuredCategories", "rawText", "cleanText", "replace", "trim", "categories", "love", "id", "title", "emoji", "icon", "content", "keywords", "career", "health", "finance", "general", "lines", "split", "filter", "line", "length", "currentCategory", "contentBuffer", "i", "detectedCategory", "numberedMatch", "match", "num", "parseInt", "categoryOrder", "catId", "catData", "Object", "entries", "keyword", "toLowerCase", "includes", "join", "cleanContent", "RegExp", "push", "values", "some", "cat", "sentences", "s", "categoriesArray", "keys", "for<PERSON>ach", "sentence", "index", "categoryIndex", "categoryKey", "category", "substring", "genericContent", "map", "_ref", "key", "_objectSpread", "CategoryCard", "_ref2", "translatedTitle", "<PERSON><PERSON><PERSON><PERSON>", "cardStyles", "background", "border", "shadow", "style", "className", "marginBottom", "padding", "borderRadius", "boxShadow", "<PERSON><PERSON>ilter", "transition", "position", "overflow", "onMouseEnter", "e", "currentTarget", "transform", "onMouseLeave", "children", "top", "right", "width", "height", "concat", "backgroundSize", "opacity", "pointerEvents", "display", "alignItems", "zIndex", "fontSize", "marginRight", "color", "margin", "fontFamily", "fontWeight", "textShadow", "marginTop", "lineHeight", "textAlign", "bottom", "left", "StructuredHoroscopeDisplay", "_ref3", "horoscope", "getTranslatedCategories", "isLoading", "translationLoading", "translatedCategories", "setTranslatedCategories", "getUIText", "translateCategories", "translated", "structured", "Array", "isArray", "_ref4", "max<PERSON><PERSON><PERSON>", "originalCategory", "ZodiacPage", "_ref5", "sign", "setHoroscope", "loading", "setLoading", "error", "setError", "soundEnabled", "setSoundEnabled", "lastUpdated", "setLastUpdated", "getTranslatedText", "translatedSignName", "setTranslatedSignName", "analytics", "refreshing", "setRefreshing", "userInteracted", "setUserInteracted", "showAudioPrompt", "setShowAudioPrompt", "audioRef", "fetchHoroscope", "forceRefresh", "arguments", "undefined", "trackZodiacView", "trackEvent", "event_category", "zodiac_sign", "cachedHoroscope", "getCachedHoroscope", "Date", "horoscopeData", "getHoroscope", "categoryConfig", "_ref6", "dateCreated", "date_created", "createdAt", "created_at", "rawContent", "raw_content", "load_type", "content_length", "JSON", "stringify", "setCachedHoroscope", "err", "console", "trackError", "message", "timer", "setTimeout", "current", "loop", "volume", "attemptPlay", "playPromise", "play", "then", "catch", "clearTimeout", "handleUserInteraction", "events", "event", "document", "addEventListener", "once", "removeEventListener", "translateSignName", "sinhala", "handleRefresh", "toggleSound", "newSoundState", "sound_enabled", "action", "pause", "getCurrentDate", "today", "options", "year", "month", "day", "weekday", "toLocaleDateString", "ref", "src", "window", "CACHE_VERSION", "now", "alt", "to", "english", "toLocaleString", "fontStyle", "toLocaleTimeString", "hour", "minute", "hour12", "onClick", "marginLeft", "cursor", "animation", "gap", "flexWrap", "justifyContent"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/ZodiacPage.js"], "sourcesContent": ["import React, { useState, useEffect, useCallback, useRef } from 'react';\nimport { Link } from 'react-router-dom';\nimport ParticleBackground from './ParticleBackground';\nimport SmokeAnimation from './SmokeAnimation';\nimport KuberaAnimation from './KuberaAnimation';\nimport LanguageSelector from './LanguageSelector';\nimport TranslationLoader from './TranslationLoader';\nimport HoroscopeService from '../services/HoroscopeService';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\nimport { useTranslatedContent, useUITranslations } from '../hooks/useTranslatedContent';\n\n\nconst zodiacIcons = {\n  aries: '♈',\n  taurus: '♉',\n  gemini: '♊',\n  cancer: '♋',\n  leo: '♌',\n  virgo: '♍',\n  libra: '♎',\n  scorpio: '♏',\n  sagittarius: '♐',\n  capricorn: '♑',\n  aquarius: '♒',\n  pisces: '♓'\n};\n\n// Advanced horoscope parser that extracts structured content\nconst parseHoroscopeIntoStructuredCategories = (rawText) => {\n  // Check if rawText is valid\n  if (!rawText || typeof rawText !== 'string') {\n    return [];\n  }\n  \n  // Clean the raw text first\n  const cleanText = rawText\n    .replace(/\\*\\*/g, '')\n    .replace(/##/g, '')\n    .replace(/\\*/g, '')\n    .replace(/\\[.*?\\]/g, '')\n    .trim();\n\n  const categories = {\n    love: {\n      id: 'love',\n      title: 'ආදරය සහ සම්බන්ධතා',\n      emoji: '💕',\n      icon: '❤️',\n      content: '',\n      keywords: ['ආදර', 'සම්බන්ධතා', 'ප්‍රේම', 'විවාහ', 'මිත්‍ර']\n    },\n    career: {\n      id: 'career',\n      title: 'වෘත්තීය ජීවිතය',\n      emoji: '💼',\n      icon: '🏢',\n      content: '',\n      keywords: ['වෘත්ති', 'කාර්', 'රැකියා', 'ව්‍යාපාර', 'සේවා']\n    },\n    health: {\n      id: 'health',\n      title: 'සෞඛ්‍ය සහ යහපැවැත්ම',\n      emoji: '🌿',\n      icon: '🏥',\n      content: '',\n      keywords: ['සෞඛ්', 'සෞඛ', 'යහපැවැත්ම', 'ශරීර', 'මානසික']\n    },\n    finance: {\n      id: 'finance',\n      title: 'මූල්‍ය කටයුතු',\n      emoji: '💰',\n      icon: '💳',\n      content: '',\n      keywords: ['මූල්', 'මුදල්', 'ආර්ථික', 'ආදායම', 'වියදම']\n    },\n    general: {\n      id: 'general',\n      title: 'සාමාන්‍ය උපදෙස්',\n      emoji: '✨',\n      icon: '🔮',\n      content: '',\n      keywords: ['සාමාන්', 'උපදෙස්', 'සාර්ථක', 'ජීවිත', 'දිනය']\n    }\n  };\n\n  // Split text into lines and process\n  const lines = cleanText.split('\\n').filter(line => line.trim().length > 0);\n  let currentCategory = null;\n  let contentBuffer = [];\n  \n  // If no clear structure, distribute content evenly across categories\n  if (lines.length < 5) {\n    // Short content - put everything in general\n    categories.general.content = cleanText;\n  } else {\n    // Process each line to categorize content\n    for (let i = 0; i < lines.length; i++) {\n      const line = lines[i].trim();\n      \n      if (!line || line.length < 2) continue;\n      \n      // Detect category by keywords or numbered sections\n      let detectedCategory = null;\n      \n      // Check for numbered sections (1., 2., 3., etc.)\n      const numberedMatch = line.match(/^(\\d+)\\./); \n      if (numberedMatch) {\n        const num = parseInt(numberedMatch[1]);\n        const categoryOrder = ['love', 'career', 'health', 'finance', 'general'];\n        if (num >= 1 && num <= 5) {\n          detectedCategory = categoryOrder[num - 1];\n        }\n      }\n      \n      // Check for keyword-based detection (more flexible)\n      if (!detectedCategory) {\n        for (const [catId, catData] of Object.entries(categories)) {\n          for (const keyword of catData.keywords) {\n            if (line.toLowerCase().includes(keyword.toLowerCase())) {\n              detectedCategory = catId;\n              break;\n            }\n          }\n          if (detectedCategory) break;\n        }\n      }\n      \n      // If we found a new category, save previous content\n      if (detectedCategory && detectedCategory !== currentCategory) {\n        if (currentCategory && contentBuffer.length > 0) {\n          categories[currentCategory].content = contentBuffer.join(' ').trim();\n        }\n        currentCategory = detectedCategory;\n        contentBuffer = [];\n        \n        // Clean the line and add to buffer\n        let cleanContent = line\n          .replace(/^\\d+\\.\\s*/, '')\n          .replace(/^[•-]\\s*/, '')\n          .replace(new RegExp(categories[detectedCategory].title, 'gi'), '')\n          .replace(/:/g, '')\n          .trim();\n        \n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else if (currentCategory) {\n        // Add content to current category\n        let cleanContent = line.trim();\n        if (cleanContent.length > 0) {\n          contentBuffer.push(cleanContent);\n        }\n      } else {\n        // No category detected yet, start with general and add content\n        currentCategory = 'general';\n        contentBuffer.push(line.trim());\n       }\n    }\n    \n    // If no categories were detected, distribute content intelligently\n    if (!Object.values(categories).some(cat => cat.content)) {\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 10);\n      const categoriesArray = Object.keys(categories);\n      \n      sentences.forEach((sentence, index) => {\n        const categoryIndex = index % categoriesArray.length;\n        const categoryKey = categoriesArray[categoryIndex];\n        if (!categories[categoryKey].content) {\n          categories[categoryKey].content = sentence.trim();\n        } else {\n          categories[categoryKey].content += ' ' + sentence.trim();\n        }\n      });\n    }\n   }\n   \n   // Save final category content\n   if (currentCategory && contentBuffer.length > 0) {\n     categories[currentCategory].content = contentBuffer.join(' ').trim();\n   }\n   \n   // Ensure all categories have meaningful content\n  Object.values(categories).forEach((category, index) => {\n    if (!category.content || category.content.length < 5) {\n      // If still no content, use a portion of the original text\n      const sentences = cleanText.split(/[.!?]/).filter(s => s.trim().length > 5);\n      if (sentences.length > index) {\n        category.content = sentences[index].trim() || cleanText.substring(index * 50, (index + 1) * 50).trim();\n      } else {\n        // Last resort - use generic content based on category\n        const genericContent = {\n          love: 'ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n          career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා සහ ප්‍රගතිය අපේක්ෂා කරන්න.',\n          health: 'සෞඛ්‍ය සහ යහපැවැත්ම සඳහා විශේෂ අවධානය යොමු කරන්න.',\n          finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න සහ ඉතිරිකිරීම් කරන්න.',\n          general: 'සාමාන්‍ය ජීවිතයේ සමතුලිතතාවය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n        };\n        category.content = genericContent[category.id] || 'ධනාත්මක වෙනස්කම් සහ සාර්ථකත්වය අපේක්ෂා කරන්න.';\n      }\n    }\n  });\n   \n   // Ensure each category has its id properly set and return as array\n   return Object.entries(categories).map(([key, category]) => ({\n     ...category,\n     id: key // Ensure id is properly set\n   }));\n };\n \n // Beautiful category card component\n const CategoryCard = ({ category, index, translatedTitle, translatedContent }) => {\n   const cardStyles = {\n     love: {\n       background: 'linear-gradient(135deg, rgba(255, 182, 193, 0.1) 0%, rgba(255, 105, 180, 0.05) 100%)',\n       border: '1px solid rgba(255, 182, 193, 0.3)',\n       shadow: '0 8px 32px rgba(255, 105, 180, 0.1)'\n     },\n     career: {\n       background: 'linear-gradient(135deg, rgba(70, 130, 180, 0.1) 0%, rgba(30, 144, 255, 0.05) 100%)',\n       border: '1px solid rgba(70, 130, 180, 0.3)',\n       shadow: '0 8px 32px rgba(30, 144, 255, 0.1)'\n     },\n     health: {\n       background: 'linear-gradient(135deg, rgba(144, 238, 144, 0.1) 0%, rgba(50, 205, 50, 0.05) 100%)',\n       border: '1px solid rgba(144, 238, 144, 0.3)',\n       shadow: '0 8px 32px rgba(50, 205, 50, 0.1)'\n     },\n     finance: {\n       background: 'linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 165, 0, 0.05) 100%)',\n       border: '1px solid rgba(255, 215, 0, 0.3)',\n       shadow: '0 8px 32px rgba(255, 165, 0, 0.1)'\n     },\n     general: {\n       background: 'linear-gradient(135deg, rgba(221, 160, 221, 0.1) 0%, rgba(147, 112, 219, 0.05) 100%)',\n       border: '1px solid rgba(221, 160, 221, 0.3)',\n       shadow: '0 8px 32px rgba(147, 112, 219, 0.1)'\n     }\n   };\n   \n   const style = cardStyles[category.id] || cardStyles.general;\n   \n   return (\n     <div \n       className=\"horoscope-category-card\"\n       style={{\n         marginBottom: '2rem',\n         padding: '2rem',\n         background: style.background,\n         border: style.border,\n         borderRadius: '20px',\n         boxShadow: style.shadow,\n         backdropFilter: 'blur(10px)',\n         transition: 'all 0.3s ease',\n         position: 'relative',\n         overflow: 'hidden'\n       }}\n       onMouseEnter={(e) => {\n         e.currentTarget.style.transform = 'translateY(-5px)';\n         e.currentTarget.style.boxShadow = style.shadow.replace('0.1)', '0.2)');\n       }}\n       onMouseLeave={(e) => {\n         e.currentTarget.style.transform = 'translateY(0)';\n         e.currentTarget.style.boxShadow = style.shadow;\n       }}\n     >\n       {/* Decorative background pattern */}\n       <div \n         style={{\n           position: 'absolute',\n           top: '-50%',\n           right: '-50%',\n           width: '200%',\n           height: '200%',\n           background: `radial-gradient(circle, ${style.border.replace('1px solid ', '').replace('0.3)', '0.05)')} 1px, transparent 1px)`,\n           backgroundSize: '20px 20px',\n           opacity: 0.3,\n           pointerEvents: 'none'\n         }}\n       />\n       \n       {/* Header */}\n       <div \n         style={{\n           display: 'flex',\n           alignItems: 'center',\n           marginBottom: '1.5rem',\n           position: 'relative',\n           zIndex: 1\n         }}\n       >\n         <div \n           style={{\n             fontSize: '2.5rem',\n             marginRight: '1rem',\n             filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))'\n           }}\n         >\n           {category.emoji}\n         </div>\n         <div>\n           <h3 \n             style={{\n               color: '#f4d03f',\n               fontSize: '1.4rem',\n               margin: 0,\n               fontFamily: 'Noto Sans Sinhala, sans-serif',\n               fontWeight: '600',\n               textShadow: '0 2px 4px rgba(0,0,0,0.1)'\n             }}\n           >\n             {translatedTitle || category.title}\n           </h3>\n           <div \n             style={{\n               width: '50px',\n               height: '3px',\n               background: 'linear-gradient(90deg, #f4d03f, transparent)',\n               marginTop: '0.5rem',\n               borderRadius: '2px'\n             }}\n           />\n         </div>\n       </div>\n       \n       {/* Content */}\n       <div \n         style={{\n           position: 'relative',\n           zIndex: 1\n         }}\n       >\n         <p \n           style={{\n             color: '#e8f4fd',\n             lineHeight: '1.8',\n             fontSize: '1.1rem',\n             margin: 0,\n             fontFamily: 'Noto Sans Sinhala, sans-serif',\n             textAlign: 'justify',\n             textShadow: '0 1px 2px rgba(0,0,0,0.1)'\n           }}\n         >\n           {translatedContent || category.content}\n         </p>\n       </div>\n       \n       {/* Bottom accent */}\n       <div \n         style={{\n           position: 'absolute',\n           bottom: 0,\n           left: 0,\n           right: 0,\n           height: '4px',\n           background: `linear-gradient(90deg, ${style.border.replace('1px solid ', '').replace('0.3)', '0.6)')}, transparent)`,\n           borderRadius: '0 0 20px 20px'\n         }}\n       />\n     </div>\n   );\n };\n \n // Main display component\nconst StructuredHoroscopeDisplay = ({ horoscope }) => {\n  const { getTranslatedCategories, isLoading: translationLoading } = useTranslatedContent();\n  const [translatedCategories, setTranslatedCategories] = useState(null);\n  const { getUIText } = useUITranslations();\n  let categories;\n\n  // Translate categories when horoscope or language changes\n  useEffect(() => {\n    const translateCategories = async () => {\n      if (categories && categories.length > 0) {\n        const translated = await getTranslatedCategories(categories);\n        setTranslatedCategories(translated);\n      }\n    };\n\n    translateCategories();\n  }, [horoscope, getTranslatedCategories]);\n\n  // Check if we have structured data from the new API\n  if (horoscope && horoscope.structured && horoscope.categories) {\n    // Handle both object and array formats\n    if (Array.isArray(horoscope.categories)) {\n      categories = horoscope.categories;\n    } else {\n      // Convert object to array with proper ids\n      categories = Object.entries(horoscope.categories).map(([key, category]) => ({\n        ...category,\n        id: category.id || key\n      }));\n    }\n  } else if (typeof horoscope === 'string') {\n    // Fallback to parsing raw text\n    categories = parseHoroscopeIntoStructuredCategories(horoscope);\n  } else {\n    categories = [];\n  }\n  \n  // Fallback if no categories found\n  if (!categories || categories.length === 0) {\n    return (\n      <div style={{\n        textAlign: 'center',\n        padding: '2rem',\n        color: '#e8f4fd',\n        fontFamily: 'Noto Sans Sinhala, sans-serif'\n      }}>\n        {getUIText('loading')}\n      </div>\n    );\n  }\n  \n  return (\n    <div \n      className=\"structured-horoscope-display\"\n      style={{\n        maxWidth: '800px',\n        margin: '0 auto',\n        padding: '1rem'\n      }}\n    >\n      {(translatedCategories || categories).map((category, index) => {\n        const originalCategory = categories[index];\n        return (\n          <CategoryCard\n            key={category.id || `category-${index}`}\n            category={originalCategory}\n            index={index}\n            translatedTitle={category.title}\n            translatedContent={category.content}\n          />\n        );\n      })}\n    </div>\n  );\n};\n\nconst ZodiacPage = ({ sign }) => {\n  const [horoscope, setHoroscope] = useState('');\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState('');\n  const [soundEnabled, setSoundEnabled] = useState(true); // Default to true (on)\n  const [lastUpdated, setLastUpdated] = useState(null);\n  const { getTranslatedText } = useTranslatedContent();\n  const { getUIText } = useUITranslations();\n  const [translatedSignName, setTranslatedSignName] = useState('');\n\n  // Analytics integration\n  const analytics = useAnalytics();\n  useComponentTracking('ZodiacPage');\n  const [refreshing, setRefreshing] = useState(false);\n  const [userInteracted, setUserInteracted] = useState(false);\n  const [showAudioPrompt, setShowAudioPrompt] = useState(false);\n  const audioRef = useRef(null);\n\n  const fetchHoroscope = useCallback(async (forceRefresh = false) => {\n    try {\n      // Track zodiac page view\n      analytics.trackZodiacView(sign.id);\n\n      if (forceRefresh) {\n        setRefreshing(true);\n        analytics.trackEvent('horoscope_refresh', {\n          event_category: 'user_action',\n          zodiac_sign: sign.id\n        });\n      } else {\n        setLoading(true);\n      }\n      setError('');\n      \n      // Check cache first (skip cache if force refresh)\n      if (!forceRefresh) {\n        const cachedHoroscope = HoroscopeService.getCachedHoroscope(sign.id);\n        if (cachedHoroscope) {\n          setHoroscope(cachedHoroscope);\n          setLastUpdated(new Date());\n          setLoading(false);\n          return;\n        }\n      }\n\n      // Fetch structured horoscope data from the new API\n      const horoscopeData = await HoroscopeService.getHoroscope(sign.id, forceRefresh);\n      \n      // Check if we got structured data\n      if (horoscopeData && horoscopeData.categories) {\n        // Convert API response to the format expected by the display component\n        const categoryConfig = {\n          love: { id: 'love', title: 'ආදරය සහ සම්බන්ධතා', emoji: '💕' },\n          career: { id: 'career', title: 'වෘත්තීය ජීවිතය', emoji: '💼' },\n          health: { id: 'health', title: 'සෞඛ්‍ය සහ යහපැවැත්ම', emoji: '🌿' },\n          finance: { id: 'finance', title: 'මූල්‍ය කටයුතු', emoji: '💰' },\n          general: { id: 'general', title: 'සාමාන්‍ය උපදෙස්', emoji: '✨' }\n        };\n        \n        const categories = Object.entries(horoscopeData.categories).map(([key, content]) => ({\n          ...categoryConfig[key],\n          content: content || 'අද දිනය සඳහා විශේෂ තොරතුරු නොමැත.'\n        }));\n        \n        setHoroscope({ \n          categories, \n          structured: true,\n          dateCreated: horoscopeData.date_created,\n          createdAt: horoscopeData.created_at,\n          rawContent: horoscopeData.raw_content\n        });\n      } else {\n        // Fallback to old parsing method if we get raw text\n        setHoroscope(horoscopeData);\n      }\n      \n      setLastUpdated(new Date());\n\n      // Track successful horoscope load\n      analytics.trackEvent('horoscope_loaded', {\n        event_category: 'content_interaction',\n        zodiac_sign: sign.id,\n        load_type: forceRefresh ? 'refresh' : 'initial',\n        content_length: typeof horoscopeData === 'string' ? horoscopeData.length : JSON.stringify(horoscopeData).length\n      });\n\n      // Cache the result\n      HoroscopeService.setCachedHoroscope(sign.id, horoscopeData);\n      \n    } catch (err) {\n      setError('රාශිඵල ලබා ගැනීමේදී දෝෂයක් ඇති විය. කරුණාකර නැවත උත්සාහ කරන්න.');\n      console.error('Error fetching horoscope:', err);\n\n      // Track error\n      analytics.trackError(\n        `Horoscope fetch error: ${err.message}`,\n        `ZodiacPage-${sign.id}`,\n        false\n      );\n    } finally {\n      setLoading(false);\n      setRefreshing(false);\n    }\n  }, [sign.id]);\n\n  useEffect(() => {\n    fetchHoroscope();\n  }, [fetchHoroscope]);\n\n  // Immediate audio play attempt on component mount\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (audioRef.current && soundEnabled && !userInteracted) {\n        audioRef.current.loop = true;\n        audioRef.current.volume = 0.3;\n\n        // Multiple attempts to play\n        const attemptPlay = () => {\n          const playPromise = audioRef.current.play();\n          if (playPromise !== undefined) {\n            playPromise.then(() => {\n              setUserInteracted(true);\n              setShowAudioPrompt(false);\n            }).catch(() => {\n              // If first attempt fails, show prompt\n              setShowAudioPrompt(true);\n            });\n          }\n        };\n\n        attemptPlay();\n      }\n    }, 500); // Small delay to ensure component is fully mounted\n\n    return () => clearTimeout(timer);\n  }, [soundEnabled, userInteracted]); // Include dependencies\n\n  // Auto-play background music when component mounts or user interacts\n  useEffect(() => {\n    if (audioRef.current && soundEnabled) {\n      // Set audio properties\n      audioRef.current.loop = true;\n      audioRef.current.volume = 0.3; // Set volume to 30%\n\n      // Try to play the audio\n      const playPromise = audioRef.current.play();\n      if (playPromise !== undefined) {\n        playPromise.then(() => {\n          setUserInteracted(true);\n          setShowAudioPrompt(false);\n        }).catch(error => {\n          // Show prompt to encourage user interaction\n          setShowAudioPrompt(true);\n        });\n      }\n    }\n  }, [soundEnabled, userInteracted]);\n\n  // Add user interaction listeners to enable audio\n  useEffect(() => {\n    const handleUserInteraction = () => {\n      if (!userInteracted && audioRef.current && soundEnabled) {\n        const playPromise = audioRef.current.play();\n        if (playPromise !== undefined) {\n          playPromise.then(() => {\n            setUserInteracted(true);\n            setShowAudioPrompt(false);\n          }).catch(console.error);\n        }\n      }\n    };\n\n    // Add event listeners for user interaction\n    const events = ['click', 'touchstart', 'keydown', 'scroll'];\n    events.forEach(event => {\n      document.addEventListener(event, handleUserInteraction, { once: true });\n    });\n\n    // Cleanup\n    return () => {\n      events.forEach(event => {\n        document.removeEventListener(event, handleUserInteraction);\n      });\n    };\n  }, [soundEnabled, userInteracted]);\n\n  // Translate zodiac sign name when language changes\n  useEffect(() => {\n    const translateSignName = async () => {\n      const translated = await getTranslatedText(sign.sinhala, 'zodiac_sign_name');\n      setTranslatedSignName(translated);\n    };\n\n    translateSignName();\n  }, [sign.sinhala, getTranslatedText]);\n\n  const handleRefresh = () => {\n    fetchHoroscope(true);\n  };\n\n\n\n  const toggleSound = () => {\n    const newSoundState = !soundEnabled;\n    setSoundEnabled(newSoundState);\n\n    // Track sound toggle\n    analytics.trackEvent('sound_toggle', {\n      event_category: 'user_preference',\n      zodiac_sign: sign.id,\n      sound_enabled: newSoundState,\n      action: newSoundState ? 'enable' : 'disable'\n    });\n\n    if (audioRef.current) {\n      if (newSoundState) {\n        audioRef.current.play().then(() => {\n          setUserInteracted(true);\n          setShowAudioPrompt(false);\n        }).catch(console.error);\n      } else {\n        audioRef.current.pause();\n      }\n    }\n  };\n\n  const getCurrentDate = () => {\n    const today = new Date();\n    const options = { \n      year: 'numeric', \n      month: 'long', \n      day: 'numeric',\n      weekday: 'long'\n    };\n    return today.toLocaleDateString('si-LK', options);\n  };\n\n  return (\n    <TranslationLoader>\n      <div className=\"zodiac-page\">\n        {/* Background Music Audio Element */}\n        <audio\n          ref={audioRef}\n          src={`/music.mp3?v=${window.CACHE_VERSION || Date.now()}`}\n          loop\n          style={{ display: 'none' }}\n        />\n      \n      {/* Divine Background Image */}\n      <div className=\"divine-background\">\n        <img \n          src={`/god.jpg?v=${window.CACHE_VERSION || Date.now()}`}\n          alt=\"Divine Blessing\" \n          className=\"god-image\"\n        />\n      </div>\n      \n      <ParticleBackground />\n      <SmokeAnimation />\n      <KuberaAnimation />\n      \n      <Link to=\"/\" className=\"back-button\">\n        {getUIText('backToHome')}\n      </Link>\n\n      {/* Language Selector */}\n      <div\n        className=\"language-selector-container\"\n        style={{\n          position: 'fixed',\n          top: '20px',\n          right: '20px',\n          zIndex: 1001\n        }}\n      >\n        <LanguageSelector />\n      </div>\n\n      <div className=\"zodiac-content\">\n        <div className=\"zodiac-header\">\n          <div className=\"zodiac-icon\" style={{ fontSize: '5rem', marginBottom: '1rem' }}>\n            {zodiacIcons[sign.id]}\n          </div>\n          <h1 className=\"zodiac-title\">{translatedSignName || sign.sinhala}</h1>\n          <h2 className=\"zodiac-subtitle\">{sign.english} {getUIText('zodiacSign')}</h2>\n          \n          {/* Display horoscope date if available */}\n          {horoscope && horoscope.dateCreated ? (\n            <div style={{ \n              background: 'rgba(244, 208, 63, 0.15)',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderRadius: '15px',\n              padding: '1rem',\n              marginBottom: '2rem',\n              textAlign: 'center'\n            }}>\n              <div style={{ color: '#f4d03f', fontSize: '1.1rem', fontWeight: 'bold', marginBottom: '0.5rem' }}>\n                {getUIText('horoscopeDate')}\n              </div>\n              <div style={{ color: '#ffffff', fontSize: '1rem' }}>\n                {new Date(horoscope.dateCreated).toLocaleDateString('si-LK', {\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric',\n                  weekday: 'long'\n                })}\n              </div>\n              {horoscope.createdAt && (\n                <div style={{ color: '#aeb6bf', fontSize: '0.85rem', marginTop: '0.5rem' }}>\n                  ජනනය කළේ: {new Date(horoscope.createdAt).toLocaleString('si-LK')}\n                </div>\n              )}\n            </div>\n          ) : (\n            <p style={{ color: '#aeb6bf', marginBottom: '2rem' }}>\n              {getCurrentDate()}\n            </p>\n          )}\n        </div>\n\n        <div className=\"horoscope-section\">\n          <div style={{ marginBottom: '1.5rem' }}>\n            <h3 className=\"horoscope-title\" style={{ margin: 0 }}>{getUIText('todaysHoroscope')}</h3>\n          </div>\n          \n          {lastUpdated && (\n            <div style={{ \n              fontSize: '0.85rem', \n              color: '#aeb6bf', \n              marginBottom: '1rem',\n              textAlign: 'center',\n              fontStyle: 'italic'\n            }}>\n              {getUIText('lastUpdated')}: {lastUpdated.toLocaleTimeString('si-LK', {\n                hour: '2-digit', \n                minute: '2-digit',\n                hour12: true\n              })}\n            </div>\n          )}\n          \n          {loading && (\n            <div className=\"loading\">\n              {getUIText('loading')}\n            </div>\n          )}\n\n          {refreshing && (\n            <div className=\"loading\">\n              {getUIText('refreshing')}\n            </div>\n          )}\n          \n          {error && (\n            <div className=\"error\">\n              {error}\n              <button \n                onClick={handleRefresh}\n                style={{\n                  marginLeft: '1rem',\n                  background: 'rgba(231, 76, 60, 0.1)',\n                  border: '1px solid #e74c3c',\n                  color: '#e74c3c',\n                  padding: '0.4rem 0.8rem',\n                  borderRadius: '15px',\n                  cursor: 'pointer',\n                  fontSize: '0.8rem'\n                }}\n              >\n                {getUIText('refreshHoroscope')}\n              </button>\n            </div>\n          )}\n          \n          {!loading && !refreshing && !error && horoscope && (\n            <StructuredHoroscopeDisplay horoscope={horoscope} />\n          )}\n        </div>\n\n        {/* Audio Prompt */}\n        {showAudioPrompt && soundEnabled && (\n          <div style={{\n            position: 'fixed',\n            top: '20px',\n            right: '20px',\n            background: 'rgba(244, 208, 63, 0.95)',\n            color: '#1a1a2e',\n            padding: '1rem 1.5rem',\n            borderRadius: '15px',\n            border: '2px solid #f4d03f',\n            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',\n            zIndex: 1000,\n            animation: 'pulse 2s infinite',\n            cursor: 'pointer',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            fontWeight: 'bold',\n            fontSize: '0.9rem',\n            maxWidth: '250px',\n            textAlign: 'center'\n          }}\n          onClick={() => {\n            if (audioRef.current) {\n              audioRef.current.play().then(() => {\n                setUserInteracted(true);\n                setShowAudioPrompt(false);\n              }).catch(console.error);\n            }\n          }}\n          >\n            🎵 ශබ්ද සක්‍රිය කිරීමට ක්ලික් කරන්න\n          </div>\n        )}\n\n        <div className=\"controls\" style={{ marginTop: '2rem', display: 'flex', gap: '1rem', flexWrap: 'wrap', justifyContent: 'center' }}>\n          <button\n            onClick={toggleSound}\n            className=\"sound-toggle\"\n            style={{\n              background: soundEnabled ? 'rgba(244, 208, 63, 0.2)' : 'rgba(255, 255, 255, 0.1)',\n              border: '1px solid #f4d03f',\n              color: '#f4d03f',\n              padding: '0.8rem 1.5rem',\n              borderRadius: '25px',\n              cursor: 'pointer',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              transition: 'all 0.3s ease'\n            }}\n          >\n            {soundEnabled ? getUIText('soundOn') : getUIText('soundOff')}\n          </button>\n\n\n        </div>\n\n        <div className=\"spiritual-message\" style={{\n          marginTop: '3rem',\n          padding: '2rem',\n          background: 'rgba(244, 208, 63, 0.1)',\n          borderRadius: '15px',\n          border: '1px solid rgba(244, 208, 63, 0.3)',\n          textAlign: 'center'\n        }}>\n          <p style={{ color: '#f4d03f', fontStyle: 'italic', fontSize: '1.1rem' }}>\n            {getUIText('spiritualMessage')}\n          </p>\n        </div>\n      </div>\n    </div>\n    </TranslationLoader>\n  );\n};\n\nexport default ZodiacPage;"], "mappings": "qHAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACvE,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CACrD,MAAO,CAAAC,cAAc,KAAM,kBAAkB,CAC7C,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,gBAAgB,KAAM,oBAAoB,CACjD,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CACnD,MAAO,CAAAC,gBAAgB,KAAM,8BAA8B,CAC3D,OAASC,YAAY,CAAEC,oBAAoB,KAAQ,uBAAuB,CAC1E,OAASC,oBAAoB,CAAEC,iBAAiB,KAAQ,+BAA+B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGxF,KAAM,CAAAC,WAAW,CAAG,CAClBC,KAAK,CAAE,GAAG,CACVC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,MAAM,CAAE,GAAG,CACXC,GAAG,CAAE,GAAG,CACRC,KAAK,CAAE,GAAG,CACVC,KAAK,CAAE,GAAG,CACVC,OAAO,CAAE,GAAG,CACZC,WAAW,CAAE,GAAG,CAChBC,SAAS,CAAE,GAAG,CACdC,QAAQ,CAAE,GAAG,CACbC,MAAM,CAAE,GACV,CAAC,CAED;AACA,KAAM,CAAAC,sCAAsC,CAAIC,OAAO,EAAK,CAC1D;AACA,GAAI,CAACA,OAAO,EAAI,MAAO,CAAAA,OAAO,GAAK,QAAQ,CAAE,CAC3C,MAAO,EAAE,CACX,CAEA;AACA,KAAM,CAAAC,SAAS,CAAGD,OAAO,CACtBE,OAAO,CAAC,OAAO,CAAE,EAAE,CAAC,CACpBA,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,KAAK,CAAE,EAAE,CAAC,CAClBA,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CACvBC,IAAI,CAAC,CAAC,CAET,KAAM,CAAAC,UAAU,CAAG,CACjBC,IAAI,CAAE,CACJC,EAAE,CAAE,MAAM,CACVC,KAAK,CAAE,mBAAmB,CAC1BC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,KAAK,CAAE,WAAW,CAAE,QAAQ,CAAE,OAAO,CAAE,QAAQ,CAC5D,CAAC,CACDC,MAAM,CAAE,CACNN,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,gBAAgB,CACvBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,QAAQ,CAAE,MAAM,CAAE,QAAQ,CAAE,UAAU,CAAE,MAAM,CAC3D,CAAC,CACDE,MAAM,CAAE,CACNP,EAAE,CAAE,QAAQ,CACZC,KAAK,CAAE,qBAAqB,CAC5BC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,MAAM,CAAE,KAAK,CAAE,WAAW,CAAE,MAAM,CAAE,QAAQ,CACzD,CAAC,CACDG,OAAO,CAAE,CACPR,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,eAAe,CACtBC,KAAK,CAAE,IAAI,CACXC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,MAAM,CAAE,OAAO,CAAE,QAAQ,CAAE,OAAO,CAAE,OAAO,CACxD,CAAC,CACDI,OAAO,CAAE,CACPT,EAAE,CAAE,SAAS,CACbC,KAAK,CAAE,iBAAiB,CACxBC,KAAK,CAAE,GAAG,CACVC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,QAAQ,CAAE,OAAO,CAAE,MAAM,CAC1D,CACF,CAAC,CAED;AACA,KAAM,CAAAK,KAAK,CAAGf,SAAS,CAACgB,KAAK,CAAC,IAAI,CAAC,CAACC,MAAM,CAACC,IAAI,EAAIA,IAAI,CAAChB,IAAI,CAAC,CAAC,CAACiB,MAAM,CAAG,CAAC,CAAC,CAC1E,GAAI,CAAAC,eAAe,CAAG,IAAI,CAC1B,GAAI,CAAAC,aAAa,CAAG,EAAE,CAEtB;AACA,GAAIN,KAAK,CAACI,MAAM,CAAG,CAAC,CAAE,CACpB;AACAhB,UAAU,CAACW,OAAO,CAACL,OAAO,CAAGT,SAAS,CACxC,CAAC,IAAM,CACL;AACA,IAAK,GAAI,CAAAsB,CAAC,CAAG,CAAC,CAAEA,CAAC,CAAGP,KAAK,CAACI,MAAM,CAAEG,CAAC,EAAE,CAAE,CACrC,KAAM,CAAAJ,IAAI,CAAGH,KAAK,CAACO,CAAC,CAAC,CAACpB,IAAI,CAAC,CAAC,CAE5B,GAAI,CAACgB,IAAI,EAAIA,IAAI,CAACC,MAAM,CAAG,CAAC,CAAE,SAE9B;AACA,GAAI,CAAAI,gBAAgB,CAAG,IAAI,CAE3B;AACA,KAAM,CAAAC,aAAa,CAAGN,IAAI,CAACO,KAAK,CAAC,UAAU,CAAC,CAC5C,GAAID,aAAa,CAAE,CACjB,KAAM,CAAAE,GAAG,CAAGC,QAAQ,CAACH,aAAa,CAAC,CAAC,CAAC,CAAC,CACtC,KAAM,CAAAI,aAAa,CAAG,CAAC,MAAM,CAAE,QAAQ,CAAE,QAAQ,CAAE,SAAS,CAAE,SAAS,CAAC,CACxE,GAAIF,GAAG,EAAI,CAAC,EAAIA,GAAG,EAAI,CAAC,CAAE,CACxBH,gBAAgB,CAAGK,aAAa,CAACF,GAAG,CAAG,CAAC,CAAC,CAC3C,CACF,CAEA;AACA,GAAI,CAACH,gBAAgB,CAAE,CACrB,IAAK,KAAM,CAACM,KAAK,CAAEC,OAAO,CAAC,EAAI,CAAAC,MAAM,CAACC,OAAO,CAAC7B,UAAU,CAAC,CAAE,CACzD,IAAK,KAAM,CAAA8B,OAAO,GAAI,CAAAH,OAAO,CAACpB,QAAQ,CAAE,CACtC,GAAIQ,IAAI,CAACgB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,OAAO,CAACC,WAAW,CAAC,CAAC,CAAC,CAAE,CACtDX,gBAAgB,CAAGM,KAAK,CACxB,MACF,CACF,CACA,GAAIN,gBAAgB,CAAE,MACxB,CACF,CAEA;AACA,GAAIA,gBAAgB,EAAIA,gBAAgB,GAAKH,eAAe,CAAE,CAC5D,GAAIA,eAAe,EAAIC,aAAa,CAACF,MAAM,CAAG,CAAC,CAAE,CAC/ChB,UAAU,CAACiB,eAAe,CAAC,CAACX,OAAO,CAAGY,aAAa,CAACe,IAAI,CAAC,GAAG,CAAC,CAAClC,IAAI,CAAC,CAAC,CACtE,CACAkB,eAAe,CAAGG,gBAAgB,CAClCF,aAAa,CAAG,EAAE,CAElB;AACA,GAAI,CAAAgB,YAAY,CAAGnB,IAAI,CACpBjB,OAAO,CAAC,WAAW,CAAE,EAAE,CAAC,CACxBA,OAAO,CAAC,UAAU,CAAE,EAAE,CAAC,CACvBA,OAAO,CAAC,GAAI,CAAAqC,MAAM,CAACnC,UAAU,CAACoB,gBAAgB,CAAC,CAACjB,KAAK,CAAE,IAAI,CAAC,CAAE,EAAE,CAAC,CACjEL,OAAO,CAAC,IAAI,CAAE,EAAE,CAAC,CACjBC,IAAI,CAAC,CAAC,CAET,GAAImC,YAAY,CAAClB,MAAM,CAAG,CAAC,CAAE,CAC3BE,aAAa,CAACkB,IAAI,CAACF,YAAY,CAAC,CAClC,CACF,CAAC,IAAM,IAAIjB,eAAe,CAAE,CAC1B;AACA,GAAI,CAAAiB,YAAY,CAAGnB,IAAI,CAAChB,IAAI,CAAC,CAAC,CAC9B,GAAImC,YAAY,CAAClB,MAAM,CAAG,CAAC,CAAE,CAC3BE,aAAa,CAACkB,IAAI,CAACF,YAAY,CAAC,CAClC,CACF,CAAC,IAAM,CACL;AACAjB,eAAe,CAAG,SAAS,CAC3BC,aAAa,CAACkB,IAAI,CAACrB,IAAI,CAAChB,IAAI,CAAC,CAAC,CAAC,CAChC,CACH,CAEA;AACA,GAAI,CAAC6B,MAAM,CAACS,MAAM,CAACrC,UAAU,CAAC,CAACsC,IAAI,CAACC,GAAG,EAAIA,GAAG,CAACjC,OAAO,CAAC,CAAE,CACvD,KAAM,CAAAkC,SAAS,CAAG3C,SAAS,CAACgB,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC2B,CAAC,EAAIA,CAAC,CAAC1C,IAAI,CAAC,CAAC,CAACiB,MAAM,CAAG,EAAE,CAAC,CAC5E,KAAM,CAAA0B,eAAe,CAAGd,MAAM,CAACe,IAAI,CAAC3C,UAAU,CAAC,CAE/CwC,SAAS,CAACI,OAAO,CAAC,CAACC,QAAQ,CAAEC,KAAK,GAAK,CACrC,KAAM,CAAAC,aAAa,CAAGD,KAAK,CAAGJ,eAAe,CAAC1B,MAAM,CACpD,KAAM,CAAAgC,WAAW,CAAGN,eAAe,CAACK,aAAa,CAAC,CAClD,GAAI,CAAC/C,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,CAAE,CACpCN,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,CAAGuC,QAAQ,CAAC9C,IAAI,CAAC,CAAC,CACnD,CAAC,IAAM,CACLC,UAAU,CAACgD,WAAW,CAAC,CAAC1C,OAAO,EAAI,GAAG,CAAGuC,QAAQ,CAAC9C,IAAI,CAAC,CAAC,CAC1D,CACF,CAAC,CAAC,CACJ,CACD,CAEA;AACA,GAAIkB,eAAe,EAAIC,aAAa,CAACF,MAAM,CAAG,CAAC,CAAE,CAC/ChB,UAAU,CAACiB,eAAe,CAAC,CAACX,OAAO,CAAGY,aAAa,CAACe,IAAI,CAAC,GAAG,CAAC,CAAClC,IAAI,CAAC,CAAC,CACtE,CAEA;AACD6B,MAAM,CAACS,MAAM,CAACrC,UAAU,CAAC,CAAC4C,OAAO,CAAC,CAACK,QAAQ,CAAEH,KAAK,GAAK,CACrD,GAAI,CAACG,QAAQ,CAAC3C,OAAO,EAAI2C,QAAQ,CAAC3C,OAAO,CAACU,MAAM,CAAG,CAAC,CAAE,CACpD;AACA,KAAM,CAAAwB,SAAS,CAAG3C,SAAS,CAACgB,KAAK,CAAC,OAAO,CAAC,CAACC,MAAM,CAAC2B,CAAC,EAAIA,CAAC,CAAC1C,IAAI,CAAC,CAAC,CAACiB,MAAM,CAAG,CAAC,CAAC,CAC3E,GAAIwB,SAAS,CAACxB,MAAM,CAAG8B,KAAK,CAAE,CAC5BG,QAAQ,CAAC3C,OAAO,CAAGkC,SAAS,CAACM,KAAK,CAAC,CAAC/C,IAAI,CAAC,CAAC,EAAIF,SAAS,CAACqD,SAAS,CAACJ,KAAK,CAAG,EAAE,CAAE,CAACA,KAAK,CAAG,CAAC,EAAI,EAAE,CAAC,CAAC/C,IAAI,CAAC,CAAC,CACxG,CAAC,IAAM,CACL;AACA,KAAM,CAAAoD,cAAc,CAAG,CACrBlD,IAAI,CAAE,8DAA8D,CACpEO,MAAM,CAAE,yDAAyD,CACjEC,MAAM,CAAE,mDAAmD,CAC3DC,OAAO,CAAE,0DAA0D,CACnEC,OAAO,CAAE,2DACX,CAAC,CACDsC,QAAQ,CAAC3C,OAAO,CAAG6C,cAAc,CAACF,QAAQ,CAAC/C,EAAE,CAAC,EAAI,+CAA+C,CACnG,CACF,CACF,CAAC,CAAC,CAED;AACA,MAAO,CAAA0B,MAAM,CAACC,OAAO,CAAC7B,UAAU,CAAC,CAACoD,GAAG,CAACC,IAAA,MAAC,CAACC,GAAG,CAAEL,QAAQ,CAAC,CAAAI,IAAA,QAAAE,aAAA,CAAAA,aAAA,IACjDN,QAAQ,MACX/C,EAAE,CAAEoD,GAAI;AAAA,IACR,CAAC,CACL,CAAC,CAED;AACA,KAAM,CAAAE,YAAY,CAAGC,KAAA,EAA6D,IAA5D,CAAER,QAAQ,CAAEH,KAAK,CAAEY,eAAe,CAAEC,iBAAkB,CAAC,CAAAF,KAAA,CAC3E,KAAM,CAAAG,UAAU,CAAG,CACjB3D,IAAI,CAAE,CACJ4D,UAAU,CAAE,sFAAsF,CAClGC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,qCACV,CAAC,CACDvD,MAAM,CAAE,CACNqD,UAAU,CAAE,oFAAoF,CAChGC,MAAM,CAAE,mCAAmC,CAC3CC,MAAM,CAAE,oCACV,CAAC,CACDtD,MAAM,CAAE,CACNoD,UAAU,CAAE,oFAAoF,CAChGC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,mCACV,CAAC,CACDrD,OAAO,CAAE,CACPmD,UAAU,CAAE,kFAAkF,CAC9FC,MAAM,CAAE,kCAAkC,CAC1CC,MAAM,CAAE,mCACV,CAAC,CACDpD,OAAO,CAAE,CACPkD,UAAU,CAAE,sFAAsF,CAClGC,MAAM,CAAE,oCAAoC,CAC5CC,MAAM,CAAE,qCACV,CACF,CAAC,CAED,KAAM,CAAAC,KAAK,CAAGJ,UAAU,CAACX,QAAQ,CAAC/C,EAAE,CAAC,EAAI0D,UAAU,CAACjD,OAAO,CAE3D,mBACE9B,KAAA,QACEoF,SAAS,CAAC,yBAAyB,CACnCD,KAAK,CAAE,CACLE,YAAY,CAAE,MAAM,CACpBC,OAAO,CAAE,MAAM,CACfN,UAAU,CAAEG,KAAK,CAACH,UAAU,CAC5BC,MAAM,CAAEE,KAAK,CAACF,MAAM,CACpBM,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAEL,KAAK,CAACD,MAAM,CACvBO,cAAc,CAAE,YAAY,CAC5BC,UAAU,CAAE,eAAe,CAC3BC,QAAQ,CAAE,UAAU,CACpBC,QAAQ,CAAE,QACZ,CAAE,CACFC,YAAY,CAAGC,CAAC,EAAK,CACnBA,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACa,SAAS,CAAG,kBAAkB,CACpDF,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACK,SAAS,CAAGL,KAAK,CAACD,MAAM,CAACjE,OAAO,CAAC,MAAM,CAAE,MAAM,CAAC,CACxE,CAAE,CACFgF,YAAY,CAAGH,CAAC,EAAK,CACnBA,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACa,SAAS,CAAG,eAAe,CACjDF,CAAC,CAACC,aAAa,CAACZ,KAAK,CAACK,SAAS,CAAGL,KAAK,CAACD,MAAM,CAChD,CAAE,CAAAgB,QAAA,eAGFpG,IAAA,QACEqF,KAAK,CAAE,CACLQ,QAAQ,CAAE,UAAU,CACpBQ,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbC,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdtB,UAAU,4BAAAuB,MAAA,CAA6BpB,KAAK,CAACF,MAAM,CAAChE,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,CAAE,OAAO,CAAC,0BAAwB,CAC9HuF,cAAc,CAAE,WAAW,CAC3BC,OAAO,CAAE,GAAG,CACZC,aAAa,CAAE,MACjB,CAAE,CACH,CAAC,cAGF1G,KAAA,QACEmF,KAAK,CAAE,CACLwB,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBvB,YAAY,CAAE,QAAQ,CACtBM,QAAQ,CAAE,UAAU,CACpBkB,MAAM,CAAE,CACV,CAAE,CAAAX,QAAA,eAEFpG,IAAA,QACEqF,KAAK,CAAE,CACL2B,QAAQ,CAAE,QAAQ,CAClBC,WAAW,CAAE,MAAM,CACnB9E,MAAM,CAAE,wCACV,CAAE,CAAAiE,QAAA,CAED9B,QAAQ,CAAC7C,KAAK,CACZ,CAAC,cACNvB,KAAA,QAAAkG,QAAA,eACEpG,IAAA,OACEqF,KAAK,CAAE,CACL6B,KAAK,CAAE,SAAS,CAChBF,QAAQ,CAAE,QAAQ,CAClBG,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,+BAA+B,CAC3CC,UAAU,CAAE,KAAK,CACjBC,UAAU,CAAE,2BACd,CAAE,CAAAlB,QAAA,CAEDrB,eAAe,EAAIT,QAAQ,CAAC9C,KAAK,CAChC,CAAC,cACLxB,IAAA,QACEqF,KAAK,CAAE,CACLkB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,KAAK,CACbtB,UAAU,CAAE,8CAA8C,CAC1DqC,SAAS,CAAE,QAAQ,CACnB9B,YAAY,CAAE,KAChB,CAAE,CACH,CAAC,EACC,CAAC,EACH,CAAC,cAGNzF,IAAA,QACEqF,KAAK,CAAE,CACLQ,QAAQ,CAAE,UAAU,CACpBkB,MAAM,CAAE,CACV,CAAE,CAAAX,QAAA,cAEFpG,IAAA,MACEqF,KAAK,CAAE,CACL6B,KAAK,CAAE,SAAS,CAChBM,UAAU,CAAE,KAAK,CACjBR,QAAQ,CAAE,QAAQ,CAClBG,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,+BAA+B,CAC3CK,SAAS,CAAE,SAAS,CACpBH,UAAU,CAAE,2BACd,CAAE,CAAAlB,QAAA,CAEDpB,iBAAiB,EAAIV,QAAQ,CAAC3C,OAAO,CACrC,CAAC,CACD,CAAC,cAGN3B,IAAA,QACEqF,KAAK,CAAE,CACLQ,QAAQ,CAAE,UAAU,CACpB6B,MAAM,CAAE,CAAC,CACTC,IAAI,CAAE,CAAC,CACPrB,KAAK,CAAE,CAAC,CACRE,MAAM,CAAE,KAAK,CACbtB,UAAU,2BAAAuB,MAAA,CAA4BpB,KAAK,CAACF,MAAM,CAAChE,OAAO,CAAC,YAAY,CAAE,EAAE,CAAC,CAACA,OAAO,CAAC,MAAM,CAAE,MAAM,CAAC,kBAAgB,CACpHsE,YAAY,CAAE,eAChB,CAAE,CACH,CAAC,EACC,CAAC,CAEV,CAAC,CAED;AACD,KAAM,CAAAmC,0BAA0B,CAAGC,KAAA,EAAmB,IAAlB,CAAEC,SAAU,CAAC,CAAAD,KAAA,CAC/C,KAAM,CAAEE,uBAAuB,CAAEC,SAAS,CAAEC,kBAAmB,CAAC,CAAGpI,oBAAoB,CAAC,CAAC,CACzF,KAAM,CAACqI,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGnJ,QAAQ,CAAC,IAAI,CAAC,CACtE,KAAM,CAAEoJ,SAAU,CAAC,CAAGtI,iBAAiB,CAAC,CAAC,CACzC,GAAI,CAAAuB,UAAU,CAEd;AACApC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAoJ,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAIhH,UAAU,EAAIA,UAAU,CAACgB,MAAM,CAAG,CAAC,CAAE,CACvC,KAAM,CAAAiG,UAAU,CAAG,KAAM,CAAAP,uBAAuB,CAAC1G,UAAU,CAAC,CAC5D8G,uBAAuB,CAACG,UAAU,CAAC,CACrC,CACF,CAAC,CAEDD,mBAAmB,CAAC,CAAC,CACvB,CAAC,CAAE,CAACP,SAAS,CAAEC,uBAAuB,CAAC,CAAC,CAExC;AACA,GAAID,SAAS,EAAIA,SAAS,CAACS,UAAU,EAAIT,SAAS,CAACzG,UAAU,CAAE,CAC7D;AACA,GAAImH,KAAK,CAACC,OAAO,CAACX,SAAS,CAACzG,UAAU,CAAC,CAAE,CACvCA,UAAU,CAAGyG,SAAS,CAACzG,UAAU,CACnC,CAAC,IAAM,CACL;AACAA,UAAU,CAAG4B,MAAM,CAACC,OAAO,CAAC4E,SAAS,CAACzG,UAAU,CAAC,CAACoD,GAAG,CAACiE,KAAA,MAAC,CAAC/D,GAAG,CAAEL,QAAQ,CAAC,CAAAoE,KAAA,QAAA9D,aAAA,CAAAA,aAAA,IACjEN,QAAQ,MACX/C,EAAE,CAAE+C,QAAQ,CAAC/C,EAAE,EAAIoD,GAAG,IACtB,CAAC,CACL,CACF,CAAC,IAAM,IAAI,MAAO,CAAAmD,SAAS,GAAK,QAAQ,CAAE,CACxC;AACAzG,UAAU,CAAGL,sCAAsC,CAAC8G,SAAS,CAAC,CAChE,CAAC,IAAM,CACLzG,UAAU,CAAG,EAAE,CACjB,CAEA;AACA,GAAI,CAACA,UAAU,EAAIA,UAAU,CAACgB,MAAM,GAAK,CAAC,CAAE,CAC1C,mBACErC,IAAA,QAAKqF,KAAK,CAAE,CACVoC,SAAS,CAAE,QAAQ,CACnBjC,OAAO,CAAE,MAAM,CACf0B,KAAK,CAAE,SAAS,CAChBE,UAAU,CAAE,+BACd,CAAE,CAAAhB,QAAA,CACCgC,SAAS,CAAC,SAAS,CAAC,CAClB,CAAC,CAEV,CAEA,mBACEpI,IAAA,QACEsF,SAAS,CAAC,8BAA8B,CACxCD,KAAK,CAAE,CACLsD,QAAQ,CAAE,OAAO,CACjBxB,MAAM,CAAE,QAAQ,CAChB3B,OAAO,CAAE,MACX,CAAE,CAAAY,QAAA,CAED,CAAC8B,oBAAoB,EAAI7G,UAAU,EAAEoD,GAAG,CAAC,CAACH,QAAQ,CAAEH,KAAK,GAAK,CAC7D,KAAM,CAAAyE,gBAAgB,CAAGvH,UAAU,CAAC8C,KAAK,CAAC,CAC1C,mBACEnE,IAAA,CAAC6E,YAAY,EAEXP,QAAQ,CAAEsE,gBAAiB,CAC3BzE,KAAK,CAAEA,KAAM,CACbY,eAAe,CAAET,QAAQ,CAAC9C,KAAM,CAChCwD,iBAAiB,CAAEV,QAAQ,CAAC3C,OAAQ,EAJ/B2C,QAAQ,CAAC/C,EAAE,cAAAkF,MAAA,CAAgBtC,KAAK,CAKtC,CAAC,CAEN,CAAC,CAAC,CACC,CAAC,CAEV,CAAC,CAED,KAAM,CAAA0E,UAAU,CAAGC,KAAA,EAAc,IAAb,CAAEC,IAAK,CAAC,CAAAD,KAAA,CAC1B,KAAM,CAAChB,SAAS,CAAEkB,YAAY,CAAC,CAAGhK,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiK,OAAO,CAAEC,UAAU,CAAC,CAAGlK,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACmK,KAAK,CAAEC,QAAQ,CAAC,CAAGpK,QAAQ,CAAC,EAAE,CAAC,CACtC,KAAM,CAACqK,YAAY,CAAEC,eAAe,CAAC,CAAGtK,QAAQ,CAAC,IAAI,CAAC,CAAE;AACxD,KAAM,CAACuK,WAAW,CAAEC,cAAc,CAAC,CAAGxK,QAAQ,CAAC,IAAI,CAAC,CACpD,KAAM,CAAEyK,iBAAkB,CAAC,CAAG5J,oBAAoB,CAAC,CAAC,CACpD,KAAM,CAAEuI,SAAU,CAAC,CAAGtI,iBAAiB,CAAC,CAAC,CACzC,KAAM,CAAC4J,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG3K,QAAQ,CAAC,EAAE,CAAC,CAEhE;AACA,KAAM,CAAA4K,SAAS,CAAGjK,YAAY,CAAC,CAAC,CAChCC,oBAAoB,CAAC,YAAY,CAAC,CAClC,KAAM,CAACiK,UAAU,CAAEC,aAAa,CAAC,CAAG9K,QAAQ,CAAC,KAAK,CAAC,CACnD,KAAM,CAAC+K,cAAc,CAAEC,iBAAiB,CAAC,CAAGhL,QAAQ,CAAC,KAAK,CAAC,CAC3D,KAAM,CAACiL,eAAe,CAAEC,kBAAkB,CAAC,CAAGlL,QAAQ,CAAC,KAAK,CAAC,CAC7D,KAAM,CAAAmL,QAAQ,CAAGhL,MAAM,CAAC,IAAI,CAAC,CAE7B,KAAM,CAAAiL,cAAc,CAAGlL,WAAW,CAAC,gBAAgC,IAAzB,CAAAmL,YAAY,CAAAC,SAAA,CAAAjI,MAAA,IAAAiI,SAAA,MAAAC,SAAA,CAAAD,SAAA,IAAG,KAAK,CAC5D,GAAI,CACF;AACAV,SAAS,CAACY,eAAe,CAACzB,IAAI,CAACxH,EAAE,CAAC,CAElC,GAAI8I,YAAY,CAAE,CAChBP,aAAa,CAAC,IAAI,CAAC,CACnBF,SAAS,CAACa,UAAU,CAAC,mBAAmB,CAAE,CACxCC,cAAc,CAAE,aAAa,CAC7BC,WAAW,CAAE5B,IAAI,CAACxH,EACpB,CAAC,CAAC,CACJ,CAAC,IAAM,CACL2H,UAAU,CAAC,IAAI,CAAC,CAClB,CACAE,QAAQ,CAAC,EAAE,CAAC,CAEZ;AACA,GAAI,CAACiB,YAAY,CAAE,CACjB,KAAM,CAAAO,eAAe,CAAGlL,gBAAgB,CAACmL,kBAAkB,CAAC9B,IAAI,CAACxH,EAAE,CAAC,CACpE,GAAIqJ,eAAe,CAAE,CACnB5B,YAAY,CAAC4B,eAAe,CAAC,CAC7BpB,cAAc,CAAC,GAAI,CAAAsB,IAAI,CAAC,CAAC,CAAC,CAC1B5B,UAAU,CAAC,KAAK,CAAC,CACjB,OACF,CACF,CAEA;AACA,KAAM,CAAA6B,aAAa,CAAG,KAAM,CAAArL,gBAAgB,CAACsL,YAAY,CAACjC,IAAI,CAACxH,EAAE,CAAE8I,YAAY,CAAC,CAEhF;AACA,GAAIU,aAAa,EAAIA,aAAa,CAAC1J,UAAU,CAAE,CAC7C;AACA,KAAM,CAAA4J,cAAc,CAAG,CACrB3J,IAAI,CAAE,CAAEC,EAAE,CAAE,MAAM,CAAEC,KAAK,CAAE,mBAAmB,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC7DI,MAAM,CAAE,CAAEN,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,gBAAgB,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC9DK,MAAM,CAAE,CAAEP,EAAE,CAAE,QAAQ,CAAEC,KAAK,CAAE,qBAAqB,CAAEC,KAAK,CAAE,IAAK,CAAC,CACnEM,OAAO,CAAE,CAAER,EAAE,CAAE,SAAS,CAAEC,KAAK,CAAE,eAAe,CAAEC,KAAK,CAAE,IAAK,CAAC,CAC/DO,OAAO,CAAE,CAAET,EAAE,CAAE,SAAS,CAAEC,KAAK,CAAE,iBAAiB,CAAEC,KAAK,CAAE,GAAI,CACjE,CAAC,CAED,KAAM,CAAAJ,UAAU,CAAG4B,MAAM,CAACC,OAAO,CAAC6H,aAAa,CAAC1J,UAAU,CAAC,CAACoD,GAAG,CAACyG,KAAA,MAAC,CAACvG,GAAG,CAAEhD,OAAO,CAAC,CAAAuJ,KAAA,QAAAtG,aAAA,CAAAA,aAAA,IAC1EqG,cAAc,CAACtG,GAAG,CAAC,MACtBhD,OAAO,CAAEA,OAAO,EAAI,mCAAmC,IACvD,CAAC,CAEHqH,YAAY,CAAC,CACX3H,UAAU,CACVkH,UAAU,CAAE,IAAI,CAChB4C,WAAW,CAAEJ,aAAa,CAACK,YAAY,CACvCC,SAAS,CAAEN,aAAa,CAACO,UAAU,CACnCC,UAAU,CAAER,aAAa,CAACS,WAC5B,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACAxC,YAAY,CAAC+B,aAAa,CAAC,CAC7B,CAEAvB,cAAc,CAAC,GAAI,CAAAsB,IAAI,CAAC,CAAC,CAAC,CAE1B;AACAlB,SAAS,CAACa,UAAU,CAAC,kBAAkB,CAAE,CACvCC,cAAc,CAAE,qBAAqB,CACrCC,WAAW,CAAE5B,IAAI,CAACxH,EAAE,CACpBkK,SAAS,CAAEpB,YAAY,CAAG,SAAS,CAAG,SAAS,CAC/CqB,cAAc,CAAE,MAAO,CAAAX,aAAa,GAAK,QAAQ,CAAGA,aAAa,CAAC1I,MAAM,CAAGsJ,IAAI,CAACC,SAAS,CAACb,aAAa,CAAC,CAAC1I,MAC3G,CAAC,CAAC,CAEF;AACA3C,gBAAgB,CAACmM,kBAAkB,CAAC9C,IAAI,CAACxH,EAAE,CAAEwJ,aAAa,CAAC,CAE7D,CAAE,MAAOe,GAAG,CAAE,CACZ1C,QAAQ,CAAC,gEAAgE,CAAC,CAC1E2C,OAAO,CAAC5C,KAAK,CAAC,2BAA2B,CAAE2C,GAAG,CAAC,CAE/C;AACAlC,SAAS,CAACoC,UAAU,2BAAAvF,MAAA,CACQqF,GAAG,CAACG,OAAO,gBAAAxF,MAAA,CACvBsC,IAAI,CAACxH,EAAE,EACrB,KACF,CAAC,CACH,CAAC,OAAS,CACR2H,UAAU,CAAC,KAAK,CAAC,CACjBY,aAAa,CAAC,KAAK,CAAC,CACtB,CACF,CAAC,CAAE,CAACf,IAAI,CAACxH,EAAE,CAAC,CAAC,CAEbtC,SAAS,CAAC,IAAM,CACdmL,cAAc,CAAC,CAAC,CAClB,CAAC,CAAE,CAACA,cAAc,CAAC,CAAC,CAEpB;AACAnL,SAAS,CAAC,IAAM,CACd,KAAM,CAAAiN,KAAK,CAAGC,UAAU,CAAC,IAAM,CAC7B,GAAIhC,QAAQ,CAACiC,OAAO,EAAI/C,YAAY,EAAI,CAACU,cAAc,CAAE,CACvDI,QAAQ,CAACiC,OAAO,CAACC,IAAI,CAAG,IAAI,CAC5BlC,QAAQ,CAACiC,OAAO,CAACE,MAAM,CAAG,GAAG,CAE7B;AACA,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,WAAW,CAAGrC,QAAQ,CAACiC,OAAO,CAACK,IAAI,CAAC,CAAC,CAC3C,GAAID,WAAW,GAAKjC,SAAS,CAAE,CAC7BiC,WAAW,CAACE,IAAI,CAAC,IAAM,CACrB1C,iBAAiB,CAAC,IAAI,CAAC,CACvBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CAACyC,KAAK,CAAC,IAAM,CACb;AACAzC,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAAC,CACJ,CACF,CAAC,CAEDqC,WAAW,CAAC,CAAC,CACf,CACF,CAAC,CAAE,GAAG,CAAC,CAAE;AAET,MAAO,IAAMK,YAAY,CAACV,KAAK,CAAC,CAClC,CAAC,CAAE,CAAC7C,YAAY,CAAEU,cAAc,CAAC,CAAC,CAAE;AAEpC;AACA9K,SAAS,CAAC,IAAM,CACd,GAAIkL,QAAQ,CAACiC,OAAO,EAAI/C,YAAY,CAAE,CACpC;AACAc,QAAQ,CAACiC,OAAO,CAACC,IAAI,CAAG,IAAI,CAC5BlC,QAAQ,CAACiC,OAAO,CAACE,MAAM,CAAG,GAAG,CAAE;AAE/B;AACA,KAAM,CAAAE,WAAW,CAAGrC,QAAQ,CAACiC,OAAO,CAACK,IAAI,CAAC,CAAC,CAC3C,GAAID,WAAW,GAAKjC,SAAS,CAAE,CAC7BiC,WAAW,CAACE,IAAI,CAAC,IAAM,CACrB1C,iBAAiB,CAAC,IAAI,CAAC,CACvBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CAACyC,KAAK,CAACxD,KAAK,EAAI,CAChB;AACAe,kBAAkB,CAAC,IAAI,CAAC,CAC1B,CAAC,CAAC,CACJ,CACF,CACF,CAAC,CAAE,CAACb,YAAY,CAAEU,cAAc,CAAC,CAAC,CAElC;AACA9K,SAAS,CAAC,IAAM,CACd,KAAM,CAAA4N,qBAAqB,CAAGA,CAAA,GAAM,CAClC,GAAI,CAAC9C,cAAc,EAAII,QAAQ,CAACiC,OAAO,EAAI/C,YAAY,CAAE,CACvD,KAAM,CAAAmD,WAAW,CAAGrC,QAAQ,CAACiC,OAAO,CAACK,IAAI,CAAC,CAAC,CAC3C,GAAID,WAAW,GAAKjC,SAAS,CAAE,CAC7BiC,WAAW,CAACE,IAAI,CAAC,IAAM,CACrB1C,iBAAiB,CAAC,IAAI,CAAC,CACvBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CAACyC,KAAK,CAACZ,OAAO,CAAC5C,KAAK,CAAC,CACzB,CACF,CACF,CAAC,CAED;AACA,KAAM,CAAA2D,MAAM,CAAG,CAAC,OAAO,CAAE,YAAY,CAAE,SAAS,CAAE,QAAQ,CAAC,CAC3DA,MAAM,CAAC7I,OAAO,CAAC8I,KAAK,EAAI,CACtBC,QAAQ,CAACC,gBAAgB,CAACF,KAAK,CAAEF,qBAAqB,CAAE,CAAEK,IAAI,CAAE,IAAK,CAAC,CAAC,CACzE,CAAC,CAAC,CAEF;AACA,MAAO,IAAM,CACXJ,MAAM,CAAC7I,OAAO,CAAC8I,KAAK,EAAI,CACtBC,QAAQ,CAACG,mBAAmB,CAACJ,KAAK,CAAEF,qBAAqB,CAAC,CAC5D,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAE,CAACxD,YAAY,CAAEU,cAAc,CAAC,CAAC,CAElC;AACA9K,SAAS,CAAC,IAAM,CACd,KAAM,CAAAmO,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpC,KAAM,CAAA9E,UAAU,CAAG,KAAM,CAAAmB,iBAAiB,CAACV,IAAI,CAACsE,OAAO,CAAE,kBAAkB,CAAC,CAC5E1D,qBAAqB,CAACrB,UAAU,CAAC,CACnC,CAAC,CAED8E,iBAAiB,CAAC,CAAC,CACrB,CAAC,CAAE,CAACrE,IAAI,CAACsE,OAAO,CAAE5D,iBAAiB,CAAC,CAAC,CAErC,KAAM,CAAA6D,aAAa,CAAGA,CAAA,GAAM,CAC1BlD,cAAc,CAAC,IAAI,CAAC,CACtB,CAAC,CAID,KAAM,CAAAmD,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,aAAa,CAAG,CAACnE,YAAY,CACnCC,eAAe,CAACkE,aAAa,CAAC,CAE9B;AACA5D,SAAS,CAACa,UAAU,CAAC,cAAc,CAAE,CACnCC,cAAc,CAAE,iBAAiB,CACjCC,WAAW,CAAE5B,IAAI,CAACxH,EAAE,CACpBkM,aAAa,CAAED,aAAa,CAC5BE,MAAM,CAAEF,aAAa,CAAG,QAAQ,CAAG,SACrC,CAAC,CAAC,CAEF,GAAIrD,QAAQ,CAACiC,OAAO,CAAE,CACpB,GAAIoB,aAAa,CAAE,CACjBrD,QAAQ,CAACiC,OAAO,CAACK,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAM,CACjC1C,iBAAiB,CAAC,IAAI,CAAC,CACvBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CAACyC,KAAK,CAACZ,OAAO,CAAC5C,KAAK,CAAC,CACzB,CAAC,IAAM,CACLgB,QAAQ,CAACiC,OAAO,CAACuB,KAAK,CAAC,CAAC,CAC1B,CACF,CACF,CAAC,CAED,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAA/C,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAgD,OAAO,CAAG,CACdC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,OAAO,CAAE,MACX,CAAC,CACD,MAAO,CAAAL,KAAK,CAACM,kBAAkB,CAAC,OAAO,CAAEL,OAAO,CAAC,CACnD,CAAC,CAED,mBACE9N,IAAA,CAACP,iBAAiB,EAAA2G,QAAA,cAChBlG,KAAA,QAAKoF,SAAS,CAAC,aAAa,CAAAc,QAAA,eAE1BpG,IAAA,UACEoO,GAAG,CAAEjE,QAAS,CACdkE,GAAG,iBAAA5H,MAAA,CAAkB6H,MAAM,CAACC,aAAa,EAAIzD,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAAG,CAC1DnC,IAAI,MACJhH,KAAK,CAAE,CAAEwB,OAAO,CAAE,MAAO,CAAE,CAC5B,CAAC,cAGJ7G,IAAA,QAAKsF,SAAS,CAAC,mBAAmB,CAAAc,QAAA,cAChCpG,IAAA,QACEqO,GAAG,eAAA5H,MAAA,CAAgB6H,MAAM,CAACC,aAAa,EAAIzD,IAAI,CAAC0D,GAAG,CAAC,CAAC,CAAG,CACxDC,GAAG,CAAC,iBAAiB,CACrBnJ,SAAS,CAAC,WAAW,CACtB,CAAC,CACC,CAAC,cAENtF,IAAA,CAACX,kBAAkB,GAAE,CAAC,cACtBW,IAAA,CAACV,cAAc,GAAE,CAAC,cAClBU,IAAA,CAACT,eAAe,GAAE,CAAC,cAEnBS,IAAA,CAACZ,IAAI,EAACsP,EAAE,CAAC,GAAG,CAACpJ,SAAS,CAAC,aAAa,CAAAc,QAAA,CACjCgC,SAAS,CAAC,YAAY,CAAC,CACpB,CAAC,cAGPpI,IAAA,QACEsF,SAAS,CAAC,6BAA6B,CACvCD,KAAK,CAAE,CACLQ,QAAQ,CAAE,OAAO,CACjBQ,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbS,MAAM,CAAE,IACV,CAAE,CAAAX,QAAA,cAEFpG,IAAA,CAACR,gBAAgB,GAAE,CAAC,CACjB,CAAC,cAENU,KAAA,QAAKoF,SAAS,CAAC,gBAAgB,CAAAc,QAAA,eAC7BlG,KAAA,QAAKoF,SAAS,CAAC,eAAe,CAAAc,QAAA,eAC5BpG,IAAA,QAAKsF,SAAS,CAAC,aAAa,CAACD,KAAK,CAAE,CAAE2B,QAAQ,CAAE,MAAM,CAAEzB,YAAY,CAAE,MAAO,CAAE,CAAAa,QAAA,CAC5EjG,WAAW,CAAC4I,IAAI,CAACxH,EAAE,CAAC,CAClB,CAAC,cACNvB,IAAA,OAAIsF,SAAS,CAAC,cAAc,CAAAc,QAAA,CAAEsD,kBAAkB,EAAIX,IAAI,CAACsE,OAAO,CAAK,CAAC,cACtEnN,KAAA,OAAIoF,SAAS,CAAC,iBAAiB,CAAAc,QAAA,EAAE2C,IAAI,CAAC4F,OAAO,CAAC,GAAC,CAACvG,SAAS,CAAC,YAAY,CAAC,EAAK,CAAC,CAG5EN,SAAS,EAAIA,SAAS,CAACqD,WAAW,cACjCjL,KAAA,QAAKmF,KAAK,CAAE,CACVH,UAAU,CAAE,0BAA0B,CACtCC,MAAM,CAAE,mCAAmC,CAC3CM,YAAY,CAAE,MAAM,CACpBD,OAAO,CAAE,MAAM,CACfD,YAAY,CAAE,MAAM,CACpBkC,SAAS,CAAE,QACb,CAAE,CAAArB,QAAA,eACApG,IAAA,QAAKqF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAEF,QAAQ,CAAE,QAAQ,CAAEK,UAAU,CAAE,MAAM,CAAE9B,YAAY,CAAE,QAAS,CAAE,CAAAa,QAAA,CAC9FgC,SAAS,CAAC,eAAe,CAAC,CACxB,CAAC,cACNpI,IAAA,QAAKqF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAEF,QAAQ,CAAE,MAAO,CAAE,CAAAZ,QAAA,CAChD,GAAI,CAAA0E,IAAI,CAAChD,SAAS,CAACqD,WAAW,CAAC,CAACgD,kBAAkB,CAAC,OAAO,CAAE,CAC3DJ,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,OAAO,CAAE,MACX,CAAC,CAAC,CACC,CAAC,CACLpG,SAAS,CAACuD,SAAS,eAClBnL,KAAA,QAAKmF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAEF,QAAQ,CAAE,SAAS,CAAEO,SAAS,CAAE,QAAS,CAAE,CAAAnB,QAAA,EAAC,+CAChE,CAAC,GAAI,CAAA0E,IAAI,CAAChD,SAAS,CAACuD,SAAS,CAAC,CAACuD,cAAc,CAAC,OAAO,CAAC,EAC7D,CACN,EACE,CAAC,cAEN5O,IAAA,MAAGqF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAE3B,YAAY,CAAE,MAAO,CAAE,CAAAa,QAAA,CAClDwH,cAAc,CAAC,CAAC,CAChB,CACJ,EACE,CAAC,cAEN1N,KAAA,QAAKoF,SAAS,CAAC,mBAAmB,CAAAc,QAAA,eAChCpG,IAAA,QAAKqF,KAAK,CAAE,CAAEE,YAAY,CAAE,QAAS,CAAE,CAAAa,QAAA,cACrCpG,IAAA,OAAIsF,SAAS,CAAC,iBAAiB,CAACD,KAAK,CAAE,CAAE8B,MAAM,CAAE,CAAE,CAAE,CAAAf,QAAA,CAAEgC,SAAS,CAAC,iBAAiB,CAAC,CAAK,CAAC,CACtF,CAAC,CAELmB,WAAW,eACVrJ,KAAA,QAAKmF,KAAK,CAAE,CACV2B,QAAQ,CAAE,SAAS,CACnBE,KAAK,CAAE,SAAS,CAChB3B,YAAY,CAAE,MAAM,CACpBkC,SAAS,CAAE,QAAQ,CACnBoH,SAAS,CAAE,QACb,CAAE,CAAAzI,QAAA,EACCgC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAE,CAACmB,WAAW,CAACuF,kBAAkB,CAAC,OAAO,CAAE,CACnEC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SAAS,CACjBC,MAAM,CAAE,IACV,CAAC,CAAC,EACC,CACN,CAEAhG,OAAO,eACNjJ,IAAA,QAAKsF,SAAS,CAAC,SAAS,CAAAc,QAAA,CACrBgC,SAAS,CAAC,SAAS,CAAC,CAClB,CACN,CAEAyB,UAAU,eACT7J,IAAA,QAAKsF,SAAS,CAAC,SAAS,CAAAc,QAAA,CACrBgC,SAAS,CAAC,YAAY,CAAC,CACrB,CACN,CAEAe,KAAK,eACJjJ,KAAA,QAAKoF,SAAS,CAAC,OAAO,CAAAc,QAAA,EACnB+C,KAAK,cACNnJ,IAAA,WACEkP,OAAO,CAAE5B,aAAc,CACvBjI,KAAK,CAAE,CACL8J,UAAU,CAAE,MAAM,CAClBjK,UAAU,CAAE,wBAAwB,CACpCC,MAAM,CAAE,mBAAmB,CAC3B+B,KAAK,CAAE,SAAS,CAChB1B,OAAO,CAAE,eAAe,CACxBC,YAAY,CAAE,MAAM,CACpB2J,MAAM,CAAE,SAAS,CACjBpI,QAAQ,CAAE,QACZ,CAAE,CAAAZ,QAAA,CAEDgC,SAAS,CAAC,kBAAkB,CAAC,CACxB,CAAC,EACN,CACN,CAEA,CAACa,OAAO,EAAI,CAACY,UAAU,EAAI,CAACV,KAAK,EAAIrB,SAAS,eAC7C9H,IAAA,CAAC4H,0BAA0B,EAACE,SAAS,CAAEA,SAAU,CAAE,CACpD,EACE,CAAC,CAGLmC,eAAe,EAAIZ,YAAY,eAC9BrJ,IAAA,QAAKqF,KAAK,CAAE,CACVQ,QAAQ,CAAE,OAAO,CACjBQ,GAAG,CAAE,MAAM,CACXC,KAAK,CAAE,MAAM,CACbpB,UAAU,CAAE,0BAA0B,CACtCgC,KAAK,CAAE,SAAS,CAChB1B,OAAO,CAAE,aAAa,CACtBC,YAAY,CAAE,MAAM,CACpBN,MAAM,CAAE,mBAAmB,CAC3BO,SAAS,CAAE,+BAA+B,CAC1CqB,MAAM,CAAE,IAAI,CACZsI,SAAS,CAAE,mBAAmB,CAC9BD,MAAM,CAAE,SAAS,CACjBhI,UAAU,CAAE,+BAA+B,CAC3CC,UAAU,CAAE,MAAM,CAClBL,QAAQ,CAAE,QAAQ,CAClB2B,QAAQ,CAAE,OAAO,CACjBlB,SAAS,CAAE,QACb,CAAE,CACFyH,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI/E,QAAQ,CAACiC,OAAO,CAAE,CACpBjC,QAAQ,CAACiC,OAAO,CAACK,IAAI,CAAC,CAAC,CAACC,IAAI,CAAC,IAAM,CACjC1C,iBAAiB,CAAC,IAAI,CAAC,CACvBE,kBAAkB,CAAC,KAAK,CAAC,CAC3B,CAAC,CAAC,CAACyC,KAAK,CAACZ,OAAO,CAAC5C,KAAK,CAAC,CACzB,CACF,CAAE,CAAA/C,QAAA,CACD,2LAED,CAAK,CACN,cAEDpG,IAAA,QAAKsF,SAAS,CAAC,UAAU,CAACD,KAAK,CAAE,CAAEkC,SAAS,CAAE,MAAM,CAAEV,OAAO,CAAE,MAAM,CAAEyI,GAAG,CAAE,MAAM,CAAEC,QAAQ,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAS,CAAE,CAAApJ,QAAA,cAC/HpG,IAAA,WACEkP,OAAO,CAAE3B,WAAY,CACrBjI,SAAS,CAAC,cAAc,CACxBD,KAAK,CAAE,CACLH,UAAU,CAAEmE,YAAY,CAAG,yBAAyB,CAAG,0BAA0B,CACjFlE,MAAM,CAAE,mBAAmB,CAC3B+B,KAAK,CAAE,SAAS,CAChB1B,OAAO,CAAE,eAAe,CACxBC,YAAY,CAAE,MAAM,CACpB2J,MAAM,CAAE,SAAS,CACjBhI,UAAU,CAAE,+BAA+B,CAC3CxB,UAAU,CAAE,eACd,CAAE,CAAAQ,QAAA,CAEDiD,YAAY,CAAGjB,SAAS,CAAC,SAAS,CAAC,CAAGA,SAAS,CAAC,UAAU,CAAC,CACtD,CAAC,CAGN,CAAC,cAENpI,IAAA,QAAKsF,SAAS,CAAC,mBAAmB,CAACD,KAAK,CAAE,CACxCkC,SAAS,CAAE,MAAM,CACjB/B,OAAO,CAAE,MAAM,CACfN,UAAU,CAAE,yBAAyB,CACrCO,YAAY,CAAE,MAAM,CACpBN,MAAM,CAAE,mCAAmC,CAC3CsC,SAAS,CAAE,QACb,CAAE,CAAArB,QAAA,cACApG,IAAA,MAAGqF,KAAK,CAAE,CAAE6B,KAAK,CAAE,SAAS,CAAE2H,SAAS,CAAE,QAAQ,CAAE7H,QAAQ,CAAE,QAAS,CAAE,CAAAZ,QAAA,CACrEgC,SAAS,CAAC,kBAAkB,CAAC,CAC7B,CAAC,CACD,CAAC,EACH,CAAC,EACH,CAAC,CACa,CAAC,CAExB,CAAC,CAED,cAAe,CAAAS,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}