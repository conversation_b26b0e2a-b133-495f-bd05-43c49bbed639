#!/bin/bash

# Clean Deployment Script for Language Selector Mobile Responsive Updates
# This script will COMPLETELY CLEAN and redeploy the application to kubera.help

echo "🧹 Starting CLEAN deployment of Language Selector Updates to kubera.help..."
echo "=========================================================================="

# Configuration
SERVER_IP="<EMAIL>"
SSH_KEY="~/.ssh/kubera.pem"
REMOTE_PATH="/var/www/kubera.help"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Step 1: Build frontend with production configuration
echo_info "Step 1: Building frontend with production configuration..."

# Ensure correct environment variables for frontend
cat > .env << EOF
REACT_APP_API_URL=https://kubera.help/api
REACT_APP_DOMAIN=kubera.help
NODE_ENV=production
REACT_APP_GA_MEASUREMENT_ID=G-072DL84PM0
EOF

# Build frontend
npm run build
if [ $? -ne 0 ]; then
    echo_error "Frontend build failed!"
    exit 1
fi

echo_success "Frontend build completed"

# Step 2: Prepare backend configuration
echo_info "Step 2: Preparing backend configuration..."

# Ensure correct environment variables for backend
cat > server/.env << EOF
PORT=5000
NODE_ENV=production
DB_PATH=./horoscope.db
GEMINI_API_KEY=AIzaSyBdazvi0etIk23H6Fn7nNpU1BS55H9NNVc
OPENAI_API_KEY=********************************************************************************************************************************************************************
TZ=Asia/Colombo
CORS_ORIGIN=https://kubera.help
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=241550840
EOF

echo_success "Backend configuration prepared"

# Step 3: Test SSH connection
echo_info "Step 3: Testing SSH connection..."
ssh -i $SSH_KEY $SERVER_IP "echo 'SSH connection successful'" > /dev/null 2>&1
if [ $? -ne 0 ]; then
    echo_error "Cannot connect to server via SSH"
    exit 1
fi
echo_success "SSH connection verified"

# Step 4: Create backup
echo_info "Step 4: Creating backup of current deployment..."
ssh -i $SSH_KEY $SERVER_IP << 'ENDSSH'
cd /var/www/kubera.help
if [ -d "build" ]; then
    sudo cp -r build build_backup_$(date +%Y%m%d_%H%M%S)
    echo "✅ Backup created"
else
    echo "ℹ️  No existing build to backup"
fi
ENDSSH

# Step 5: Upload new files
echo_info "Step 5: Uploading updated files..."

# Upload frontend build
echo_info "📤 Uploading frontend build..."
scp -i $SSH_KEY -r build/* $SERVER_IP:$REMOTE_PATH/

# Upload backend (only if there are changes)
echo_info "📤 Uploading backend updates..."
scp -i $SSH_KEY -r server/ $SERVER_IP:$REMOTE_PATH/

echo_success "Files uploaded successfully"

# Step 6: Set up production environment
echo_info "Step 6: Setting up production environment..."

ssh -i $SSH_KEY $SERVER_IP << 'ENDSSH'
cd /var/www/kubera.help

echo "📦 Installing/updating backend dependencies..."
cd server
npm install --production

echo "🚀 Restarting PM2 process..."
pm2 restart kubera-backend || pm2 start server.js --name kubera-backend --env production

echo "⏳ Waiting for service to start..."
sleep 3

echo "🔍 Checking PM2 status..."
pm2 status kubera-backend

echo "✅ Production environment setup completed"
ENDSSH

# Step 7: Verify deployment
echo_info "Step 7: Verifying deployment..."

echo_info "🔍 Testing API endpoint..."
API_RESPONSE=$(curl -s https://kubera.help/api/health)
if [[ $API_RESPONSE == *"success"* ]]; then
    echo_success "API is working!"
    echo "$API_RESPONSE"
else
    echo_warning "API response: $API_RESPONSE"
fi

echo_info "🔍 Testing website..."
WEBSITE_RESPONSE=$(curl -s -o /dev/null -w "%{http_code}" https://kubera.help)
if [ "$WEBSITE_RESPONSE" = "200" ]; then
    echo_success "Website is accessible!"
else
    echo_warning "Website HTTP status: $WEBSITE_RESPONSE"
fi

echo ""
echo_success "🎉 Language Selector Mobile Responsive Update Deployment Completed!"
echo ""
echo_info "🧪 Please test the following:"
echo "1. Visit: https://kubera.help"
echo "2. Test mobile responsive language selector on different screen sizes"
echo "3. Verify translation functionality works"
echo "4. Test API: https://kubera.help/api/health"
echo ""
echo_info "📊 Monitoring commands:"
echo "ssh -i ~/.ssh/kubera.pem <EMAIL> 'pm2 logs kubera-backend'"
echo "ssh -i ~/.ssh/kubera.pem <EMAIL> 'pm2 status'"
echo "ssh -i ~/.ssh/kubera.pem <EMAIL> 'pm2 monit'"
echo ""
echo_success "✨ Mobile responsive language selector is now live!"
