{"ast": null, "code": "/**\n * React Hook for Google Analytics Integration\n * Provides easy-to-use analytics functions for React components\n */import{useEffect,useCallback,useRef}from'react';import{useLocation}from'react-router-dom';import{initGA,trackPageView,trackEvent,trackZodiacInteraction,trackKuberaCardEvent,trackScrollDepth,trackEngagement,trackFormSubmission,trackError,setUserProperties}from'../services/analytics';/**\n * Main analytics hook\n * @param {Object} options - Configuration options\n * @returns {Object} Analytics functions\n */export const useAnalytics=function(){let options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};const location=useLocation();const startTimeRef=useRef(Date.now());const scrollTrackedRef=useRef(new Set());// Initialize GA on mount\nuseEffect(()=>{initGA();},[]);// Track page views on route changes\nuseEffect(()=>{const path=location.pathname;const search=location.search;const fullPath=path+search;// Reset engagement tracking for new page\nstartTimeRef.current=Date.now();scrollTrackedRef.current.clear();// Determine page title based on path\nlet pageTitle='කුබේර දෙවියන්ගේ ආශීර්වාදය';if(path==='/'){pageTitle='කුබේර දෙවියන්ගේ ආශීර්වාදය - මුල් පිටුව';}else if(path.includes('/')){// Extract zodiac sign from path\nconst zodiacSign=path.replace('/','');const zodiacNames={'aries':'මේෂ','taurus':'වෘෂභ','gemini':'මිථුන','cancer':'කටක','leo':'සිංහ','virgo':'කන්‍යා','libra':'තුලා','scorpio':'වෘශ්චික','sagittarius':'ධනු','capricorn':'මකර','aquarius':'කුම්භ','pisces':'මීන'};if(zodiacNames[zodiacSign]){pageTitle=\"\".concat(zodiacNames[zodiacSign],\" \\u0DBB\\u0DCF\\u0DC1\\u0DD2\\u0DB5\\u0DBD - \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA\");}}// Track the page view\ntrackPageView(fullPath,pageTitle,{referrer:document.referrer,user_language:'si'});},[location]);// Track scroll depth\nconst handleScroll=useCallback(()=>{const scrollTop=window.pageYOffset||document.documentElement.scrollTop;const documentHeight=document.documentElement.scrollHeight-window.innerHeight;const scrollPercentage=Math.round(scrollTop/documentHeight*100);// Track scroll milestones\nconst milestones=[25,50,75,90,100];milestones.forEach(milestone=>{if(scrollPercentage>=milestone&&!scrollTrackedRef.current.has(milestone)){scrollTrackedRef.current.add(milestone);trackScrollDepth(milestone,location.pathname);}});},[location.pathname]);// Set up scroll tracking\nuseEffect(()=>{window.addEventListener('scroll',handleScroll,{passive:true});return()=>window.removeEventListener('scroll',handleScroll);},[handleScroll]);// Track engagement time on page unload\nuseEffect(()=>{const handleBeforeUnload=()=>{const engagementTime=(Date.now()-startTimeRef.current)/1000;if(engagementTime>5){// Only track if user spent more than 5 seconds\ntrackEngagement(engagementTime,location.pathname);}};window.addEventListener('beforeunload',handleBeforeUnload);return()=>window.removeEventListener('beforeunload',handleBeforeUnload);},[location.pathname]);// Return analytics functions\nreturn{// Page tracking\ntrackPage:(path,title,params)=>trackPageView(path,title,params),// Event tracking\ntrackEvent:(eventName,params)=>trackEvent(eventName,params),// Zodiac-specific tracking\ntrackZodiacView:zodiacSign=>trackZodiacInteraction(zodiacSign,'view'),trackZodiacClick:(zodiacSign,element)=>trackZodiacInteraction(zodiacSign,'click',{element}),// Kubera Card tracking\ntrackKuberaCardView:()=>trackKuberaCardEvent('view_item'),trackKuberaCardAddToCart:price=>trackKuberaCardEvent('add_to_cart',{price}),trackKuberaCardPurchase:transactionData=>trackKuberaCardEvent('purchase',transactionData),// Form tracking\ntrackFormStart:formName=>trackEvent('form_start',{form_name:formName}),trackFormSubmit:(formName,data)=>trackFormSubmission(formName,data),trackFormError:(formName,error)=>trackError(\"Form error in \".concat(formName,\": \").concat(error),formName),// User interaction tracking\ntrackButtonClick:(buttonName,location)=>trackEvent('click',{event_category:'button',event_label:buttonName,button_location:location}),trackLinkClick:(linkText,destination)=>trackEvent('click',{event_category:'link',event_label:linkText,link_destination:destination}),// Media tracking\ntrackVideoPlay:videoName=>trackEvent('video_play',{event_category:'media',event_label:videoName}),trackAudioPlay:audioName=>trackEvent('audio_play',{event_category:'media',event_label:audioName}),// Error tracking\ntrackError:function(error,location){let fatal=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;return trackError(error,location,fatal);},// User properties\nsetUserProperty:properties=>setUserProperties(properties),// Custom events for this website\ntrackMantaRecitation:duration=>trackEvent('manta_recitation',{event_category:'spiritual_activity',value:Math.round(duration),duration_seconds:duration}),trackBlessingRequest:type=>trackEvent('blessing_request',{event_category:'spiritual_activity',blessing_type:type}),trackLanguageSwitch:(fromLang,toLang)=>trackEvent('language_switch',{event_category:'user_preference',from_language:fromLang,to_language:toLang}),// Ecommerce helpers\ntrackCheckoutStep:function(step){let option=arguments.length>1&&arguments[1]!==undefined?arguments[1]:'';return trackEvent('checkout_progress',{checkout_step:step,checkout_option:option});},trackPaymentMethod:method=>trackEvent('payment_method_selected',{event_category:'ecommerce',payment_method:method})};};/**\n * Hook for tracking component mount/unmount\n * @param {string} componentName - Name of the component\n */export const useComponentTracking=componentName=>{useEffect(()=>{trackEvent('component_mount',{event_category:'component_lifecycle',component_name:componentName});return()=>{trackEvent('component_unmount',{event_category:'component_lifecycle',component_name:componentName});};},[componentName]);};/**\n * Hook for tracking form interactions\n * @param {string} formName - Name of the form\n */export const useFormTracking=formName=>{const trackFieldFocus=useCallback(fieldName=>{trackEvent('form_field_focus',{event_category:'form_interaction',form_name:formName,field_name:fieldName});},[formName]);const trackFieldBlur=useCallback((fieldName,hasValue)=>{trackEvent('form_field_blur',{event_category:'form_interaction',form_name:formName,field_name:fieldName,has_value:hasValue});},[formName]);const trackValidationError=useCallback((fieldName,errorMessage)=>{trackEvent('form_validation_error',{event_category:'form_interaction',form_name:formName,field_name:fieldName,error_message:errorMessage});},[formName]);return{trackFieldFocus,trackFieldBlur,trackValidationError};};export default useAnalytics;", "map": {"version": 3, "names": ["useEffect", "useCallback", "useRef", "useLocation", "initGA", "trackPageView", "trackEvent", "trackZodiacInteraction", "trackKuberaCardEvent", "trackScrollDepth", "trackEngagement", "trackFormSubmission", "trackError", "setUserProperties", "useAnalytics", "options", "arguments", "length", "undefined", "location", "startTimeRef", "Date", "now", "scrollTrackedRef", "Set", "path", "pathname", "search", "fullPath", "current", "clear", "pageTitle", "includes", "zodiacSign", "replace", "zodiacNames", "concat", "referrer", "document", "user_language", "handleScroll", "scrollTop", "window", "pageYOffset", "documentElement", "documentHeight", "scrollHeight", "innerHeight", "scrollPercentage", "Math", "round", "milestones", "for<PERSON>ach", "milestone", "has", "add", "addEventListener", "passive", "removeEventListener", "handleBeforeUnload", "engagementTime", "trackPage", "title", "params", "eventName", "trackZodiacView", "trackZodiacClick", "element", "trackKuberaCardView", "trackKuberaCardAddToCart", "price", "trackKuberaCardPurchase", "transactionData", "trackFormStart", "formName", "form_name", "trackFormSubmit", "data", "trackFormError", "error", "trackButtonClick", "buttonName", "event_category", "event_label", "button_location", "trackLinkClick", "linkText", "destination", "link_destination", "trackVideoPlay", "videoName", "trackAudioPlay", "audioName", "fatal", "setUserProperty", "properties", "trackMantaRecitation", "duration", "value", "duration_seconds", "trackBlessingRequest", "type", "blessing_type", "trackLanguageSwitch", "fromLang", "toLang", "from_language", "to_language", "trackCheckoutStep", "step", "option", "checkout_step", "checkout_option", "trackPaymentMethod", "method", "payment_method", "useComponentTracking", "componentName", "component_name", "useFormTracking", "trackFieldFocus", "fieldName", "field_name", "trackFieldBlur", "hasValue", "has_value", "trackValidationError", "errorMessage", "error_message"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/hooks/useAnalytics.js"], "sourcesContent": ["/**\n * React Hook for Google Analytics Integration\n * Provides easy-to-use analytics functions for React components\n */\n\nimport { useEffect, useCallback, useRef } from 'react';\nimport { useLocation } from 'react-router-dom';\nimport {\n  initGA,\n  trackPageView,\n  trackEvent,\n  trackZodiacInteraction,\n  trackKuberaCardEvent,\n  trackScrollDepth,\n  trackEngagement,\n  trackFormSubmission,\n  trackError,\n  setUserProperties\n} from '../services/analytics';\n\n/**\n * Main analytics hook\n * @param {Object} options - Configuration options\n * @returns {Object} Analytics functions\n */\nexport const useAnalytics = (options = {}) => {\n  const location = useLocation();\n  const startTimeRef = useRef(Date.now());\n  const scrollTrackedRef = useRef(new Set());\n\n  // Initialize GA on mount\n  useEffect(() => {\n    initGA();\n  }, []);\n\n  // Track page views on route changes\n  useEffect(() => {\n    const path = location.pathname;\n    const search = location.search;\n    const fullPath = path + search;\n    \n    // Reset engagement tracking for new page\n    startTimeRef.current = Date.now();\n    scrollTrackedRef.current.clear();\n\n    // Determine page title based on path\n    let pageTitle = 'කුබේර දෙවියන්ගේ ආශීර්වාදය';\n    \n    if (path === '/') {\n      pageTitle = 'කුබේර දෙවියන්ගේ ආශීර්වාදය - මුල් පිටුව';\n    } else if (path.includes('/')) {\n      // Extract zodiac sign from path\n      const zodiacSign = path.replace('/', '');\n      const zodiacNames = {\n        'aries': 'මේෂ',\n        'taurus': 'වෘෂභ',\n        'gemini': 'මිථුන',\n        'cancer': 'කටක',\n        'leo': 'සිංහ',\n        'virgo': 'කන්‍යා',\n        'libra': 'තුලා',\n        'scorpio': 'වෘශ්චික',\n        'sagittarius': 'ධනු',\n        'capricorn': 'මකර',\n        'aquarius': 'කුම්භ',\n        'pisces': 'මීන'\n      };\n      \n      if (zodiacNames[zodiacSign]) {\n        pageTitle = `${zodiacNames[zodiacSign]} රාශිඵල - කුබේර දෙවියන්ගේ ආශීර්වාදය`;\n      }\n    }\n\n    // Track the page view\n    trackPageView(fullPath, pageTitle, {\n      referrer: document.referrer,\n      user_language: 'si'\n    });\n\n  }, [location]);\n\n  // Track scroll depth\n  const handleScroll = useCallback(() => {\n    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;\n    const documentHeight = document.documentElement.scrollHeight - window.innerHeight;\n    const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);\n\n    // Track scroll milestones\n    const milestones = [25, 50, 75, 90, 100];\n    milestones.forEach(milestone => {\n      if (scrollPercentage >= milestone && !scrollTrackedRef.current.has(milestone)) {\n        scrollTrackedRef.current.add(milestone);\n        trackScrollDepth(milestone, location.pathname);\n      }\n    });\n  }, [location.pathname]);\n\n  // Set up scroll tracking\n  useEffect(() => {\n    window.addEventListener('scroll', handleScroll, { passive: true });\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, [handleScroll]);\n\n  // Track engagement time on page unload\n  useEffect(() => {\n    const handleBeforeUnload = () => {\n      const engagementTime = (Date.now() - startTimeRef.current) / 1000;\n      if (engagementTime > 5) { // Only track if user spent more than 5 seconds\n        trackEngagement(engagementTime, location.pathname);\n      }\n    };\n\n    window.addEventListener('beforeunload', handleBeforeUnload);\n    return () => window.removeEventListener('beforeunload', handleBeforeUnload);\n  }, [location.pathname]);\n\n  // Return analytics functions\n  return {\n    // Page tracking\n    trackPage: (path, title, params) => trackPageView(path, title, params),\n    \n    // Event tracking\n    trackEvent: (eventName, params) => trackEvent(eventName, params),\n    \n    // Zodiac-specific tracking\n    trackZodiacView: (zodiacSign) => trackZodiacInteraction(zodiacSign, 'view'),\n    trackZodiacClick: (zodiacSign, element) => trackZodiacInteraction(zodiacSign, 'click', { element }),\n    \n    // Kubera Card tracking\n    trackKuberaCardView: () => trackKuberaCardEvent('view_item'),\n    trackKuberaCardAddToCart: (price) => trackKuberaCardEvent('add_to_cart', { price }),\n    trackKuberaCardPurchase: (transactionData) => trackKuberaCardEvent('purchase', transactionData),\n    \n    // Form tracking\n    trackFormStart: (formName) => trackEvent('form_start', { form_name: formName }),\n    trackFormSubmit: (formName, data) => trackFormSubmission(formName, data),\n    trackFormError: (formName, error) => trackError(`Form error in ${formName}: ${error}`, formName),\n    \n    // User interaction tracking\n    trackButtonClick: (buttonName, location) => trackEvent('click', {\n      event_category: 'button',\n      event_label: buttonName,\n      button_location: location\n    }),\n    \n    trackLinkClick: (linkText, destination) => trackEvent('click', {\n      event_category: 'link',\n      event_label: linkText,\n      link_destination: destination\n    }),\n    \n    // Media tracking\n    trackVideoPlay: (videoName) => trackEvent('video_play', {\n      event_category: 'media',\n      event_label: videoName\n    }),\n    \n    trackAudioPlay: (audioName) => trackEvent('audio_play', {\n      event_category: 'media',\n      event_label: audioName\n    }),\n    \n    // Error tracking\n    trackError: (error, location, fatal = false) => trackError(error, location, fatal),\n    \n    // User properties\n    setUserProperty: (properties) => setUserProperties(properties),\n    \n    // Custom events for this website\n    trackMantaRecitation: (duration) => trackEvent('manta_recitation', {\n      event_category: 'spiritual_activity',\n      value: Math.round(duration),\n      duration_seconds: duration\n    }),\n    \n    trackBlessingRequest: (type) => trackEvent('blessing_request', {\n      event_category: 'spiritual_activity',\n      blessing_type: type\n    }),\n    \n    trackLanguageSwitch: (fromLang, toLang) => trackEvent('language_switch', {\n      event_category: 'user_preference',\n      from_language: fromLang,\n      to_language: toLang\n    }),\n    \n    // Ecommerce helpers\n    trackCheckoutStep: (step, option = '') => trackEvent('checkout_progress', {\n      checkout_step: step,\n      checkout_option: option\n    }),\n    \n    trackPaymentMethod: (method) => trackEvent('payment_method_selected', {\n      event_category: 'ecommerce',\n      payment_method: method\n    })\n  };\n};\n\n/**\n * Hook for tracking component mount/unmount\n * @param {string} componentName - Name of the component\n */\nexport const useComponentTracking = (componentName) => {\n  useEffect(() => {\n    trackEvent('component_mount', {\n      event_category: 'component_lifecycle',\n      component_name: componentName\n    });\n\n    return () => {\n      trackEvent('component_unmount', {\n        event_category: 'component_lifecycle',\n        component_name: componentName\n      });\n    };\n  }, [componentName]);\n};\n\n/**\n * Hook for tracking form interactions\n * @param {string} formName - Name of the form\n */\nexport const useFormTracking = (formName) => {\n  const trackFieldFocus = useCallback((fieldName) => {\n    trackEvent('form_field_focus', {\n      event_category: 'form_interaction',\n      form_name: formName,\n      field_name: fieldName\n    });\n  }, [formName]);\n\n  const trackFieldBlur = useCallback((fieldName, hasValue) => {\n    trackEvent('form_field_blur', {\n      event_category: 'form_interaction',\n      form_name: formName,\n      field_name: fieldName,\n      has_value: hasValue\n    });\n  }, [formName]);\n\n  const trackValidationError = useCallback((fieldName, errorMessage) => {\n    trackEvent('form_validation_error', {\n      event_category: 'form_interaction',\n      form_name: formName,\n      field_name: fieldName,\n      error_message: errorMessage\n    });\n  }, [formName]);\n\n  return {\n    trackFieldFocus,\n    trackFieldBlur,\n    trackValidationError\n  };\n};\n\nexport default useAnalytics;\n"], "mappings": "AAAA;AACA;AACA;AACA,GAEA,OAASA,SAAS,CAAEC,WAAW,CAAEC,MAAM,KAAQ,OAAO,CACtD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OACEC,MAAM,CACNC,aAAa,CACbC,UAAU,CACVC,sBAAsB,CACtBC,oBAAoB,CACpBC,gBAAgB,CAChBC,eAAe,CACfC,mBAAmB,CACnBC,UAAU,CACVC,iBAAiB,KACZ,uBAAuB,CAE9B;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,YAAY,CAAG,QAAAA,CAAA,CAAkB,IAAjB,CAAAC,OAAO,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CACvC,KAAM,CAAAG,QAAQ,CAAGhB,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAAiB,YAAY,CAAGlB,MAAM,CAACmB,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,CACvC,KAAM,CAAAC,gBAAgB,CAAGrB,MAAM,CAAC,GAAI,CAAAsB,GAAG,CAAC,CAAC,CAAC,CAE1C;AACAxB,SAAS,CAAC,IAAM,CACdI,MAAM,CAAC,CAAC,CACV,CAAC,CAAE,EAAE,CAAC,CAEN;AACAJ,SAAS,CAAC,IAAM,CACd,KAAM,CAAAyB,IAAI,CAAGN,QAAQ,CAACO,QAAQ,CAC9B,KAAM,CAAAC,MAAM,CAAGR,QAAQ,CAACQ,MAAM,CAC9B,KAAM,CAAAC,QAAQ,CAAGH,IAAI,CAAGE,MAAM,CAE9B;AACAP,YAAY,CAACS,OAAO,CAAGR,IAAI,CAACC,GAAG,CAAC,CAAC,CACjCC,gBAAgB,CAACM,OAAO,CAACC,KAAK,CAAC,CAAC,CAEhC;AACA,GAAI,CAAAC,SAAS,CAAG,2BAA2B,CAE3C,GAAIN,IAAI,GAAK,GAAG,CAAE,CAChBM,SAAS,CAAG,wCAAwC,CACtD,CAAC,IAAM,IAAIN,IAAI,CAACO,QAAQ,CAAC,GAAG,CAAC,CAAE,CAC7B;AACA,KAAM,CAAAC,UAAU,CAAGR,IAAI,CAACS,OAAO,CAAC,GAAG,CAAE,EAAE,CAAC,CACxC,KAAM,CAAAC,WAAW,CAAG,CAClB,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,KAAK,CACf,KAAK,CAAE,MAAM,CACb,OAAO,CAAE,QAAQ,CACjB,OAAO,CAAE,MAAM,CACf,SAAS,CAAE,SAAS,CACpB,aAAa,CAAE,KAAK,CACpB,WAAW,CAAE,KAAK,CAClB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,KACZ,CAAC,CAED,GAAIA,WAAW,CAACF,UAAU,CAAC,CAAE,CAC3BF,SAAS,IAAAK,MAAA,CAAMD,WAAW,CAACF,UAAU,CAAC,wLAAqC,CAC7E,CACF,CAEA;AACA5B,aAAa,CAACuB,QAAQ,CAAEG,SAAS,CAAE,CACjCM,QAAQ,CAAEC,QAAQ,CAACD,QAAQ,CAC3BE,aAAa,CAAE,IACjB,CAAC,CAAC,CAEJ,CAAC,CAAE,CAACpB,QAAQ,CAAC,CAAC,CAEd;AACA,KAAM,CAAAqB,YAAY,CAAGvC,WAAW,CAAC,IAAM,CACrC,KAAM,CAAAwC,SAAS,CAAGC,MAAM,CAACC,WAAW,EAAIL,QAAQ,CAACM,eAAe,CAACH,SAAS,CAC1E,KAAM,CAAAI,cAAc,CAAGP,QAAQ,CAACM,eAAe,CAACE,YAAY,CAAGJ,MAAM,CAACK,WAAW,CACjF,KAAM,CAAAC,gBAAgB,CAAGC,IAAI,CAACC,KAAK,CAAET,SAAS,CAAGI,cAAc,CAAI,GAAG,CAAC,CAEvE;AACA,KAAM,CAAAM,UAAU,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAC,CACxCA,UAAU,CAACC,OAAO,CAACC,SAAS,EAAI,CAC9B,GAAIL,gBAAgB,EAAIK,SAAS,EAAI,CAAC9B,gBAAgB,CAACM,OAAO,CAACyB,GAAG,CAACD,SAAS,CAAC,CAAE,CAC7E9B,gBAAgB,CAACM,OAAO,CAAC0B,GAAG,CAACF,SAAS,CAAC,CACvC5C,gBAAgB,CAAC4C,SAAS,CAAElC,QAAQ,CAACO,QAAQ,CAAC,CAChD,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,CAACP,QAAQ,CAACO,QAAQ,CAAC,CAAC,CAEvB;AACA1B,SAAS,CAAC,IAAM,CACd0C,MAAM,CAACc,gBAAgB,CAAC,QAAQ,CAAEhB,YAAY,CAAE,CAAEiB,OAAO,CAAE,IAAK,CAAC,CAAC,CAClE,MAAO,IAAMf,MAAM,CAACgB,mBAAmB,CAAC,QAAQ,CAAElB,YAAY,CAAC,CACjE,CAAC,CAAE,CAACA,YAAY,CAAC,CAAC,CAElB;AACAxC,SAAS,CAAC,IAAM,CACd,KAAM,CAAA2D,kBAAkB,CAAGA,CAAA,GAAM,CAC/B,KAAM,CAAAC,cAAc,CAAG,CAACvC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGF,YAAY,CAACS,OAAO,EAAI,IAAI,CACjE,GAAI+B,cAAc,CAAG,CAAC,CAAE,CAAE;AACxBlD,eAAe,CAACkD,cAAc,CAAEzC,QAAQ,CAACO,QAAQ,CAAC,CACpD,CACF,CAAC,CAEDgB,MAAM,CAACc,gBAAgB,CAAC,cAAc,CAAEG,kBAAkB,CAAC,CAC3D,MAAO,IAAMjB,MAAM,CAACgB,mBAAmB,CAAC,cAAc,CAAEC,kBAAkB,CAAC,CAC7E,CAAC,CAAE,CAACxC,QAAQ,CAACO,QAAQ,CAAC,CAAC,CAEvB;AACA,MAAO,CACL;AACAmC,SAAS,CAAEA,CAACpC,IAAI,CAAEqC,KAAK,CAAEC,MAAM,GAAK1D,aAAa,CAACoB,IAAI,CAAEqC,KAAK,CAAEC,MAAM,CAAC,CAEtE;AACAzD,UAAU,CAAEA,CAAC0D,SAAS,CAAED,MAAM,GAAKzD,UAAU,CAAC0D,SAAS,CAAED,MAAM,CAAC,CAEhE;AACAE,eAAe,CAAGhC,UAAU,EAAK1B,sBAAsB,CAAC0B,UAAU,CAAE,MAAM,CAAC,CAC3EiC,gBAAgB,CAAEA,CAACjC,UAAU,CAAEkC,OAAO,GAAK5D,sBAAsB,CAAC0B,UAAU,CAAE,OAAO,CAAE,CAAEkC,OAAQ,CAAC,CAAC,CAEnG;AACAC,mBAAmB,CAAEA,CAAA,GAAM5D,oBAAoB,CAAC,WAAW,CAAC,CAC5D6D,wBAAwB,CAAGC,KAAK,EAAK9D,oBAAoB,CAAC,aAAa,CAAE,CAAE8D,KAAM,CAAC,CAAC,CACnFC,uBAAuB,CAAGC,eAAe,EAAKhE,oBAAoB,CAAC,UAAU,CAAEgE,eAAe,CAAC,CAE/F;AACAC,cAAc,CAAGC,QAAQ,EAAKpE,UAAU,CAAC,YAAY,CAAE,CAAEqE,SAAS,CAAED,QAAS,CAAC,CAAC,CAC/EE,eAAe,CAAEA,CAACF,QAAQ,CAAEG,IAAI,GAAKlE,mBAAmB,CAAC+D,QAAQ,CAAEG,IAAI,CAAC,CACxEC,cAAc,CAAEA,CAACJ,QAAQ,CAAEK,KAAK,GAAKnE,UAAU,kBAAAwB,MAAA,CAAkBsC,QAAQ,OAAAtC,MAAA,CAAK2C,KAAK,EAAIL,QAAQ,CAAC,CAEhG;AACAM,gBAAgB,CAAEA,CAACC,UAAU,CAAE9D,QAAQ,GAAKb,UAAU,CAAC,OAAO,CAAE,CAC9D4E,cAAc,CAAE,QAAQ,CACxBC,WAAW,CAAEF,UAAU,CACvBG,eAAe,CAAEjE,QACnB,CAAC,CAAC,CAEFkE,cAAc,CAAEA,CAACC,QAAQ,CAAEC,WAAW,GAAKjF,UAAU,CAAC,OAAO,CAAE,CAC7D4E,cAAc,CAAE,MAAM,CACtBC,WAAW,CAAEG,QAAQ,CACrBE,gBAAgB,CAAED,WACpB,CAAC,CAAC,CAEF;AACAE,cAAc,CAAGC,SAAS,EAAKpF,UAAU,CAAC,YAAY,CAAE,CACtD4E,cAAc,CAAE,OAAO,CACvBC,WAAW,CAAEO,SACf,CAAC,CAAC,CAEFC,cAAc,CAAGC,SAAS,EAAKtF,UAAU,CAAC,YAAY,CAAE,CACtD4E,cAAc,CAAE,OAAO,CACvBC,WAAW,CAAES,SACf,CAAC,CAAC,CAEF;AACAhF,UAAU,CAAE,QAAAA,CAACmE,KAAK,CAAE5D,QAAQ,KAAE,CAAA0E,KAAK,CAAA7E,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,KAAK,OAAK,CAAAJ,UAAU,CAACmE,KAAK,CAAE5D,QAAQ,CAAE0E,KAAK,CAAC,GAElF;AACAC,eAAe,CAAGC,UAAU,EAAKlF,iBAAiB,CAACkF,UAAU,CAAC,CAE9D;AACAC,oBAAoB,CAAGC,QAAQ,EAAK3F,UAAU,CAAC,kBAAkB,CAAE,CACjE4E,cAAc,CAAE,oBAAoB,CACpCgB,KAAK,CAAEjD,IAAI,CAACC,KAAK,CAAC+C,QAAQ,CAAC,CAC3BE,gBAAgB,CAAEF,QACpB,CAAC,CAAC,CAEFG,oBAAoB,CAAGC,IAAI,EAAK/F,UAAU,CAAC,kBAAkB,CAAE,CAC7D4E,cAAc,CAAE,oBAAoB,CACpCoB,aAAa,CAAED,IACjB,CAAC,CAAC,CAEFE,mBAAmB,CAAEA,CAACC,QAAQ,CAAEC,MAAM,GAAKnG,UAAU,CAAC,iBAAiB,CAAE,CACvE4E,cAAc,CAAE,iBAAiB,CACjCwB,aAAa,CAAEF,QAAQ,CACvBG,WAAW,CAAEF,MACf,CAAC,CAAC,CAEF;AACAG,iBAAiB,CAAE,QAAAA,CAACC,IAAI,KAAE,CAAAC,MAAM,CAAA9F,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,OAAK,CAAAV,UAAU,CAAC,mBAAmB,CAAE,CACxEyG,aAAa,CAAEF,IAAI,CACnBG,eAAe,CAAEF,MACnB,CAAC,CAAC,GAEFG,kBAAkB,CAAGC,MAAM,EAAK5G,UAAU,CAAC,yBAAyB,CAAE,CACpE4E,cAAc,CAAE,WAAW,CAC3BiC,cAAc,CAAED,MAClB,CAAC,CACH,CAAC,CACH,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,oBAAoB,CAAIC,aAAa,EAAK,CACrDrH,SAAS,CAAC,IAAM,CACdM,UAAU,CAAC,iBAAiB,CAAE,CAC5B4E,cAAc,CAAE,qBAAqB,CACrCoC,cAAc,CAAED,aAClB,CAAC,CAAC,CAEF,MAAO,IAAM,CACX/G,UAAU,CAAC,mBAAmB,CAAE,CAC9B4E,cAAc,CAAE,qBAAqB,CACrCoC,cAAc,CAAED,aAClB,CAAC,CAAC,CACJ,CAAC,CACH,CAAC,CAAE,CAACA,aAAa,CAAC,CAAC,CACrB,CAAC,CAED;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAE,eAAe,CAAI7C,QAAQ,EAAK,CAC3C,KAAM,CAAA8C,eAAe,CAAGvH,WAAW,CAAEwH,SAAS,EAAK,CACjDnH,UAAU,CAAC,kBAAkB,CAAE,CAC7B4E,cAAc,CAAE,kBAAkB,CAClCP,SAAS,CAAED,QAAQ,CACnBgD,UAAU,CAAED,SACd,CAAC,CAAC,CACJ,CAAC,CAAE,CAAC/C,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAiD,cAAc,CAAG1H,WAAW,CAAC,CAACwH,SAAS,CAAEG,QAAQ,GAAK,CAC1DtH,UAAU,CAAC,iBAAiB,CAAE,CAC5B4E,cAAc,CAAE,kBAAkB,CAClCP,SAAS,CAAED,QAAQ,CACnBgD,UAAU,CAAED,SAAS,CACrBI,SAAS,CAAED,QACb,CAAC,CAAC,CACJ,CAAC,CAAE,CAAClD,QAAQ,CAAC,CAAC,CAEd,KAAM,CAAAoD,oBAAoB,CAAG7H,WAAW,CAAC,CAACwH,SAAS,CAAEM,YAAY,GAAK,CACpEzH,UAAU,CAAC,uBAAuB,CAAE,CAClC4E,cAAc,CAAE,kBAAkB,CAClCP,SAAS,CAAED,QAAQ,CACnBgD,UAAU,CAAED,SAAS,CACrBO,aAAa,CAAED,YACjB,CAAC,CAAC,CACJ,CAAC,CAAE,CAACrD,QAAQ,CAAC,CAAC,CAEd,MAAO,CACL8C,eAAe,CACfG,cAAc,CACdG,oBACF,CAAC,CACH,CAAC,CAED,cAAe,CAAAhH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}