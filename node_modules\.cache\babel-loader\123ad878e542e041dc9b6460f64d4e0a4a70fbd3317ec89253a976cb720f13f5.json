{"ast": null, "code": "import React,{useEffect}from'react';import ParticleBackground from'./ParticleBackground';import KuberaAnimation from'./KuberaAnimation';import KuberaCardSection from'./KuberaCardSection';import{useAnalytics,useComponentTracking}from'../hooks/useAnalytics';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LandingPage=()=>{const analytics=useAnalytics();// Track component mounting\nuseComponentTracking('LandingPage');useEffect(()=>{// Track landing page view with additional context\nanalytics.trackEvent('landing_page_loaded',{event_category:'page_interaction',page_type:'landing',content_type:'kubera_guide'});// Add floating animation to content cards with staggered delay\nconst cards=document.querySelectorAll('.kubera-content-card');cards.forEach((card,index)=>{card.style.animationDelay=\"\".concat(index*0.2,\"s\");card.classList.add('floating');// Track card visibility\nconst observer=new IntersectionObserver(entries=>{entries.forEach(entry=>{if(entry.isIntersecting){analytics.trackEvent('content_card_viewed',{event_category:'content_engagement',card_index:index,card_delay:index*0.2});observer.unobserve(entry.target);}});},{threshold:0.5});observer.observe(card);});},[analytics]);return/*#__PURE__*/_jsxs(\"div\",{className:\"landing-page kubera-guide-page\",children:[/*#__PURE__*/_jsx(ParticleBackground,{}),/*#__PURE__*/_jsx(KuberaAnimation,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"landing-header\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"main-title\",children:\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DDA \\u0DB6\\u0DBD\\u0DBA\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"subtitle\",children:\"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DDA \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DD6\\u0DBB\\u0DCA\\u0DAB \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C\\u0DDD\\u0DB4\\u0DAF\\u0DDA\\u0DC1\\u0DBA\"}),/*#__PURE__*/_jsx(\"div\",{className:\"divine-blessing\",children:/*#__PURE__*/_jsx(\"span\",{className:\"blessing-text\",children:\"\\uD83D\\uDE4F \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\uD83D\\uDE4F\"})})]}),/*#__PURE__*/_jsx(KuberaCardSection,{}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-content-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kubera-content-card dark-glass-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-shine\"}),/*#__PURE__*/_jsx(\"div\",{className:\"content-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"content-title\",children:\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0DDA \\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA\\u0D9A\\u0DB8\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-body\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DC3\\u0DD1\\u0DB8 \\u0DB4\\u0DD4\\u0DAF\\u0DCA\\u0D9C\\u0DBD\\u0DBA\\u0D9A\\u0DD4\\u0D9C\\u0DDA\\u0DB8 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DC0\\u0DBB\\u0DAD\\u0DCA\\u0DC0\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DDE\\u0DB7\\u0DCF\\u0D9C\\u0DCA\\u200D\\u0DBA\\u0DBA \\u0DBA\\u0DB1\\u0DD4 \\u0D89\\u0DAD\\u0DCF \\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA \\u0D85\\u0D82\\u0D9C\\u0DBA\\u0D9A\\u0DD2. \\u0DC0\\u0DDB\\u0DAF\\u0DD2\\u0D9A \\u0DC3\\u0DC4 \\u0DC4\\u0DD2\\u0DB1\\u0DCA\\u0DAF\\u0DD4 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DB1\\u0DCA\\u0DA7 \\u0D85\\u0DB1\\u0DD4\\u0DC0, \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0\\u0DDA \\u0D87\\u0DAD\\u0DD2 \\u0DB0\\u0DB1\\u0DBA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DC3\\u0DC4 \\u0D91\\u0DC4\\u0DD2 \\u0DB7\\u0DCF\\u0DBB\\u0D9A\\u0DBB\\u0DD4 \\u0DC0\\u0DB1\\u0DCA\\u0DB1\\u0DDA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0DBA.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DBD\\u0DB6\\u0DCF \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DB0\\u0DB1 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DAD\\u0DCA, \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0DA2\\u0DD3\\u0DC0\\u0DD2\\u0DAD\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DB6\\u0DC0\\u0DA7 \\u0D9C\\u0DD0\\u0DB9\\u0DD4\\u0DBB\\u0DD4 \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0\\u0DCF\\u0DC3\\u0DBA\\u0D9A\\u0DCA \\u0DB4\\u0DC0\\u0DAD\\u0DD3.\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-content-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kubera-content-card dark-glass-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-shine\"}),/*#__PURE__*/_jsx(\"div\",{className:\"content-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"content-title\",children:\"\\u0D9A\\u0DC0\\u0DD4\\u0DAF \\u0DB8\\u0DDA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA?\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"content-body\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DC4\\u0DD2\\u0DB1\\u0DCA\\u0DAF\\u0DD4 \\u0DAF\\u0DDA\\u0DC0 \\u0DB4\\u0DD4\\u0DBB\\u0DCF\\u0DAB\\u0DBA\\u0DA7 \\u0D85\\u0DB1\\u0DD4\\u0DC0, \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DBA\\u0DB1\\u0DCA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2, \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DB7\\u0DCF\\u0DAB\\u0DCA\\u0DA9\\u0DCF\\u0D9C\\u0DCF\\u0DBB\\u0DD2\\u0D9A \\u0DC3\\u0DC4 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DDA \\u0D86\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0D9A\\u0DBA\\u0DCF (\\u0DAF\\u0DD2\\u0D9A\\u0DCA\\u0DB4\\u0DCF\\u0DBD) \\u0DBD\\u0DD9\\u0DC3 \\u0DC3\\u0DD0\\u0DBD\\u0D9A\\u0DDA.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DD3 \\u0DBD\\u0DCF\\u0D82\\u0D9A\\u0DD2\\u0D9A \\u0D85\\u0DB4\\u0DA7 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0DC0\\u0DA9\\u0DCF\\u0DAD\\u0DCA \\u0DC3\\u0DB8\\u0DD3\\u0DB4 \\u0DA0\\u0DBB\\u0DD2\\u0DAD\\u0DBA\\u0D9A\\u0DD2, \\u0DB8\\u0DB1\\u0DCA\\u0DAF \\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA \\u0DBD\\u0D82\\u0D9A\\u0DCF\\u0DB4\\u0DD4\\u0DBB\\u0DDA \\u0DBB\\u0DCF\\u0DC0\\u0DAB \\u0DBB\\u0DA2\\u0DD4\\u0D9C\\u0DDA \\u0D85\\u0DBB\\u0DCA\\u0DB0 \\u0DC3\\u0DC4\\u0DDD\\u0DAF\\u0DBB\\u0DBA\\u0DCF \\u0DBD\\u0DD9\\u0DC3\\u0DAF \\u0DC3\\u0DD0\\u0DBD\\u0D9A\\u0DD9\\u0DB1 \\u0DB6\\u0DD0\\u0DC0\\u0DD2\\u0DB1\\u0DD2. \\u0DB6\\u0DDE\\u0DAF\\u0DCA\\u0DB0 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DDA\\u0DAF\\u0DD3 \\u0D91\\u0DAD\\u0DD4\\u0DB8\\u0DB1\\u0DCA \\\"\\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB\\\" (\\u0DC0\\u0DD9\\u0DC3\\u0DB8\\u0DD4\\u0DAB\\u0DD2) \\u0DBD\\u0DD9\\u0DC3 \\u0DC4\\u0DB3\\u0DD4\\u0DB1\\u0DCA\\u0DC0\\u0DB1\\u0DD4 \\u0DBD\\u0DB6\\u0DB1 \\u0D85\\u0DAD\\u0DBB, \\u0DC3\\u0DAD\\u0DBB\\u0DC0\\u0DBB\\u0DB8\\u0DCA \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DC0\\u0DBB\\u0DD4\\u0DB1\\u0DCA\\u0D9C\\u0DD9\\u0DB1\\u0DCA \\u0D9A\\u0DD9\\u0DB1\\u0DD9\\u0D9A\\u0DD4 \\u0DBD\\u0DD9\\u0DC3 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2\\u0DAD\\u0DCA\\u0DC0\\u0DBA \\u0DAF\\u0DBB\\u0DBA\\u0DD2.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DBA\\u0DB1\\u0DD4 \\u0D9A\\u0DD1\\u0DAF\\u0DBB \\u0DBD\\u0DD9\\u0DC3 \\u0DB0\\u0DB1\\u0DBA \\u0DBB\\u0DD0\\u0DC3\\u0DCA \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1\\u0DD9\\u0D9A\\u0DD4 \\u0DB1\\u0DDC\\u0DC0, \\u0DB0\\u0DCF\\u0DBB\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DC0 \\u0D8B\\u0DB4\\u0DBA\\u0DB1 \\u0DBD\\u0DAF \\u0DB0\\u0DB1\\u0DBA \\u0DBD\\u0DDD\\u0D9A\\u0DBA\\u0DA7 \\u0DB6\\u0DD9\\u0DAF\\u0DCF\\u0DC4\\u0DBB\\u0DD2\\u0DB1 \\u0DB4\\u0DCF\\u0DBD\\u0D9A\\u0DBA\\u0DD9\\u0D9A\\u0DD2.\"})]})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-content-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kubera-content-card dark-glass-card mantra-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-shine\"}),/*#__PURE__*/_jsx(\"div\",{className:\"content-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"content-title\",children:\"\\u0DB6\\u0DBD\\u0D9C\\u0DAD\\u0DD4 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DC3\\u0DC4 \\u0D91\\u0DC4\\u0DD2 \\u0DAD\\u0DDA\\u0DBB\\u0DD4\\u0DB8\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"content-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"mantra-section\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"mantra-subtitle\",children:\"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA (\\u0DC3\\u0D82\\u0DC3\\u0DCA\\u0D9A\\u0DD8\\u0DAD):\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"sanskrit-mantra\",children:[\"\\u0950 \\u092F\\u0915\\u094D\\u0937\\u093E\\u092F \\u0915\\u0941\\u092C\\u0947\\u0930\\u093E\\u092F \\u0935\\u0948\\u0936\\u094D\\u0930\\u0935\\u0923\\u093E\\u092F \\u0927\\u0928\\u0927\\u093E\\u0928\\u094D\\u092F\\u093E\\u0927\\u093F\\u092A\\u0924\\u092F\\u0947\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u0927\\u0928\\u0927\\u093E\\u0928\\u094D\\u092F\\u0938\\u092E\\u0943\\u0926\\u094D\\u0927\\u093F\\u0902 \\u092E\\u0947 \\u0926\\u0947\\u0939\\u093F \\u0926\\u093E\\u092A\\u092F \\u0938\\u094D\\u0935\\u093E\\u0939\\u093E \\u0965\"]}),/*#__PURE__*/_jsx(\"h4\",{className:\"mantra-subtitle\",children:\"\\u0D8B\\u0DA0\\u0DCA\\u0DA0\\u0DCF\\u0DBB\\u0DAB\\u0DBA \\u0DC3\\u0DB3\\u0DC4\\u0DCF (\\u0DC3\\u0DD2\\u0D82\\u0DC4\\u0DBD):\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"sinhala-pronunciation\",children:[\"\\u0D95\\u0DB8\\u0DCA \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DCF\\u0DBA \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB\\u0DCF\\u0DBA \\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB\\u0DCF\\u0DBA \\u0DB0\\u0DB1\\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DBA\\u0DDA,\",/*#__PURE__*/_jsx(\"br\",{}),\"\\u0DB0\\u0DB1\\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DB8\\u0DCA \\u0DB8\\u0DDA \\u0DAF\\u0DDA\\u0DC4\\u0DD2 \\u0DAF\\u0DCF\\u0DB4\\u0DBA \\u0DC3\\u0DCA\\u0DC0\\u0DCF\\u0DC4\\u0DCF \\u0965\"]}),/*#__PURE__*/_jsx(\"h4\",{className:\"mantra-subtitle\",children:\"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DDA \\u0DC3\\u0DBB\\u0DBD \\u0D85\\u0DBB\\u0DCA\\u0DAE\\u0DBA:\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mantra-meaning\",children:\"\\\"\\u0D95\\u0DB8\\u0DCA, \\u0DBA\\u0D9A\\u0DCA\\u0DC2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DBB\\u0DA2\\u0DD4 \\u0DC0\\u0DD6\\u0DAD\\u0DCA, \\u0DB0\\u0DB1\\u0DBA\\u0DA7 \\u0DC3\\u0DC4 \\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DBA\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DC0\\u0DD6\\u0DAD\\u0DCA, \\u0DC0\\u0DDB\\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DC0\\u0DAB \\u0DBD\\u0DD9\\u0DC3\\u0DAF \\u0DC4\\u0DD0\\u0DB3\\u0DD2\\u0DB1\\u0DCA\\u0DC0\\u0DD9\\u0DB1 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DD2, \\u0D94\\u0DB6 \\u0DC0\\u0DC4\\u0DB1\\u0DCA\\u0DC3\\u0DDA\\u0DA7 \\u0DB8\\u0DB8 \\u0DB1\\u0DB8\\u0DC3\\u0DCA\\u0D9A\\u0DCF\\u0DBB \\u0D9A\\u0DBB\\u0DB8\\u0DD2. \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DCF\\u0D9A\\u0DBB \\u0DB8\\u0DA7 \\u0DB0\\u0DB1\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DC4 \\u0DB0\\u0DCF\\u0DB1\\u0DCA\\u200D\\u0DBA\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0DBD\\u0DB6\\u0DCF \\u0DAF\\u0DD9\\u0DB1\\u0DD4 \\u0DB8\\u0DD0\\u0DB1\\u0DC0.\\\"\"})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-content-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kubera-content-card dark-glass-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-shine\"}),/*#__PURE__*/_jsx(\"div\",{className:\"content-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"content-title\",children:\"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DB1\\u0DD2\\u0DC0\\u0DD0\\u0DBB\\u0DAF\\u0DD2\\u0DC0 \\u0DB7\\u0DCF\\u0DC0\\u0DD2\\u0DAD \\u0D9A\\u0DBB\\u0DB1 \\u0D86\\u0D9A\\u0DCF\\u0DBB\\u0DBA\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"content-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"usage-guidelines\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"guideline-item\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"guideline-title\",children:\"1. \\u0DC3\\u0DD4\\u0DAF\\u0DD4\\u0DC3\\u0DD4\\u0DB8 \\u0DC0\\u0DDA\\u0DBD\\u0DCF\\u0DC0:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DAF\\u0DD2\\u0DB1\\u0DB4\\u0DAD\\u0DCF \\u0D8B\\u0DAF\\u0DD1\\u0DC3\\u0DB1 \\u0DC3\\u0DCA\\u0DB1\\u0DCF\\u0DB1\\u0DBA \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D85\\u0DB1\\u0DAD\\u0DD4\\u0DBB\\u0DD4\\u0DC0 \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0DC0 \\u0DC4\\u0DDD \\u0DC3\\u0DB1\\u0DCA\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF \\u0D9A\\u0DCF\\u0DBD\\u0DDA\\u0DAF\\u0DD3 \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC0\\u0DA9\\u0DCF\\u0DAD\\u0DCA \\u0DC3\\u0DD4\\u0DAF\\u0DD4\\u0DC3\\u0DD4\\u0DBA. \\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0DB6\\u0DCA\\u200D\\u0DBB\\u0DC4\\u0DCA\\u0DB8 \\u0DB8\\u0DD4\\u0DC4\\u0DD4\\u0DBB\\u0DCA\\u0DAD\\u0DBA (\\u0D85\\u0DBD\\u0DD4\\u0DBA\\u0DB8 4:30 - 5:30 \\u0DB4\\u0DB8\\u0DAB) \\u0D89\\u0DAD\\u0DCF \\u0DB6\\u0DBD\\u0D9C\\u0DAD\\u0DD4\\u0DBA.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"guideline-item\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"guideline-title\",children:\"2. \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DB1\\u0DBA:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DB1\\u0DD2\\u0DC0\\u0DC3\\u0DDA \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4, \\u0DB1\\u0DD2\\u0DC3\\u0DCA\\u0D9A\\u0DBD\\u0D82\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DB1\\u0DBA\\u0D9A\\u0DCA \\u0DAD\\u0DDD\\u0DBB\\u0DCF\\u0D9C\\u0DB1\\u0DCA\\u0DB1. \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DB1\\u0DB8\\u0DCA \\u0DB4\\u0DD6\\u0DA2\\u0DCF\\u0DC3\\u0DB1\\u0DBA\\u0D9A\\u0DCA \\u0DC3\\u0D9A\\u0DC3\\u0DCF \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0DBB\\u0DD6\\u0DB4\\u0DBA\\u0D9A\\u0DCA \\u0DC4\\u0DDD \\u0DBA\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA\\u0D9A\\u0DCA \\u0DAD\\u0DB6\\u0DCF \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DB8\\u0DB1\\u0DC3 \\u0D92\\u0D9A\\u0DCF\\u0D9C\\u0DCA\\u200D\\u0DBB \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8 \\u0DB4\\u0DC4\\u0DC3\\u0DD4\\u0DBA.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"guideline-item\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"guideline-title\",children:\"3. \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DC1\\u0DCF\\u0DBB\\u0DD3\\u0DBB\\u0DD2\\u0D9A \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8 \\u0DB8\\u0DD9\\u0DB1\\u0DCA\\u0DB8 \\u0DB8\\u0DCF\\u0DB1\\u0DC3\\u0DD2\\u0D9A \\u0DB4\\u0DD2\\u0DBB\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4\\u0D9A\\u0DB8\\u0DAF \\u0D85\\u0DAD\\u0DD2\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA\\u0DBA. \\u0D9A\\u0DD2\\u0DC3\\u0DD2\\u0DAF\\u0DD4 \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DDD\\u0DB0\\u0DBA\\u0D9A\\u0DCA, \\u0DC0\\u0DDB\\u0DBB\\u0DBA\\u0D9A\\u0DCA \\u0DC4\\u0DDD \\u0DB1\\u0DD2\\u0DC2\\u0DDA\\u0DB0\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0D9A \\u0DC3\\u0DD2\\u0DAD\\u0DD4\\u0DC0\\u0DD2\\u0DBD\\u0DCA\\u0DBD\\u0D9A\\u0DCA \\u0DC3\\u0DD2\\u0DAD\\u0DDA \\u0DAD\\u0DB6\\u0DCF \\u0DB1\\u0DDC\\u0D9C\\u0DD9\\u0DB1, \\u0DC3\\u0DD0\\u0DC4\\u0DD0\\u0DBD\\u0DCA\\u0DBD\\u0DD4 \\u0DB8\\u0DB1\\u0DC3\\u0D9A\\u0DD2\\u0DB1\\u0DCA \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0D86\\u0DBB\\u0DB8\\u0DCA\\u0DB7 \\u0D9A\\u0DBB\\u0DB1\\u0DCA\\u0DB1.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"guideline-item\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"guideline-title\",children:\"4. \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0D85\\u0DB0\\u0DD2\\u0DB4\\u0DAD\\u0DD2 \\u0DB1\\u0DD2\\u0DC3\\u0DCF, \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DBB\\u0DB1 \\u0DC0\\u0DD2\\u0DA7 \\u0D8B\\u0DAD\\u0DD4\\u0DBB\\u0DD4 \\u0DAF\\u0DD2\\u0DC1\\u0DCF\\u0DC0\\u0DA7 \\u0DB8\\u0DD4\\u0DC4\\u0DD4\\u0DAB\\u0DBD\\u0DCF \\u0DC0\\u0DCF\\u0DA9\\u0DD2 \\u0DC0\\u0DD3\\u0DB8 \\u0D89\\u0DAD\\u0DCF \\u0DBA\\u0DDD\\u0D9C\\u0DCA\\u200D\\u0DBA\\u0DBA.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"guideline-item\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"guideline-title\",children:\"5. \\u0DA2\\u0DB4 \\u0D9A\\u0DBB\\u0DB1 \\u0DC0\\u0DCF\\u0DBB \\u0D9C\\u0DAB\\u0DB1:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DC3\\u0DCA\\u0DB5\\u0DA7\\u0DD2\\u0D9A, \\u0DBB\\u0DD4\\u0DAF\\u0DCA\\u200D\\u0DBB\\u0DCF\\u0D9A\\u0DCA\\u0DC2 \\u0DC4\\u0DDD \\u0DAD\\u0DD4\\u0DBD\\u0DCA\\u0DC3\\u0DD2 (\\u0DB8\\u0DAF\\u0DD4\\u0DBB\\u0DD4\\u0DAD\\u0DBD\\u0DCF) \\u0D87\\u0DA7\\u0DC0\\u0DBD\\u0DD2\\u0DB1\\u0DCA \\u0DC3\\u0DD0\\u0DAF\\u0DD6 \\u0DA2\\u0DB4\\u0DB8\\u0DCF\\u0DBD\\u0DBA\\u0D9A\\u0DCA \\u0DB7\\u0DCF\\u0DC0\\u0DD2\\u0DAD \\u0D9A\\u0DBB 108 \\u0DC0\\u0DAD\\u0DCF\\u0DC0\\u0D9A\\u0DCA \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC3\\u0DB8\\u0DCA\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCF\\u0DBA\\u0DBA\\u0DD2. \\u0D86\\u0DBB\\u0DB8\\u0DCA\\u0DB7\\u0DDA\\u0DAF\\u0DD3 \\u0D94\\u0DB6\\u0DA7 \\u0DC4\\u0DD0\\u0D9A\\u0DD2 \\u0DC0\\u0DCF\\u0DBB \\u0D9C\\u0DAB\\u0DB1\\u0D9A\\u0DCA (\\u0D8B\\u0DAF\\u0DCF: 9, 27, 54) \\u0DA2\\u0DB4 \\u0D9A\\u0DBB \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0DD9\\u0DB1\\u0DCA 108 \\u0DAF\\u0D9A\\u0DCA\\u0DC0\\u0DCF \\u0DC0\\u0DD0\\u0DA9\\u0DD2 \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A.\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"guideline-item\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"guideline-title\",children:\"6. \\u0DB4\\u0DD6\\u0DA2\\u0DCF\\u0DC0:\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DA7 \\u0DB4\\u0DD9\\u0DBB \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA \\u0D8B\\u0DAF\\u0DD9\\u0DC3\\u0DCF \\u0DB4\\u0DC4\\u0DB1\\u0D9A\\u0DCA \\u0DAF\\u0DD0\\u0DBD\\u0DCA\\u0DC0\\u0DD3\\u0DB8, \\u0DC3\\u0DD4\\u0DC0\\u0DB3 \\u0DC4\\u0DB3\\u0DD4\\u0DB1\\u0DCA\\u0D9A\\u0DD6\\u0DBB\\u0D9A\\u0DCA \\u0DB4\\u0DAD\\u0DCA\\u0DAD\\u0DD4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8 \\u0DC3\\u0DC4 \\u0DB1\\u0DD0\\u0DC0\\u0DD4\\u0DB8\\u0DCA \\u0DB8\\u0DBD\\u0DCA \\u0D9A\\u0DD2\\u0DC4\\u0DD2\\u0DB4\\u0DBA\\u0D9A\\u0DCA \\u0DB4\\u0DD6\\u0DA2\\u0DCF \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DB7\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCA\\u0DB0\\u0DCF\\u0DC0 \\u0DB4\\u0DCA\\u200D\\u0DBB\\u0D9A\\u0DCF\\u0DC1 \\u0D9A\\u0DC5 \\u0DC4\\u0DD0\\u0D9A\\u0DD2\\u0DBA.\"})]})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-content-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kubera-content-card dark-glass-card benefits-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-shine\"}),/*#__PURE__*/_jsx(\"div\",{className:\"content-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"content-title\",children:\"\\u0DB4\\u0DCA\\u200D\\u0DBB\\u0DAD\\u0DD2\\u0DBD\\u0DCF\\u0DB7 \\u0DC3\\u0DC4 \\u0DB1\\u0DD2\\u0DC0\\u0DD0\\u0DBB\\u0DAF\\u0DD2 \\u0DB8\\u0DCF\\u0DB1\\u0DC3\\u0DD2\\u0D9A \\u0D86\\u0D9A\\u0DBD\\u0DCA\\u0DB4\\u0DBA\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"content-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"benefits-list\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"benefit-icon\",children:\"\\uD83D\\uDCB0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0DC0\\u0DD3\\u0DB8\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DBB\\u0DD0\\u0D9A\\u0DD2\\u0DBA\\u0DCF\\u0DC0\\u0DDA, \\u0DC0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DB4\\u0DCF\\u0DBB\\u0DDA \\u0DC4\\u0DDD \\u0DC0\\u0DD9\\u0DB1\\u0DAD\\u0DCA \\u0D86\\u0DAF\\u0DCF\\u0DBA\\u0DB8\\u0DCA \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C\\u0DC0\\u0DBD \\u0D87\\u0DAD\\u0DD2 \\u0DB6\\u0DCF\\u0DB0\\u0D9A \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0DD9\\u0DB1\\u0DCA \\u0D89\\u0DC0\\u0DAD\\u0DCA \\u0DC0\\u0DD3 \\u0DBA\\u0DC4\\u0DB4\\u0DAD \\u0D8B\\u0DAF\\u0DCF\\u0DC0\\u0DDA.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"benefit-icon\",children:\"\\uD83C\\uDF1F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0DB1\\u0DC0 \\u0D86\\u0DAF\\u0DCF\\u0DBA\\u0DB8\\u0DCA \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C \\u0DC0\\u0DD2\\u0DC0\\u0DD8\\u0DAD \\u0DC0\\u0DD3\\u0DB8\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DB0\\u0DB1\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0DC0\\u0DD3\\u0DB8\\u0DA7 \\u0DB1\\u0DC0 \\u0D85\\u0DC0\\u0DC3\\u0DCA\\u0DAE\\u0DCF \\u0DC3\\u0DC4 \\u0DB8\\u0DCF\\u0DBB\\u0DCA\\u0D9C \\u0DC0\\u0DD2\\u0DC0\\u0DD8\\u0DAD\\u0DC0\\u0DDA.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"benefit-icon\",children:\"\\uD83C\\uDFE6\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0DAB\\u0DBA \\u0DB6\\u0DBB\\u0DD2\\u0DB1\\u0DCA \\u0DB8\\u0DD2\\u0DAF\\u0DD3\\u0DB8\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0DC3\\u0DCA\\u0DAE\\u0DCF\\u0DC0\\u0DBB\\u0DAD\\u0DCA\\u0DC0\\u0DBA\\u0D9A\\u0DCA \\u0D87\\u0DAD\\u0DD2\\u0DC0\\u0DD3\\u0DB8 \\u0DB1\\u0DD2\\u0DC3\\u0DCF \\u0DAB\\u0DBA\\u0DAD\\u0DD4\\u0DBB\\u0DD4\\u0DC3\\u0DCA \\u0DC0\\u0DBD\\u0DD2\\u0DB1\\u0DCA \\u0DB1\\u0DD2\\u0DAF\\u0DC4\\u0DC3\\u0DCA \\u0DC0\\u0DD3\\u0DB8\\u0DA7 \\u0DB8\\u0D9C \\u0DB4\\u0DD1\\u0DAF\\u0DDA.\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-item\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"benefit-icon\",children:\"\\uD83D\\uDEE1\\uFE0F\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"benefit-content\",children:[/*#__PURE__*/_jsx(\"h4\",{children:\"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DD4\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0DD2\\u0DAD \\u0DC0\\u0DD3\\u0DB8\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0D8B\\u0DB4\\u0DBA\\u0DB1 \\u0DBD\\u0DAF \\u0DB0\\u0DB1\\u0DBA \\u0D85\\u0DB1\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA \\u0DBD\\u0DD9\\u0DC3 \\u0DC0\\u0DD2\\u0DBA\\u0DAF\\u0DB8\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DD3 \\u0D89\\u0DAD\\u0DD2\\u0DBB\\u0DD2 \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DA7 \\u0DC3\\u0DC4 \\u0DC0\\u0DBB\\u0DCA\\u0DB0\\u0DB1\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DA7 \\u0D85\\u0DC0\\u0DC1\\u0DCA\\u200D\\u0DBA \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DBD\\u0DD0\\u0DB6\\u0DDA.\"})]})]})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-content-section\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"kubera-content-card dark-glass-card important-notes-card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-glow\"}),/*#__PURE__*/_jsx(\"div\",{className:\"card-shine\"}),/*#__PURE__*/_jsx(\"div\",{className:\"content-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"content-title\",children:\"\\u0DC0\\u0DD0\\u0DAF\\u0D9C\\u0DAD\\u0DCA\\u0DB8 \\u0DAF\\u0DDA\"})}),/*#__PURE__*/_jsx(\"div\",{className:\"content-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"important-note\",children:[/*#__PURE__*/_jsx(\"p\",{children:\"\\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB\\u0DBA \\u0DBA\\u0DB1\\u0DD4 \\u0DB8\\u0DD0\\u0DA2\\u0DD2\\u0D9A\\u0DCA \\u0DBA\\u0DC2\\u0DCA\\u0DA7\\u0DD2\\u0DBA\\u0D9A\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DB1 \\u0DB6\\u0DC0 \\u0DAD\\u0DDA\\u0DBB\\u0DD4\\u0DB8\\u0DCA \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DBA\\u0DD2. \\u0D91\\u0DBA \\u0D9A\\u0DCA\\u0DC2\\u0DAB\\u0DD2\\u0D9A\\u0DC0 \\u0DB8\\u0DD4\\u0DAF\\u0DBD\\u0DCA \\u0DB8\\u0DC0\\u0DB1 \\u0D9A\\u0DCA\\u200D\\u0DBB\\u0DB8\\u0DBA\\u0D9A\\u0DCA \\u0DB1\\u0DDC\\u0DC0\\u0DDA.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DA2\\u0DB4 \\u0D9A\\u0DD2\\u0DBB\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0DC3\\u0DD2\\u0DAF\\u0DD4 \\u0DC0\\u0DB1\\u0DCA\\u0DB1\\u0DDA \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DC3\\u0DD2\\u0DAD\\u0DD4\\u0DC0\\u0DD2\\u0DBD\\u0DD2 \\u0DC3\\u0DC4 \\u0D9A\\u0DB8\\u0DCA\\u0DB4\\u0DB1 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA, \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0D9C\\u0DDA \\u0DC3\\u0DC4 \\u0DB0\\u0DB1\\u0D9C\\u0DDA \\u0DC0\\u0DD2\\u0DC1\\u0DCA\\u0DC0 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC4\\u0DCF \\u0D85\\u0DB1\\u0DD4\\u0D9C\\u0DAD \\u0DC0\\u0DD3\\u0DB8\\u0DBA\\u0DD2.\"}),/*#__PURE__*/_jsx(\"p\",{children:\"\\u0D91\\u0DB6\\u0DD0\\u0DC0\\u0DD2\\u0DB1\\u0DCA, \\u0DC1\\u0DCA\\u200D\\u0DBB\\u0DAF\\u0DCA\\u0DB0\\u0DCF\\u0DC0, \\u0DB7\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA \\u0DC3\\u0DC4 \\u0D9A\\u0DD0\\u0DB4\\u0DC0\\u0DD3\\u0DB8 \\u0DBA\\u0DB1 \\u0D9A\\u0DBB\\u0DD4\\u0DAB\\u0DD4 \\u0DAD\\u0DD4\\u0DB1 \\u0DB8\\u0DAD \\u0DB4\\u0DAF\\u0DB1\\u0DB8\\u0DCA\\u0DC0, \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DC0\\u0DD9\\u0DC4\\u0DD9\\u0DC3 \\u0DB8\\u0DC4\\u0DB1\\u0DCA\\u0DC3\\u0DD2 \\u0DC0\\u0DD3 \\u0D9A\\u0DBB\\u0DB1 \\u0D8B\\u0DAD\\u0DCA\\u0DC3\\u0DCF\\u0DC4\\u0DBA\\u0DA7 \\u0DB8\\u0DD9\\u0DB8 \\u0D85\\u0DB0\\u0DCA\\u200D\\u0DBA\\u0DCF\\u0DAD\\u0DCA\\u0DB8\\u0DD2\\u0D9A \\u0DB4\\u0DD4\\u0DC4\\u0DD4\\u0DAB\\u0DD4\\u0DC0\\u0DAF \\u0D91\\u0D9A\\u0DCA \\u0D9A\\u0DBB \\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DD9\\u0DB1\\u0DCA \\u0D94\\u0DB6\\u0DA7 \\u0D94\\u0DB6\\u0D9C\\u0DDA \\u0DB8\\u0DD6\\u0DBD\\u0DD2\\u0D9A \\u0D89\\u0DBD\\u0D9A\\u0DCA\\u0D9A \\u0DC3\\u0DB5\\u0DBD \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8\\u0DDA \\u0DB8\\u0DCF\\u0DC0\\u0DAD \\u0DC0\\u0DD2\\u0DC0\\u0DBB \\u0D9A\\u0DBB\\u0D9C\\u0DAD \\u0DC4\\u0DD0\\u0D9A\\u0DD2\\u0DBA.\"})]})})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"kubera-footer\",children:/*#__PURE__*/_jsx(\"div\",{className:\"divine-blessing\",children:/*#__PURE__*/_jsx(\"span\",{className:\"blessing-text\",children:\"\\uD83D\\uDE4F \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD3\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0D94\\u0DB6 \\u0DC3\\u0DB8\\u0D9F \\u0DC0\\u0DDA\\u0DC0\\u0DCF \\uD83D\\uDE4F\"})})})]});};export default LandingPage;", "map": {"version": 3, "names": ["React", "useEffect", "ParticleBackground", "KuberaAnimation", "KuberaCardSection", "useAnalytics", "useComponentTracking", "jsx", "_jsx", "jsxs", "_jsxs", "LandingPage", "analytics", "trackEvent", "event_category", "page_type", "content_type", "cards", "document", "querySelectorAll", "for<PERSON>ach", "card", "index", "style", "animationDelay", "concat", "classList", "add", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "card_index", "card_delay", "unobserve", "target", "threshold", "observe", "className", "children"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/LandingPage.js"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport ParticleBackground from './ParticleBackground';\nimport KuberaAnimation from './KuberaAnimation';\nimport KuberaCardSection from './KuberaCardSection';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\n\nconst LandingPage = () => {\n  const analytics = useAnalytics();\n\n  // Track component mounting\n  useComponentTracking('LandingPage');\n\n  useEffect(() => {\n    // Track landing page view with additional context\n    analytics.trackEvent('landing_page_loaded', {\n      event_category: 'page_interaction',\n      page_type: 'landing',\n      content_type: 'kubera_guide'\n    });\n\n    // Add floating animation to content cards with staggered delay\n    const cards = document.querySelectorAll('.kubera-content-card');\n    cards.forEach((card, index) => {\n      card.style.animationDelay = `${index * 0.2}s`;\n      card.classList.add('floating');\n\n      // Track card visibility\n      const observer = new IntersectionObserver((entries) => {\n        entries.forEach(entry => {\n          if (entry.isIntersecting) {\n            analytics.trackEvent('content_card_viewed', {\n              event_category: 'content_engagement',\n              card_index: index,\n              card_delay: index * 0.2\n            });\n            observer.unobserve(entry.target);\n          }\n        });\n      }, { threshold: 0.5 });\n\n      observer.observe(card);\n    });\n  }, [analytics]);\n\n  return (\n    <div className=\"landing-page kubera-guide-page\">\n      <ParticleBackground />\n      <KuberaAnimation />\n\n      {/* Header Section */}\n      <div className=\"landing-header\">\n        <h1 className=\"main-title\">කුබේර මන්ත්‍රේ බලය</h1>\n        <h2 className=\"subtitle\">ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීමේ සම්පූර්ණ මාර්ගෝපදේශය</h2>\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය සමඟ 🙏</span>\n        </div>\n      </div>\n\n      {/* Kubera Card Product Section */}\n      <KuberaCardSection />\n\n\n\n      {/* Introduction Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">කුබේර මන්ත්‍රයේ වැදගත්කම</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <p>\n              සෑම පුද්ගලයකුගේම ජීවිතේ මූලික ස්ථාවරත්වය සහ සෞභාග්‍යය යනු ඉතා වැදගත්\n              අංගයකි. වෛදික සහ හින්දු සම්ප්‍රදායන්ට අනුව, විශ්වේ ඇති ධනයට අධිපති සහ එහි\n              භාරකරු වන්නේ කුබේර දෙවියන්ය.\n            </p>\n            <p>\n              එතුමන්ගේ ආශීර්වාදය ලබා ගැනීමෙන් ධන සම්පත්, සමෘද්ධිය සහ ජීවිතේ මූලික\n              බාධක ඉවත් කරගත හැකි බවට ගැඹුරු විශ්වාසයක් පවතී.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Who is Kubera Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">කවුද මේ කුබේර දෙවියන්?</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <p>\n              හින්දු දේව පුරාණයට අනුව, කුබේර යනු යක්ෂයන්ට අධිපති, දෙවියන්ගේ භාණ්ඩාගාරික\n              සහ උතුරු දිශාවේ ආරක්ෂකයා (දික්පාල) ලෙස සැලකේ.\n            </p>\n            <p>\n              ශ්‍රී ලාංකික අපට කුබේර යනු වඩාත් සමීප චරිතයකි, මන්ද එතුමන් ලංකාපුරේ රාවණ\n              රජුගේ අර්ධ සහෝදරයා ලෙසද සැලකෙන බැවිනි. බෞද්ධ සම්ප්‍රදායේදී එතුමන්\n              \"වෛශ්‍රවණ\" (වෙසමුණි) ලෙස හඳුන්වනු ලබන අතර, සතරවරම් දෙවිවරුන්ගෙන් කෙනෙකු\n              ලෙස උතුරු දිශාවට අධිපතිත්වය දරයි.\n            </p>\n            <p>\n              කුබේර යනු කෑදර ලෙස ධනය රැස් කරන්නෙකු නොව, ධාර්මික ව උපයන ලද ධනය\n              ලෝකයට බෙදාහරින පාලකයෙකි.\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* The Mantra Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card mantra-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">බලගතු කුබේර මන්ත්‍රය සහ එහි තේරුම</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"mantra-section\">\n              <h4 className=\"mantra-subtitle\">මන්ත්‍රය (සංස්කෘත):</h4>\n              <div className=\"sanskrit-mantra\">\n                ॐ यक्षाय कुबेराय वैश्रवणाय धनधान्याधिपतये<br/>\n                धनधान्यसमृद्धिं मे देहि दापय स्वाहा ॥\n              </div>\n\n              <h4 className=\"mantra-subtitle\">උච්චාරණය සඳහා (සිංහල):</h4>\n              <div className=\"sinhala-pronunciation\">\n                ඕම් යක්ෂාය කුබේරාය වෛශ්‍රවණාය ධනධාන්‍යාධිපතයේ,<br/>\n                ධනධාන්‍ය සමෘද්ධිම් මේ දේහි දාපය ස්වාහා ॥\n              </div>\n\n              <h4 className=\"mantra-subtitle\">මන්ත්‍රේ සරල අර්ථය:</h4>\n              <div className=\"mantra-meaning\">\n                \"ඕම්, යක්ෂයන්ගේ රජු වූත්, ධනයට සහ ධාන්‍යයට අධිපති වූත්, වෛශ්‍රවණ ලෙසද\n                හැඳින්වෙන කුබේර දෙවියනි, ඔබ වහන්සේට මම නමස්කාර කරමි. කරුණාකර මට\n                ධනයෙන් සහ ධාන්‍යයෙන් සමෘද්ධිය ලබා දෙනු මැනව.\"\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* How to Use Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">මන්ත්‍රය නිවැරදිව භාවිත කරන ආකාරය</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"usage-guidelines\">\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">1. සුදුසුම වේලාව:</h4>\n                <p>\n                  දිනපතා උදෑසන ස්නානය කිරීමෙන් අනතුරුව පිරිසිදුව හෝ සන්ධ්‍යා කාලේදී\n                  මන්ත්‍රය ජප කිරීම වඩාත් සුදුසුය. විශේෂයෙන් බ්‍රහ්ම මුහුර්තය\n                  (අලුයම 4:30 - 5:30 පමණ) ඉතා බලගතුය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">2. ස්ථානය:</h4>\n                <p>\n                  නිවසේ පිරිසිදු, නිස්කලංක ස්ථානයක් තෝරාගන්න. හැකි නම් පූජාසනයක්\n                  සකසා කුබේර දෙවියන්ගේ රූපයක් හෝ යන්ත්‍රයක් තබා ගැනීමෙන් මනස\n                  ඒකාග්‍ර කරගැනීම පහසුය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">3. පිරිසිදුකම:</h4>\n                <p>\n                  ශාරීරික පිරිසිදුකම මෙන්ම මානසික පිරිසිදුකමද අතිවශ්‍යය. කිසිදු\n                  ක්‍රෝධයක්, වෛරයක් හෝ නිෂේධාත්මක සිතුවිල්ලක් සිතේ තබා නොගෙන,\n                  සැහැල්ලු මනසකින් මන්ත්‍ර ජප කිරීම ආරම්භ කරන්න.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">4. දිශාව:</h4>\n                <p>\n                  කුබේර දෙවියන් උතුරු දිශාවට අධිපති නිසා, මන්ත්‍රය ජප කරන විට උතුරු\n                  දිශාවට මුහුණලා වාඩි වීම ඉතා යෝග්‍යය.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">5. ජප කරන වාර ගණන:</h4>\n                <p>\n                  ස්ඵටික, රුද්‍රාක්ෂ හෝ තුල්සි (මදුරුතලා) ඇටවලින් සැදූ ජපමාලයක්\n                  භාවිත කර 108 වතාවක් මන්ත්‍රය ජප කිරීම සම්ප්‍රදායයි. ආරම්භේදී\n                  ඔබට හැකි වාර ගණනක් (උදා: 9, 27, 54) ජප කර ක්‍රමයෙන් 108 දක්වා\n                  වැඩි කරගත හැක.\n                </p>\n              </div>\n\n              <div className=\"guideline-item\">\n                <h4 className=\"guideline-title\">6. පූජාව:</h4>\n                <p>\n                  මන්ත්‍රය ජප කිරීමට පෙර කුබේර දෙවියන් උදෙසා පහනක් දැල්වීම, සුවඳ\n                  හඳුන්කූරක් පත්තු කිරීම සහ නැවුම් මල් කිහිපයක් පූජා කිරීමෙන් ඔබගේ\n                  භක්තිය සහ ශ්‍රද්ධාව ප්‍රකාශ කළ හැකිය.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Benefits Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card benefits-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">ප්‍රතිලාභ සහ නිවැරදි මානසික ආකල්පය</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"benefits-list\">\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">💰</div>\n                <div className=\"benefit-content\">\n                  <h4>මූලික බාධක ඉවත් වීම</h4>\n                  <p>\n                    රැකියාවේ, ව්‍යාපාරේ හෝ වෙනත් ආදායම් මාර්ගවල ඇති බාධක ක්‍රමයෙන්\n                    ඉවත් වී යහපත උදාවේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🌟</div>\n                <div className=\"benefit-content\">\n                  <h4>නව ආදායම් මාර්ග විවෘත වීම</h4>\n                  <p>\n                    ධනය ආකර්ෂණය වීමට නව අවස්ථා සහ මාර්ග විවෘතවේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🏦</div>\n                <div className=\"benefit-content\">\n                  <h4>ණය බරින් මිදීම</h4>\n                  <p>\n                    මූලික ස්ථාවරත්වයක් ඇතිවීම නිසා ණයතුරුස් වලින් නිදහස් වීමට මග පෑදේ.\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"benefit-item\">\n                <div className=\"benefit-icon\">🛡️</div>\n                <div className=\"benefit-content\">\n                  <h4>ධනය සුරක්ෂිත වීම</h4>\n                  <p>\n                    උපයන ලද ධනය අනවශ්‍ය ලෙස වියදම් නොවී ඉතිරි කරගැනීමට සහ\n                    වර්ධනය කරගැනීමට අවශ්‍ය ශක්තිය ලැබේ.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Important Notes Section */}\n      <div className=\"kubera-content-section\">\n        <div className=\"kubera-content-card dark-glass-card important-notes-card\">\n          <div className=\"card-glow\"></div>\n          <div className=\"card-shine\"></div>\n\n          <div className=\"content-header\">\n            <h3 className=\"content-title\">වැදගත්ම දේ</h3>\n          </div>\n\n          <div className=\"content-body\">\n            <div className=\"important-note\">\n              <p>\n                කුබේර මන්ත්‍රය යනු මැජික් යෂ්ටියක් නොවන බව තේරුම් ගැනීමයි.\n                එය ක්ෂණිකව මුදල් මවන ක්‍රමයක් නොවේ.\n              </p>\n              <p>\n                මන්ත්‍ර ජප කිරීමෙන් සිදු වන්නේ ඔබගේ සිතුවිලි සහ කම්පන ශක්තිය,\n                සමෘද්ධිගේ සහ ධනගේ විශ්ව ශක්තිය හා අනුගත වීමයි.\n              </p>\n              <p>\n                එබැවින්, ශ්‍රද්ධාව, භක්තිය සහ කැපවීම යන කරුණු තුන මත පදනම්ව,\n                ඔබගේ වෙහෙස මහන්සි වී කරන උත්සාහයට මෙම අධ්‍යාත්මික පුහුණුවද\n                එක් කර ගැනීමෙන් ඔබට ඔබගේ මූලික ඉලක්ක සඵල කරගැනීමේ මාවත\n                විවර කරගත හැකිය.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Footer with Blessing */}\n      <div className=\"kubera-footer\">\n        <div className=\"divine-blessing\">\n          <span className=\"blessing-text\">🙏 කුබේර දෙවියන්ගේ ආශීර්වාදය ඔබ සමඟ වේවා 🙏</span>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LandingPage;"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,MAAO,CAAAC,kBAAkB,KAAM,sBAAsB,CACrD,MAAO,CAAAC,eAAe,KAAM,mBAAmB,CAC/C,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CACnD,OAASC,YAAY,CAAEC,oBAAoB,KAAQ,uBAAuB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE3E,KAAM,CAAAC,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,SAAS,CAAGP,YAAY,CAAC,CAAC,CAEhC;AACAC,oBAAoB,CAAC,aAAa,CAAC,CAEnCL,SAAS,CAAC,IAAM,CACd;AACAW,SAAS,CAACC,UAAU,CAAC,qBAAqB,CAAE,CAC1CC,cAAc,CAAE,kBAAkB,CAClCC,SAAS,CAAE,SAAS,CACpBC,YAAY,CAAE,cAChB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,KAAK,CAAGC,QAAQ,CAACC,gBAAgB,CAAC,sBAAsB,CAAC,CAC/DF,KAAK,CAACG,OAAO,CAAC,CAACC,IAAI,CAAEC,KAAK,GAAK,CAC7BD,IAAI,CAACE,KAAK,CAACC,cAAc,IAAAC,MAAA,CAAMH,KAAK,CAAG,GAAG,KAAG,CAC7CD,IAAI,CAACK,SAAS,CAACC,GAAG,CAAC,UAAU,CAAC,CAE9B;AACA,KAAM,CAAAC,QAAQ,CAAG,GAAI,CAAAC,oBAAoB,CAAEC,OAAO,EAAK,CACrDA,OAAO,CAACV,OAAO,CAACW,KAAK,EAAI,CACvB,GAAIA,KAAK,CAACC,cAAc,CAAE,CACxBpB,SAAS,CAACC,UAAU,CAAC,qBAAqB,CAAE,CAC1CC,cAAc,CAAE,oBAAoB,CACpCmB,UAAU,CAAEX,KAAK,CACjBY,UAAU,CAAEZ,KAAK,CAAG,GACtB,CAAC,CAAC,CACFM,QAAQ,CAACO,SAAS,CAACJ,KAAK,CAACK,MAAM,CAAC,CAClC,CACF,CAAC,CAAC,CACJ,CAAC,CAAE,CAAEC,SAAS,CAAE,GAAI,CAAC,CAAC,CAEtBT,QAAQ,CAACU,OAAO,CAACjB,IAAI,CAAC,CACxB,CAAC,CAAC,CACJ,CAAC,CAAE,CAACT,SAAS,CAAC,CAAC,CAEf,mBACEF,KAAA,QAAK6B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ChC,IAAA,CAACN,kBAAkB,GAAE,CAAC,cACtBM,IAAA,CAACL,eAAe,GAAE,CAAC,cAGnBO,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,YAAY,CAAAC,QAAA,CAAC,oGAAkB,CAAI,CAAC,cAClDhC,IAAA,OAAI+B,SAAS,CAAC,UAAU,CAAAC,QAAA,CAAC,kSAAqD,CAAI,CAAC,cACnFhC,IAAA,QAAK+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BhC,IAAA,SAAM+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2LAAmC,CAAM,CAAC,CACvE,CAAC,EACH,CAAC,cAGNhC,IAAA,CAACJ,iBAAiB,GAAE,CAAC,cAKrBI,IAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,KAAA,QAAK6B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC/B,IAAA,QAAK+B,SAAS,CAAC,YAAY,CAAM,CAAC,cAElC/B,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,wIAAwB,CAAI,CAAC,CACxD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,IAAA,MAAAgC,QAAA,CAAG,w3BAIH,CAAG,CAAC,cACJhC,IAAA,MAAAgC,QAAA,CAAG,qlBAGH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,KAAA,QAAK6B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC/B,IAAA,QAAK+B,SAAS,CAAC,YAAY,CAAM,CAAC,cAElC/B,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,kHAAsB,CAAI,CAAC,CACtD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,IAAA,MAAAgC,QAAA,CAAG,mmBAGH,CAAG,CAAC,cACJhC,IAAA,MAAAgC,QAAA,CAAG,0uCAKH,CAAG,CAAC,cACJhC,IAAA,MAAAgC,QAAA,CAAG,6bAGH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,KAAA,QAAK6B,SAAS,CAAC,iDAAiD,CAAAC,QAAA,eAC9DhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC/B,IAAA,QAAK+B,SAAS,CAAC,YAAY,CAAM,CAAC,cAElC/B,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,+KAAiC,CAAI,CAAC,CACjE,CAAC,cAENhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,gGAAmB,CAAI,CAAC,cACxD9B,KAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,EAAC,oOACU,cAAAhC,IAAA,QAAI,CAAC,wMAEhD,EAAK,CAAC,cAENA,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,6GAAsB,CAAI,CAAC,cAC3D9B,KAAA,QAAK6B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EAAC,6PACS,cAAAhC,IAAA,QAAI,CAAC,qNAErD,EAAK,CAAC,cAENA,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,qGAAmB,CAAI,CAAC,cACxDhC,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,CAAC,u4BAIhC,CAAK,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,KAAA,QAAK6B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClDhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC/B,IAAA,QAAK+B,SAAS,CAAC,YAAY,CAAM,CAAC,cAElC/B,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oLAAiC,CAAI,CAAC,CACjE,CAAC,cAENhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B9B,KAAA,QAAK6B,SAAS,CAAC,kBAAkB,CAAAC,QAAA,eAC/B9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,+EAAiB,CAAI,CAAC,cACtDhC,IAAA,MAAAgC,QAAA,CAAG,oxBAIH,CAAG,CAAC,EACD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,0CAAU,CAAI,CAAC,cAC/ChC,IAAA,MAAAgC,QAAA,CAAG,ovBAIH,CAAG,CAAC,EACD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,kEAAc,CAAI,CAAC,cACnDhC,IAAA,MAAAgC,QAAA,CAAG,q3BAIH,CAAG,CAAC,EACD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,oCAAS,CAAI,CAAC,cAC9ChC,IAAA,MAAAgC,QAAA,CAAG,4gBAGH,CAAG,CAAC,EACD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,2EAAkB,CAAI,CAAC,cACvDhC,IAAA,MAAAgC,QAAA,CAAG,m6BAKH,CAAG,CAAC,EACD,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,OAAI+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,CAAC,oCAAS,CAAI,CAAC,cAC9ChC,IAAA,MAAAgC,QAAA,CAAG,y1BAIH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,KAAA,QAAK6B,SAAS,CAAC,mDAAmD,CAAAC,QAAA,eAChEhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC/B,IAAA,QAAK+B,SAAS,CAAC,YAAY,CAAM,CAAC,cAElC/B,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,0LAAkC,CAAI,CAAC,CAClE,CAAC,cAENhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B9B,KAAA,QAAK6B,SAAS,CAAC,eAAe,CAAAC,QAAA,eAC5B9B,KAAA,QAAK6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC9B,KAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhC,IAAA,OAAAgC,QAAA,CAAI,qGAAmB,CAAI,CAAC,cAC5BhC,IAAA,MAAAgC,QAAA,CAAG,waAGH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC9B,KAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhC,IAAA,OAAAgC,QAAA,CAAI,oIAAyB,CAAI,CAAC,cAClChC,IAAA,MAAAgC,QAAA,CAAG,kOAEH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,cAAE,CAAK,CAAC,cACtC9B,KAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhC,IAAA,OAAAgC,QAAA,CAAI,4EAAc,CAAI,CAAC,cACvBhC,IAAA,MAAAgC,QAAA,CAAG,4VAEH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,cAEN9B,KAAA,QAAK6B,SAAS,CAAC,cAAc,CAAAC,QAAA,eAC3BhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,CAAC,oBAAG,CAAK,CAAC,cACvC9B,KAAA,QAAK6B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,eAC9BhC,IAAA,OAAAgC,QAAA,CAAI,wFAAgB,CAAI,CAAC,cACzBhC,IAAA,MAAAgC,QAAA,CAAG,6cAGH,CAAG,CAAC,EACD,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,wBAAwB,CAAAC,QAAA,cACrC9B,KAAA,QAAK6B,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEhC,IAAA,QAAK+B,SAAS,CAAC,WAAW,CAAM,CAAC,cACjC/B,IAAA,QAAK+B,SAAS,CAAC,YAAY,CAAM,CAAC,cAElC/B,IAAA,QAAK+B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BhC,IAAA,OAAI+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yDAAU,CAAI,CAAC,CAC1C,CAAC,cAENhC,IAAA,QAAK+B,SAAS,CAAC,cAAc,CAAAC,QAAA,cAC3B9B,KAAA,QAAK6B,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BhC,IAAA,MAAAgC,QAAA,CAAG,seAGH,CAAG,CAAC,cACJhC,IAAA,MAAAgC,QAAA,CAAG,2iBAGH,CAAG,CAAC,cACJhC,IAAA,MAAAgC,QAAA,CAAG,68BAKH,CAAG,CAAC,EACD,CAAC,CACH,CAAC,EACH,CAAC,CACH,CAAC,cAGNhC,IAAA,QAAK+B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5BhC,IAAA,QAAK+B,SAAS,CAAC,iBAAiB,CAAAC,QAAA,cAC9BhC,IAAA,SAAM+B,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,iOAA2C,CAAM,CAAC,CAC/E,CAAC,CACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}