{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\contexts\\\\TranslationContext.js\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$();\nimport React, { createContext, useContext, useState, useCallback } from 'react';\nimport TranslationService from '../services/TranslationService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TranslationContext = /*#__PURE__*/createContext();\nexport const useTranslation = () => {\n  _s();\n  const context = useContext(TranslationContext);\n  if (!context) {\n    throw new Error('useTranslation must be used within a TranslationProvider');\n  }\n  return context;\n};\n_s(useTranslation, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nexport const TranslationProvider = ({\n  children\n}) => {\n  _s2();\n  const [currentLanguage, setCurrentLanguage] = useState('sinhala');\n  const [translationCache, setTranslationCache] = useState({});\n  const [isTranslating, setIsTranslating] = useState(false);\n  const languages = {\n    sinhala: {\n      code: 'si',\n      name: 'සිංහල',\n      flag: '🇱🇰',\n      nativeName: 'සිංහල'\n    },\n    english: {\n      code: 'en',\n      name: 'English',\n      flag: '🇺🇸',\n      nativeName: 'English'\n    },\n    tamil: {\n      code: 'ta',\n      name: 'தமிழ்',\n      flag: '🇱🇰',\n      nativeName: 'தமிழ்'\n    }\n  };\n\n  // Generate cache key for translation\n  const getCacheKey = (text, targetLang, sourceContext = '') => {\n    return `${text}_${targetLang}_${sourceContext}`.replace(/\\s+/g, '_').toLowerCase();\n  };\n\n  // Translate text using Gemini API\n  const translateText = useCallback(async (text, targetLanguage, sourceContext = '') => {\n    // If target language is Sinhala (default), return original text\n    if (targetLanguage === 'sinhala' || !text || text.trim() === '') {\n      return text;\n    }\n    const cacheKey = getCacheKey(text, targetLanguage, sourceContext);\n\n    // Check cache first\n    if (translationCache[cacheKey]) {\n      return translationCache[cacheKey];\n    }\n    try {\n      setIsTranslating(true);\n      const translatedText = await TranslationService.translateText(text, targetLanguage, sourceContext);\n\n      // Cache the translation\n      setTranslationCache(prev => ({\n        ...prev,\n        [cacheKey]: translatedText\n      }));\n      return translatedText;\n    } catch (error) {\n      console.error('Translation error:', error);\n      // Return original text if translation fails\n      return text;\n    } finally {\n      setIsTranslating(false);\n    }\n  }, [translationCache]);\n\n  // Translate multiple texts at once\n  const translateMultiple = useCallback(async (textArray, targetLanguage, sourceContext = '') => {\n    if (targetLanguage === 'sinhala') {\n      return textArray;\n    }\n    const translations = await Promise.all(textArray.map(text => translateText(text, targetLanguage, sourceContext)));\n    return translations;\n  }, [translateText]);\n\n  // Change language\n  const changeLanguage = useCallback(languageKey => {\n    if (languages[languageKey]) {\n      setCurrentLanguage(languageKey);\n    }\n  }, []);\n\n  // Get translated text with fallback\n  const t = useCallback(async (text, context = '') => {\n    if (currentLanguage === 'sinhala') {\n      return text;\n    }\n    return await translateText(text, currentLanguage, context);\n  }, [currentLanguage, translateText]);\n  const value = {\n    currentLanguage,\n    languages,\n    changeLanguage,\n    translateText,\n    translateMultiple,\n    t,\n    isTranslating,\n    translationCache\n  };\n  return /*#__PURE__*/_jsxDEV(TranslationContext.Provider, {\n    value: value,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s2(TranslationProvider, \"oJM1y76bqXBSRTh8jyUtcGeLWXs=\");\n_c = TranslationProvider;\nexport default TranslationContext;\nvar _c;\n$RefreshReg$(_c, \"TranslationProvider\");", "map": {"version": 3, "names": ["React", "createContext", "useContext", "useState", "useCallback", "TranslationService", "jsxDEV", "_jsxDEV", "TranslationContext", "useTranslation", "_s", "context", "Error", "TranslationProvider", "children", "_s2", "currentLanguage", "setCurrentLanguage", "translationCache", "setTranslationCache", "isTranslating", "setIsTranslating", "languages", "sinhala", "code", "name", "flag", "nativeName", "english", "tamil", "get<PERSON><PERSON><PERSON><PERSON>", "text", "targetLang", "sourceContext", "replace", "toLowerCase", "translateText", "targetLanguage", "trim", "cache<PERSON>ey", "translatedText", "prev", "error", "console", "translateMultiple", "textArray", "translations", "Promise", "all", "map", "changeLanguage", "languageKey", "t", "value", "Provider", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/contexts/TranslationContext.js"], "sourcesContent": ["import React, { createContext, useContext, useState, useCallback } from 'react';\nimport TranslationService from '../services/TranslationService';\n\nconst TranslationContext = createContext();\n\nexport const useTranslation = () => {\n  const context = useContext(TranslationContext);\n  if (!context) {\n    throw new Error('useTranslation must be used within a TranslationProvider');\n  }\n  return context;\n};\n\nexport const TranslationProvider = ({ children }) => {\n  const [currentLanguage, setCurrentLanguage] = useState('sinhala');\n  const [translationCache, setTranslationCache] = useState({});\n  const [isTranslating, setIsTranslating] = useState(false);\n\n  const languages = {\n    sinhala: {\n      code: 'si',\n      name: 'සිංහල',\n      flag: '🇱🇰',\n      nativeName: 'සිංහල'\n    },\n    english: {\n      code: 'en',\n      name: 'English',\n      flag: '🇺🇸',\n      nativeName: 'English'\n    },\n    tamil: {\n      code: 'ta',\n      name: 'தமிழ்',\n      flag: '🇱🇰',\n      nativeName: 'தமிழ்'\n    }\n  };\n\n  // Generate cache key for translation\n  const getCacheKey = (text, targetLang, sourceContext = '') => {\n    return `${text}_${targetLang}_${sourceContext}`.replace(/\\s+/g, '_').toLowerCase();\n  };\n\n  // Translate text using Gemini API\n  const translateText = useCallback(async (text, targetLanguage, sourceContext = '') => {\n    // If target language is Sinhala (default), return original text\n    if (targetLanguage === 'sinhala' || !text || text.trim() === '') {\n      return text;\n    }\n\n    const cacheKey = getCacheKey(text, targetLanguage, sourceContext);\n    \n    // Check cache first\n    if (translationCache[cacheKey]) {\n      return translationCache[cacheKey];\n    }\n\n    try {\n      setIsTranslating(true);\n      const translatedText = await TranslationService.translateText(text, targetLanguage, sourceContext);\n      \n      // Cache the translation\n      setTranslationCache(prev => ({\n        ...prev,\n        [cacheKey]: translatedText\n      }));\n\n      return translatedText;\n    } catch (error) {\n      console.error('Translation error:', error);\n      // Return original text if translation fails\n      return text;\n    } finally {\n      setIsTranslating(false);\n    }\n  }, [translationCache]);\n\n  // Translate multiple texts at once\n  const translateMultiple = useCallback(async (textArray, targetLanguage, sourceContext = '') => {\n    if (targetLanguage === 'sinhala') {\n      return textArray;\n    }\n\n    const translations = await Promise.all(\n      textArray.map(text => translateText(text, targetLanguage, sourceContext))\n    );\n\n    return translations;\n  }, [translateText]);\n\n  // Change language\n  const changeLanguage = useCallback((languageKey) => {\n    if (languages[languageKey]) {\n      setCurrentLanguage(languageKey);\n    }\n  }, []);\n\n  // Get translated text with fallback\n  const t = useCallback(async (text, context = '') => {\n    if (currentLanguage === 'sinhala') {\n      return text;\n    }\n    return await translateText(text, currentLanguage, context);\n  }, [currentLanguage, translateText]);\n\n  const value = {\n    currentLanguage,\n    languages,\n    changeLanguage,\n    translateText,\n    translateMultiple,\n    t,\n    isTranslating,\n    translationCache\n  };\n\n  return (\n    <TranslationContext.Provider value={value}>\n      {children}\n    </TranslationContext.Provider>\n  );\n};\n\nexport default TranslationContext;\n"], "mappings": ";;;AAAA,OAAOA,KAAK,IAAIC,aAAa,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC/E,OAAOC,kBAAkB,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhE,MAAMC,kBAAkB,gBAAGP,aAAa,CAAC,CAAC;AAE1C,OAAO,MAAMQ,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAMC,OAAO,GAAGT,UAAU,CAACM,kBAAkB,CAAC;EAC9C,IAAI,CAACG,OAAO,EAAE;IACZ,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;EAC7E;EACA,OAAOD,OAAO;AAChB,CAAC;AAACD,EAAA,CANWD,cAAc;AAQ3B,OAAO,MAAMI,mBAAmB,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,GAAA;EACnD,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGd,QAAQ,CAAC,SAAS,CAAC;EACjE,MAAM,CAACe,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGhB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAM,CAACiB,aAAa,EAAEC,gBAAgB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAEzD,MAAMmB,SAAS,GAAG;IAChBC,OAAO,EAAE;MACPC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE;IACd,CAAC;IACDC,OAAO,EAAE;MACPJ,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLL,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,OAAO;MACbC,IAAI,EAAE,MAAM;MACZC,UAAU,EAAE;IACd;EACF,CAAC;;EAED;EACA,MAAMG,WAAW,GAAGA,CAACC,IAAI,EAAEC,UAAU,EAAEC,aAAa,GAAG,EAAE,KAAK;IAC5D,OAAO,GAAGF,IAAI,IAAIC,UAAU,IAAIC,aAAa,EAAE,CAACC,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC;EACpF,CAAC;;EAED;EACA,MAAMC,aAAa,GAAGhC,WAAW,CAAC,OAAO2B,IAAI,EAAEM,cAAc,EAAEJ,aAAa,GAAG,EAAE,KAAK;IACpF;IACA,IAAII,cAAc,KAAK,SAAS,IAAI,CAACN,IAAI,IAAIA,IAAI,CAACO,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC/D,OAAOP,IAAI;IACb;IAEA,MAAMQ,QAAQ,GAAGT,WAAW,CAACC,IAAI,EAAEM,cAAc,EAAEJ,aAAa,CAAC;;IAEjE;IACA,IAAIf,gBAAgB,CAACqB,QAAQ,CAAC,EAAE;MAC9B,OAAOrB,gBAAgB,CAACqB,QAAQ,CAAC;IACnC;IAEA,IAAI;MACFlB,gBAAgB,CAAC,IAAI,CAAC;MACtB,MAAMmB,cAAc,GAAG,MAAMnC,kBAAkB,CAAC+B,aAAa,CAACL,IAAI,EAAEM,cAAc,EAAEJ,aAAa,CAAC;;MAElG;MACAd,mBAAmB,CAACsB,IAAI,KAAK;QAC3B,GAAGA,IAAI;QACP,CAACF,QAAQ,GAAGC;MACd,CAAC,CAAC,CAAC;MAEH,OAAOA,cAAc;IACvB,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC1C;MACA,OAAOX,IAAI;IACb,CAAC,SAAS;MACRV,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE,CAACH,gBAAgB,CAAC,CAAC;;EAEtB;EACA,MAAM0B,iBAAiB,GAAGxC,WAAW,CAAC,OAAOyC,SAAS,EAAER,cAAc,EAAEJ,aAAa,GAAG,EAAE,KAAK;IAC7F,IAAII,cAAc,KAAK,SAAS,EAAE;MAChC,OAAOQ,SAAS;IAClB;IAEA,MAAMC,YAAY,GAAG,MAAMC,OAAO,CAACC,GAAG,CACpCH,SAAS,CAACI,GAAG,CAAClB,IAAI,IAAIK,aAAa,CAACL,IAAI,EAAEM,cAAc,EAAEJ,aAAa,CAAC,CAC1E,CAAC;IAED,OAAOa,YAAY;EACrB,CAAC,EAAE,CAACV,aAAa,CAAC,CAAC;;EAEnB;EACA,MAAMc,cAAc,GAAG9C,WAAW,CAAE+C,WAAW,IAAK;IAClD,IAAI7B,SAAS,CAAC6B,WAAW,CAAC,EAAE;MAC1BlC,kBAAkB,CAACkC,WAAW,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,CAAC,GAAGhD,WAAW,CAAC,OAAO2B,IAAI,EAAEpB,OAAO,GAAG,EAAE,KAAK;IAClD,IAAIK,eAAe,KAAK,SAAS,EAAE;MACjC,OAAOe,IAAI;IACb;IACA,OAAO,MAAMK,aAAa,CAACL,IAAI,EAAEf,eAAe,EAAEL,OAAO,CAAC;EAC5D,CAAC,EAAE,CAACK,eAAe,EAAEoB,aAAa,CAAC,CAAC;EAEpC,MAAMiB,KAAK,GAAG;IACZrC,eAAe;IACfM,SAAS;IACT4B,cAAc;IACdd,aAAa;IACbQ,iBAAiB;IACjBQ,CAAC;IACDhC,aAAa;IACbF;EACF,CAAC;EAED,oBACEX,OAAA,CAACC,kBAAkB,CAAC8C,QAAQ;IAACD,KAAK,EAAEA,KAAM;IAAAvC,QAAA,EACvCA;EAAQ;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACkB,CAAC;AAElC,CAAC;AAAC3C,GAAA,CA7GWF,mBAAmB;AAAA8C,EAAA,GAAnB9C,mBAAmB;AA+GhC,eAAeL,kBAAkB;AAAC,IAAAmD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}