{"ast": null, "code": "import _objectSpread from\"/mnt/c/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React from'react';import{useTranslation}from'../contexts/TranslationContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const TranslationLoader=_ref=>{let{children,showOverlay=false,overlayMessage='Translating content...',className='',style={}}=_ref;const{isTranslating,currentLanguage}=useTranslation();const getLoadingMessage=()=>{switch(currentLanguage){case'english':return'Translating content...';case'tamil':return'உள்ளடக்கத்தை மொழிபெயர்க்கிறது...';case'sinhala':default:return'අන්තර්ගතය පරිවර්තනය කරමින්...';}};if(showOverlay&&isTranslating){return/*#__PURE__*/_jsxs(\"div\",{className:\"translation-loader-container \".concat(className),style:_objectSpread({position:'relative'},style),children:[children,/*#__PURE__*/_jsx(\"div\",{style:{position:'absolute',top:0,left:0,right:0,bottom:0,background:'rgba(20, 25, 40, 0.8)',backdropFilter:'blur(4px)',display:'flex',alignItems:'center',justifyContent:'center',zIndex:999,borderRadius:'15px'},children:/*#__PURE__*/_jsxs(\"div\",{style:{display:'flex',alignItems:'center',gap:'12px',background:'rgba(244, 208, 63, 0.1)',border:'1px solid rgba(244, 208, 63, 0.3)',borderRadius:'12px',padding:'16px 24px',color:'#f4d03f',fontFamily:'Noto Sans Sinhala, sans-serif',fontSize:'1rem',fontWeight:'500'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'20px',height:'20px',border:'2px solid rgba(244, 208, 63, 0.3)',borderTop:'2px solid #f4d03f',borderRadius:'50%',animation:'spin 1s linear infinite'}}),/*#__PURE__*/_jsx(\"span\",{children:overlayMessage||getLoadingMessage()})]})}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n          @keyframes spin {\\n            0% { transform: rotate(0deg); }\\n            100% { transform: rotate(360deg); }\\n          }\\n        \"})]});}return/*#__PURE__*/_jsxs(\"div\",{className:\"translation-loader-container \".concat(className),style:style,children:[children,isTranslating&&/*#__PURE__*/_jsxs(\"div\",{style:{position:'fixed',top:'70px',right:'20px',background:'rgba(244, 208, 63, 0.1)',border:'1px solid rgba(244, 208, 63, 0.3)',borderRadius:'8px',padding:'8px 12px',color:'#f4d03f',fontSize:'0.85rem',fontFamily:'Noto Sans Sinhala, sans-serif',display:'flex',alignItems:'center',gap:'8px',zIndex:1000,backdropFilter:'blur(10px)',boxShadow:'0 4px 15px rgba(244, 208, 63, 0.1)'},children:[/*#__PURE__*/_jsx(\"div\",{style:{width:'12px',height:'12px',border:'1px solid rgba(244, 208, 63, 0.3)',borderTop:'1px solid #f4d03f',borderRadius:'50%',animation:'spin 1s linear infinite'}}),/*#__PURE__*/_jsx(\"span\",{children:getLoadingMessage()})]}),/*#__PURE__*/_jsx(\"style\",{jsx:true,children:\"\\n        @keyframes spin {\\n          0% { transform: rotate(0deg); }\\n          100% { transform: rotate(360deg); }\\n        }\\n      \"})]});};export default TranslationLoader;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "Translation<PERSON><PERSON>der", "_ref", "children", "showOverlay", "overlayMessage", "className", "style", "isTranslating", "currentLanguage", "getLoadingMessage", "concat", "_objectSpread", "position", "top", "left", "right", "bottom", "background", "<PERSON><PERSON>ilter", "display", "alignItems", "justifyContent", "zIndex", "borderRadius", "gap", "border", "padding", "color", "fontFamily", "fontSize", "fontWeight", "width", "height", "borderTop", "animation", "boxShadow"], "sources": ["/mnt/c/Users/<USER>/Desktop/Horoscope/src/components/TranslationLoader.js"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from '../contexts/TranslationContext';\n\nconst TranslationLoader = ({ \n  children, \n  showOverlay = false, \n  overlayMessage = 'Translating content...',\n  className = '',\n  style = {} \n}) => {\n  const { isTranslating, currentLanguage } = useTranslation();\n\n  const getLoadingMessage = () => {\n    switch (currentLanguage) {\n      case 'english':\n        return 'Translating content...';\n      case 'tamil':\n        return 'உள்ளடக்கத்தை மொழிபெயர்க்கிறது...';\n      case 'sinhala':\n      default:\n        return 'අන්තර්ගතය පරිවර්තනය කරමින්...';\n    }\n  };\n\n  if (showOverlay && isTranslating) {\n    return (\n      <div \n        className={`translation-loader-container ${className}`}\n        style={{\n          position: 'relative',\n          ...style\n        }}\n      >\n        {children}\n        \n        {/* Translation Overlay */}\n        <div\n          style={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            background: 'rgba(20, 25, 40, 0.8)',\n            backdropFilter: 'blur(4px)',\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'center',\n            zIndex: 999,\n            borderRadius: '15px'\n          }}\n        >\n          <div\n            style={{\n              display: 'flex',\n              alignItems: 'center',\n              gap: '12px',\n              background: 'rgba(244, 208, 63, 0.1)',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderRadius: '12px',\n              padding: '16px 24px',\n              color: '#f4d03f',\n              fontFamily: 'Noto Sans Sinhala, sans-serif',\n              fontSize: '1rem',\n              fontWeight: '500'\n            }}\n          >\n            <div\n              style={{\n                width: '20px',\n                height: '20px',\n                border: '2px solid rgba(244, 208, 63, 0.3)',\n                borderTop: '2px solid #f4d03f',\n                borderRadius: '50%',\n                animation: 'spin 1s linear infinite'\n              }}\n            />\n            <span>{overlayMessage || getLoadingMessage()}</span>\n          </div>\n        </div>\n\n        {/* CSS Animation */}\n        <style jsx>{`\n          @keyframes spin {\n            0% { transform: rotate(0deg); }\n            100% { transform: rotate(360deg); }\n          }\n        `}</style>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`translation-loader-container ${className}`} style={style}>\n      {children}\n      \n      {/* Subtle loading indicator */}\n      {isTranslating && (\n        <div\n          style={{\n            position: 'fixed',\n            top: '70px',\n            right: '20px',\n            background: 'rgba(244, 208, 63, 0.1)',\n            border: '1px solid rgba(244, 208, 63, 0.3)',\n            borderRadius: '8px',\n            padding: '8px 12px',\n            color: '#f4d03f',\n            fontSize: '0.85rem',\n            fontFamily: 'Noto Sans Sinhala, sans-serif',\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px',\n            zIndex: 1000,\n            backdropFilter: 'blur(10px)',\n            boxShadow: '0 4px 15px rgba(244, 208, 63, 0.1)'\n          }}\n        >\n          <div\n            style={{\n              width: '12px',\n              height: '12px',\n              border: '1px solid rgba(244, 208, 63, 0.3)',\n              borderTop: '1px solid #f4d03f',\n              borderRadius: '50%',\n              animation: 'spin 1s linear infinite'\n            }}\n          />\n          <span>{getLoadingMessage()}</span>\n        </div>\n      )}\n\n      {/* CSS Animation */}\n      <style jsx>{`\n        @keyframes spin {\n          0% { transform: rotate(0deg); }\n          100% { transform: rotate(360deg); }\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default TranslationLoader;\n"], "mappings": "yHAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhE,KAAM,CAAAC,iBAAiB,CAAGC,IAAA,EAMpB,IANqB,CACzBC,QAAQ,CACRC,WAAW,CAAG,KAAK,CACnBC,cAAc,CAAG,wBAAwB,CACzCC,SAAS,CAAG,EAAE,CACdC,KAAK,CAAG,CAAC,CACX,CAAC,CAAAL,IAAA,CACC,KAAM,CAAEM,aAAa,CAAEC,eAAgB,CAAC,CAAGb,cAAc,CAAC,CAAC,CAE3D,KAAM,CAAAc,iBAAiB,CAAGA,CAAA,GAAM,CAC9B,OAAQD,eAAe,EACrB,IAAK,SAAS,CACZ,MAAO,wBAAwB,CACjC,IAAK,OAAO,CACV,MAAO,kCAAkC,CAC3C,IAAK,SAAS,CACd,QACE,MAAO,+BAA+B,CAC1C,CACF,CAAC,CAED,GAAIL,WAAW,EAAII,aAAa,CAAE,CAChC,mBACER,KAAA,QACEM,SAAS,iCAAAK,MAAA,CAAkCL,SAAS,CAAG,CACvDC,KAAK,CAAAK,aAAA,EACHC,QAAQ,CAAE,UAAU,EACjBN,KAAK,CACR,CAAAJ,QAAA,EAEDA,QAAQ,cAGTL,IAAA,QACES,KAAK,CAAE,CACLM,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTC,UAAU,CAAE,uBAAuB,CACnCC,cAAc,CAAE,WAAW,CAC3BC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QAAQ,CACxBC,MAAM,CAAE,GAAG,CACXC,YAAY,CAAE,MAChB,CAAE,CAAArB,QAAA,cAEFH,KAAA,QACEO,KAAK,CAAE,CACLa,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBI,GAAG,CAAE,MAAM,CACXP,UAAU,CAAE,yBAAyB,CACrCQ,MAAM,CAAE,mCAAmC,CAC3CF,YAAY,CAAE,MAAM,CACpBG,OAAO,CAAE,WAAW,CACpBC,KAAK,CAAE,SAAS,CAChBC,UAAU,CAAE,+BAA+B,CAC3CC,QAAQ,CAAE,MAAM,CAChBC,UAAU,CAAE,KACd,CAAE,CAAA5B,QAAA,eAEFL,IAAA,QACES,KAAK,CAAE,CACLyB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdP,MAAM,CAAE,mCAAmC,CAC3CQ,SAAS,CAAE,mBAAmB,CAC9BV,YAAY,CAAE,KAAK,CACnBW,SAAS,CAAE,yBACb,CAAE,CACH,CAAC,cACFrC,IAAA,SAAAK,QAAA,CAAOE,cAAc,EAAIK,iBAAiB,CAAC,CAAC,CAAO,CAAC,EACjD,CAAC,CACH,CAAC,cAGNZ,IAAA,UAAOD,GAAG,MAAAM,QAAA,sJAKD,CAAC,EACP,CAAC,CAEV,CAEA,mBACEH,KAAA,QAAKM,SAAS,iCAAAK,MAAA,CAAkCL,SAAS,CAAG,CAACC,KAAK,CAAEA,KAAM,CAAAJ,QAAA,EACvEA,QAAQ,CAGRK,aAAa,eACZR,KAAA,QACEO,KAAK,CAAE,CACLM,QAAQ,CAAE,OAAO,CACjBC,GAAG,CAAE,MAAM,CACXE,KAAK,CAAE,MAAM,CACbE,UAAU,CAAE,yBAAyB,CACrCQ,MAAM,CAAE,mCAAmC,CAC3CF,YAAY,CAAE,KAAK,CACnBG,OAAO,CAAE,UAAU,CACnBC,KAAK,CAAE,SAAS,CAChBE,QAAQ,CAAE,SAAS,CACnBD,UAAU,CAAE,+BAA+B,CAC3CT,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBI,GAAG,CAAE,KAAK,CACVF,MAAM,CAAE,IAAI,CACZJ,cAAc,CAAE,YAAY,CAC5BiB,SAAS,CAAE,oCACb,CAAE,CAAAjC,QAAA,eAEFL,IAAA,QACES,KAAK,CAAE,CACLyB,KAAK,CAAE,MAAM,CACbC,MAAM,CAAE,MAAM,CACdP,MAAM,CAAE,mCAAmC,CAC3CQ,SAAS,CAAE,mBAAmB,CAC9BV,YAAY,CAAE,KAAK,CACnBW,SAAS,CAAE,yBACb,CAAE,CACH,CAAC,cACFrC,IAAA,SAAAK,QAAA,CAAOO,iBAAiB,CAAC,CAAC,CAAO,CAAC,EAC/B,CACN,cAGDZ,IAAA,UAAOD,GAAG,MAAAM,QAAA,4IAKD,CAAC,EACP,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}