const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Proxy API requests to the backend server
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:5000',
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onProxyReq: (proxyReq, req, res) => {
        // Add headers for better compatibility
        proxyReq.setHeader('X-Forwarded-Host', req.headers.host);
        proxyReq.setHeader('X-Forwarded-Proto', req.protocol);
        console.log('Proxying request:', req.method, req.url, 'to', 'http://localhost:5000' + req.url);
      },
      onError: (err, req, res) => {
        console.error('Proxy error:', err.message);
        res.status(500).json({
          error: 'Proxy error',
          message: 'Unable to connect to backend server',
          details: err.message
        });
      }
    })
  );
};
